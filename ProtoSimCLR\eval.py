import random

import torch
from torch.utils.data import DataLoader
from dataset import DefaultDataset
from models.MetaLearning import ProtoNet
from models.resnet_simclr import ResNetSimCLR
import PIL
from torchvision import transforms


transform = transforms.Compose([
    transforms.Resize((32,32)),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
])
def Eval_datasets(support_set_dir,query_set_dir,k_shots):
    support_dataset = DefaultDataset(root_dir=support_set_dir)
    query_dataset = DefaultDataset(root_dir=query_set_dir)
    class_split = {}
    for img_path, label in zip(support_dataset.image_paths, support_dataset.labels):
        if label not in class_split:
            class_split[label] = []
        class_split[label].append(img_path)
    # 按类别划分数据
    support_x = []
    support_y = []
    query_x = []
    query_y = []

    # 确保模型已经移动到设备
    for class_id in class_split.keys():
        images = class_split[class_id][:k_shots]
        for img_path in images:
            image = PIL.Image.open(img_path).convert('RGB')
            if transform:
                image = transform(image)
            support_x.append(image)
            support_y.append(class_id)
    for img_path,label in zip(query_dataset.image_paths,query_dataset.labels):

        image = PIL.Image.open(img_path).convert('RGB')
        if transform:
            image = transform(image)
        query_x.append(image)
        query_y.append(label)
    # 转换为Tensor
    query_x = query_x[:1000]  # 只取前100个查询样本
    query_y = query_y[:1000]  # 只取前100个查询样本

    support_x = torch.stack(support_x)
    support_y = torch.tensor(support_y, dtype=torch.long)
    query_x = torch.stack(query_x)
    query_y = torch.tensor(query_y, dtype=torch.long)

    return support_x, support_y, query_x, query_y
def validate(model, support_set_dir, query_set_dir, device):

    episodes = Eval_datasets(support_set_dir, query_set_dir,model.k_shot)
    support_x, support_y, query_x, query_y = episodes

    model.eval()
    model.to(device)
    support_x = support_x.to(device)
    query_x = query_x.to(device)
    query_y = query_y.to(device)

    with torch.no_grad():

        # 计算 logits
        logits = model(support_x, query_x)
        # 预测类别
        pred = logits.argmax(dim=1)
        # 计算准确率
        accuracy = (pred == query_y).float().mean().item()

    print(f"Validation Accuracy: {accuracy * 100:.2f}%")
    return accuracy


# 示例调用
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
encoder = ResNetSimCLR(base_model='resnet18', out_dim=128)
model = ProtoNet(encoder=encoder, n_way=5, k_shot=3)
model_dict = torch.load('./checkpoints/checkpoint_epoch_15.pth', map_location=device)
model.load_state_dict(model_dict['model_state_dict'])
support_set_dir = '../data/CTU_5way3shot/train/'
query_set_dir = '../data/CTU_5way3shot/val/'

validate(model, support_set_dir, query_set_dir, device)