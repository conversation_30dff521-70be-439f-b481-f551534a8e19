# -*- coding: utf-8 -*-
"""
训练脚本
"""

import argparse
import sys
import torch
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from config import ImageConfig, SequenceConfig, TrainingConfig
from data import MultimodalDataLoader
from models import ContrastiveModel
from training import Trainer
from utils import setup_logger, FileManager


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='多模态网络入侵检测训练脚本')
    
    # 数据相关参数
    parser.add_argument('--data_dir', type=str, required=True,
                       help='数据目录路径')
    parser.add_argument('--output_dir', type=str, default='./output',
                       help='输出目录路径')
    
    # 模型相关参数
    parser.add_argument('--image_encoder', type=str, default='resnet18',
                       choices=['resnet18', 'resnet50', 'efficientnet', 'vgg16'],
                       help='图像编码器类型')
    parser.add_argument('--sequence_encoder', type=str, default='transformer',
                       choices=['transformer', 'lstm', 'gru', 'tcn'],
                       help='序列编码器类型')
    parser.add_argument('--fusion_strategy', type=str, default='attention',
                       choices=['concat', 'attention', 'gated'],
                       help='融合策略')
    
    # 训练相关参数
    parser.add_argument('--batch_size', type=int, default=32,
                       help='批次大小')
    parser.add_argument('--num_epochs', type=int, default=100,
                       help='训练轮数')
    parser.add_argument('--learning_rate', type=float, default=1e-3,
                       help='学习率')
    parser.add_argument('--temperature', type=float, default=0.07,
                       help='对比学习温度参数')
    
    # 设备相关参数
    parser.add_argument('--device', type=str, default='auto',
                       choices=['cpu', 'cuda', 'auto'],
                       help='训练设备')
    parser.add_argument('--num_workers', type=int, default=4,
                       help='数据加载工作进程数')
    
    # 其他参数
    parser.add_argument('--seed', type=int, default=42,
                       help='随机种子')
    parser.add_argument('--resume', type=str, default=None,
                       help='恢复训练的检查点路径')
    parser.add_argument('--config_file', type=str, default=None,
                       help='配置文件路径')
    
    return parser.parse_args()


def setup_configs(args):
    """设置配置"""
    # 如果提供了配置文件，先加载配置文件
    if args.config_file:
        config_data = FileManager.load_yaml(args.config_file)
        if config_data:
            # 从配置文件创建配置对象
            image_config = ImageConfig.from_dict(config_data.get('image_config', {}))
            sequence_config = SequenceConfig.from_dict(config_data.get('sequence_config', {}))
            training_config = TrainingConfig.from_dict(config_data.get('training_config', {}))
        else:
            # 使用默认配置
            image_config = ImageConfig()
            sequence_config = SequenceConfig()
            training_config = TrainingConfig()
    else:
        # 使用默认配置
        image_config = ImageConfig()
        sequence_config = SequenceConfig()
        training_config = TrainingConfig()
    
    # 使用命令行参数覆盖配置
    image_config.encoder_type = args.image_encoder
    sequence_config.encoder_type = args.sequence_encoder
    training_config.fusion_strategy = args.fusion_strategy
    training_config.batch_size = args.batch_size
    training_config.num_epochs = args.num_epochs
    training_config.learning_rate = args.learning_rate
    training_config.temperature = args.temperature
    training_config.num_workers = args.num_workers
    training_config.seed = args.seed
    
    # 设置设备
    if args.device == 'auto':
        training_config.device = 'cuda' if torch.cuda.is_available() else 'cpu'
    else:
        training_config.device = args.device
    
    # 设置输出目录
    training_config.output_root = args.output_dir
    training_config.log_dir = str(Path(args.output_dir) / 'logs')
    training_config.checkpoint_dir = str(Path(args.output_dir) / 'checkpoints')
    
    return image_config, sequence_config, training_config


def main():
    """主函数"""
    # 解析参数
    args = parse_args()
    
    # 设置配置
    image_config, sequence_config, training_config = setup_configs(args)
    
    # 验证配置
    image_config.validate()
    sequence_config.validate()
    training_config.validate()
    
    # 设置随机种子
    torch.manual_seed(training_config.seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(training_config.seed)
    
    # 设置日志
    logger = setup_logger(
        name='MultimodalNID',
        level=training_config.log_level,
        log_file=str(Path(training_config.log_dir) / 'train.log')
    )
    
    logger.info("开始训练多模态网络入侵检测模型")
    logger.info(f"数据目录: {args.data_dir}")
    logger.info(f"输出目录: {args.output_dir}")
    logger.info(f"设备: {training_config.device}")
    
    # 保存配置
    config_save_path = Path(args.output_dir) / 'configs'
    FileManager.ensure_dir(config_save_path)
    
    FileManager.save_yaml(image_config.to_dict(), config_save_path / 'image_config.yaml')
    FileManager.save_yaml(sequence_config.to_dict(), config_save_path / 'sequence_config.yaml')
    FileManager.save_yaml(training_config.to_dict(), config_save_path / 'training_config.yaml')
    
    try:
        # 创建数据加载器
        logger.info("创建数据加载器...")
        data_loader_manager = MultimodalDataLoader(
            data_dir=args.data_dir,
            image_config=image_config,
            sequence_config=sequence_config,
            training_config=training_config
        )
        
        train_loader = data_loader_manager.get_train_loader()
        val_loader = data_loader_manager.get_val_loader()
        
        # 打印数据集统计信息
        dataset_stats = data_loader_manager.get_dataset_statistics()
        logger.info("数据集统计信息:")
        for split, stats in dataset_stats.items():
            logger.info(f"  {split}: {stats}")
        
        # 创建模型
        logger.info("创建模型...")
        model = ContrastiveModel(
            image_config=image_config,
            sequence_config=sequence_config,
            training_config=training_config
        )
        
        # 打印模型信息
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        logger.info(f"模型总参数数: {total_params:,}")
        logger.info(f"可训练参数数: {trainable_params:,}")
        
        # 创建训练器
        logger.info("创建训练器...")
        trainer = Trainer(
            model=model,
            train_loader=train_loader,
            val_loader=val_loader,
            config=training_config,
            logger=logger
        )
        
        # 恢复训练（如果指定）
        if args.resume:
            logger.info(f"从检查点恢复训练: {args.resume}")
            trainer.load_checkpoint(args.resume)
        
        # 开始训练
        training_results = trainer.train()
        
        # 保存训练结果
        results_path = Path(args.output_dir) / 'training_results.json'
        FileManager.save_json(training_results, results_path)
        
        logger.info("训练完成!")
        logger.info(f"最佳验证分数: {training_results['best_val_score']:.4f}")
        logger.info(f"总训练时间: {training_results['total_time']:.2f}秒")
        
    except Exception as e:
        logger.error(f"训练过程中发生错误: {e}")
        raise


if __name__ == '__main__':
    main()
