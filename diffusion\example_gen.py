import torch
import numpy as np
import matplotlib.pyplot as plt
import os
from PIL import Image
import imageio
from tqdm.auto import tqdm
import torch.nn as nn
import torch.nn.functional as F

# 确保输出目录存在
os.makedirs("./generation_process", exist_ok=True)

# 设备设置
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f'Using device: {device}')


# 重新定义模型和调度器类
class CustomDDPMScheduler:
    def __init__(self, num_train_timesteps=1000, beta_start=0.0001, beta_end=0.02, beta_schedule='linear'):
        self.num_train_timesteps = num_train_timesteps
        self.beta_schedule = beta_schedule

        # 计算beta值
        if beta_schedule == 'linear':
            self.betas = torch.linspace(beta_start, beta_end, num_train_timesteps)
        elif beta_schedule == 'squaredcos_cap_v2':
            steps = num_train_timesteps + 1
            x = torch.linspace(0, num_train_timesteps, steps)
            alphas_cumprod = torch.cos(((x / num_train_timesteps) + 0.008) / 1.008 * torch.pi * 0.5) ** 2
            alphas_cumprod = alphas_cumprod / alphas_cumprod[0]
            betas = 1 - (alphas_cumprod[1:] / alphas_cumprod[:-1])
            self.betas = torch.clip(betas, 0.0001, 0.9999)

        # 计算alpha值
        self.alphas = 1.0 - self.betas
        self.alphas_cumprod = torch.cumprod(self.alphas, dim=0)
        self.alphas_cumprod_prev = F.pad(self.alphas_cumprod[:-1], (1, 0), value=1.0)

        # 计算参数
        self.sqrt_alphas_cumprod = torch.sqrt(self.alphas_cumprod)
        self.sqrt_one_minus_alphas_cumprod = torch.sqrt(1.0 - self.alphas_cumprod)

    def add_noise(self, original_samples, noise, timesteps):
        sqrt_alpha = self.sqrt_alphas_cumprod[timesteps].to(original_samples.device)
        sqrt_one_minus_alpha = self.sqrt_one_minus_alphas_cumprod[timesteps].to(original_samples.device)

        # 调整形状以适应批量处理
        sqrt_alpha = sqrt_alpha.reshape(-1, 1, 1, 1)
        sqrt_one_minus_alpha = sqrt_one_minus_alpha.reshape(-1, 1, 1, 1)

        noisy_samples = sqrt_alpha * original_samples + sqrt_one_minus_alpha * noise
        return noisy_samples

    # def step(self, model_output, timestep, sample):
    #     t = timestep
    #     prev_t = t - 1 if t > 0 else 0
    #     device = sample.device
    #
    #     # 从GPU获取参数
    #     alpha_prod_t = self.alphas_cumprod[t].to(device)
    #     alpha_prod_t_prev = self.alphas_cumprod_prev[prev_t].to(device) if t > 0 else torch.tensor(1.0).to(device)
    #     beta_prod_t = 1 - alpha_prod_t
    #     current_beta_t = self.betas[t].to(device)
    #
    #
    #     # 计算预测的原始样本
    #     pred_original_sample = (sample - torch.sqrt(beta_prod_t) * model_output) / torch.sqrt(alpha_prod_t)
    #
    #     # 计算前一步的均值
    #     pred_sample_direction = torch.sqrt(1 - alpha_prod_t_prev) * model_output
    #     mean = (torch.sqrt(alpha_prod_t_prev) * pred_original_sample + pred_sample_direction)
    #
    #     # 计算方差
    #     variance = (1 - alpha_prod_t_prev) / (1 - alpha_prod_t) * current_beta_t
    #     variance = torch.clamp(variance, min=1e-20)
    #
    #
    #
    #     # 生成新样本
    #     if t > 0:
    #         noise = torch.randn_like(sample)
    #         prev_sample = mean + torch.sqrt(variance) * noise
    #     else:
    #         prev_sample = mean
    #
    #     class StepOutput:
    #         def __init__(self, prev_sample):
    #             self.prev_sample = prev_sample
    #
    #     return StepOutput(prev_sample)
    def step(self, model_output, timestep, sample):
        t = timestep
        prev_t = t - 1 if t > 0 else 0
        device = sample.device

        # 获取参数
        alpha_prod_t = self.alphas_cumprod[t].to(device)
        beta_t = self.betas[t].to(device)
        alpha_t = 1 - beta_t

        # DDPM核心公式
        # 计算均值部分：1/sqrt(α_t) * (x_t - β_t/sqrt(1-ᾱ_t) * ε_θ)
        mean = (sample - beta_t * model_output / torch.sqrt(1 - alpha_prod_t)) / torch.sqrt(alpha_t)
        # DDPM使用固定方差β_t
        variance = beta_t
        # 生成新样本
        if t > 0:
            noise = torch.randn_like(sample)
            prev_sample = mean + torch.sqrt(variance) * noise
        else:
            prev_sample = mean

        class StepOutput:
            def __init__(self, prev_sample):
                self.prev_sample = prev_sample

        return StepOutput(prev_sample)


class SimpleUNetBlock(nn.Module):
    def __init__(self, in_channels, out_channels, time_emb_dim=None):
        super().__init__()
        self.conv1 = nn.Conv2d(in_channels, out_channels, 3, padding=1)
        self.conv2 = nn.Conv2d(out_channels, out_channels, 3, padding=1)
        self.norm1 = nn.BatchNorm2d(out_channels)
        self.norm2 = nn.BatchNorm2d(out_channels)
        self.relu = nn.ReLU()

        if time_emb_dim:
            self.time_mlp = nn.Linear(time_emb_dim, out_channels)

    def forward(self, x, t=None):
        h = self.relu(self.norm1(self.conv1(x)))
        if t is not None:
            t_emb = self.time_mlp(t)
            t_emb = t_emb.view(t_emb.shape[0], t_emb.shape[1], 1, 1)
            h = h + t_emb
        h = self.relu(self.norm2(self.conv2(h)))
        return h


class SimpleDownBlock(nn.Module):
    def __init__(self, in_channels, out_channels, time_emb_dim=None):
        super().__init__()
        self.block = SimpleUNetBlock(in_channels, out_channels, time_emb_dim)
        self.downsample = nn.Conv2d(out_channels, out_channels, 4, stride=2, padding=1)

    def forward(self, x, t=None):
        h = self.block(x, t)
        return h, self.downsample(h)


class SimpleUpBlock(nn.Module):
    def __init__(self, in_channels, out_channels, skip_channels, time_emb_dim=None):
        super().__init__()
        self.upsample = nn.ConvTranspose2d(in_channels, in_channels // 2, 4, stride=2, padding=1)
        self.adjust_skip = nn.Conv2d(skip_channels, out_channels, kernel_size=1)
        self.block = SimpleUNetBlock(in_channels // 2 + out_channels, out_channels, time_emb_dim)

    def forward(self, x, skip=None, t=None):
        x = self.upsample(x)
        if skip is not None:
            skip = self.adjust_skip(skip)
            x = torch.cat([x, skip], dim=1)
        return self.block(x, t)


class SimpleUNet2DModel(nn.Module):
    def __init__(self,
                 sample_size=28,
                 in_channels=1,
                 out_channels=1,
                 block_out_channels=(32, 64, 128)):
        super().__init__()
        self.sample_size = sample_size
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.time_emb_dim = 128

        self.time_mlp = nn.Sequential(
            nn.Linear(self.time_emb_dim, self.time_emb_dim),
            nn.ReLU(),
            nn.Linear(self.time_emb_dim, self.time_emb_dim)
        )

        self.init_conv = nn.Conv2d(in_channels, block_out_channels[0], 3, padding=1)

        self.down_blocks = nn.ModuleList()
        in_chs = block_out_channels[0]
        for out_chs in block_out_channels[1:]:
            self.down_blocks.append(SimpleDownBlock(in_chs, out_chs, self.time_emb_dim))
            in_chs = out_chs

        self.middle_block = SimpleUNetBlock(block_out_channels[-1], block_out_channels[-1], self.time_emb_dim)

        self.up_blocks = nn.ModuleList()
        reversed_chs = list(reversed(block_out_channels))
        for i in range(len(block_out_channels) - 1):
            in_chs = reversed_chs[i]
            out_chs = reversed_chs[i + 1]
            self.up_blocks.append(SimpleUpBlock(in_chs, out_chs, in_chs, self.time_emb_dim))

        self.final_conv = nn.Conv2d(block_out_channels[0], out_channels, 3, padding=1)

    def forward(self, x, t):
        t_emb = self.get_time_embedding(t)
        x = self.init_conv(x)
        skips = [x]

        for down_block in self.down_blocks:
            skip, x = down_block(x, t_emb)
            skips.append(skip)

        x = self.middle_block(x, t_emb)

        for up_block in self.up_blocks:
            skip = skips.pop()
            x = up_block(x, skip, t_emb)

        x = self.final_conv(x)
        return x

    def get_time_embedding(self, timesteps):
        half_dim = self.time_emb_dim // 2
        emb = torch.log(torch.tensor(10000.0)) / (half_dim - 1)
        emb = torch.exp(torch.arange(half_dim, dtype=torch.float32, device=timesteps.device) * -emb)
        emb = timesteps.float().unsqueeze(-1) * emb.unsqueeze(0)
        emb = torch.cat([torch.sin(emb), torch.cos(emb)], dim=-1)
        return self.time_mlp(emb)


class ClassConditionedUnet(nn.Module):
    def __init__(self, num_classes=10, class_emb_size=4):
        super().__init__()
        self.class_emb = nn.Embedding(num_classes, class_emb_size)
        self.model = SimpleUNet2DModel(
            sample_size=28,
            in_channels=1 + class_emb_size,
            out_channels=1,
            block_out_channels=(32, 64, 128)
        )
    def forward(self, x, t, class_labels):
        bs, ch, w, h = x.shape
        class_cond = self.class_emb(class_labels)
        class_cond = class_cond.view(bs, class_cond.shape[1], 1, 1).expand(-1, -1, w, h)
        net_input = torch.cat((x, class_cond), dim=1)
        return self.model(net_input, t)
# 初始化调度器
noise_scheduler = CustomDDPMScheduler(num_train_timesteps=1000, beta_schedule='squaredcos_cap_v2')
noise_scheduler.betas = noise_scheduler.betas.to(device)
noise_scheduler.alphas = noise_scheduler.alphas.to(device)
noise_scheduler.alphas_cumprod = noise_scheduler.alphas_cumprod.to(device)
noise_scheduler.alphas_cumprod_prev = noise_scheduler.alphas_cumprod_prev.to(device)
noise_scheduler.sqrt_alphas_cumprod = noise_scheduler.sqrt_alphas_cumprod.to(device)
noise_scheduler.sqrt_one_minus_alphas_cumprod = noise_scheduler.sqrt_one_minus_alphas_cumprod.to(device)

# 加载预训练模型
net = ClassConditionedUnet().to(device)
net.load_state_dict(torch.load("conditional_diffusion_model.pth", map_location=device))
net.eval()
print("Model loaded successfully")


def generate_and_visualize(num_samples=1, target_labels=None, save_gif=True, sample_interval=20):
    """
    生成样本并可视化去噪过程

    参数:
    num_samples: 生成的样本数量
    target_labels: 目标标签列表，如果为None则随机生成
    save_gif: 是否保存GIF动画
    sample_interval: 采样间隔（时间步）
    """
    # 创建噪声输入
    if target_labels is None:
        target_labels = torch.randint(0, 10, (num_samples,), device=device)
    else:
        target_labels = torch.tensor(target_labels, device=device)

    x = torch.randn(num_samples, 1, 28, 28).to(device)

    # 倒序时间步
    timesteps = list(range(noise_scheduler.num_train_timesteps))[::-1]

    # 存储中间结果用于可视化
    intermediate_images = []

    # 采样循环
    with torch.no_grad():
        for i, t in enumerate(tqdm(timesteps)):
            # 创建时间步张量
            timestep_tensor = torch.full((x.size(0),), t, device=device, dtype=torch.long)

            # 模型预测
            residual = net(x, timestep_tensor, target_labels)

            # 更新样本
            step_output = noise_scheduler.step(residual, t, x)
            x = step_output.prev_sample

            # 裁剪到合理范围
            x = torch.clamp(x, -1, 1)

            # 每隔一定步数保存中间结果
            if i % sample_interval == 0 or i == len(timesteps) - 1:
                # 反归一化并转换为图像
                img = (x[0, 0].detach().cpu().numpy() + 1) / 2  # [0, 1]
                intermediate_images.append(img)

                # 保存当前步的图像
                plt.figure(figsize=(4, 4))
                plt.imshow(img, cmap='gray', vmin=0, vmax=1)
                plt.title(f"t={t}, Label={target_labels[0].item()}")
                plt.axis('off')
                plt.savefig(f"./generation_process/step_{t:04d}.png", bbox_inches='tight', pad_inches=0)
                plt.close()
    # 最终处理
    x = (x + 1) / 2  # 转换到[0, 1]
    x = x.detach().cpu()
    # 显示最终结果
    plt.figure(figsize=(10, 4))
    for i in range(num_samples):
        plt.subplot(1, num_samples, i + 1)
        plt.imshow(x[i, 0], cmap='gray', vmin=0, vmax=1)
        plt.title(f"Label: {target_labels[i].item()}")
        plt.axis('off')
    plt.tight_layout()
    plt.savefig("final_generated_samples.png", bbox_inches='tight', pad_inches=0)
    plt.show()
    # 创建动态可视化
    if save_gif and len(intermediate_images) > 0:
        # 创建图像列表
        images = []
        for img in intermediate_images:
            # 转换为0-255范围的uint8
            img_uint8 = (img * 255).astype(np.uint8)
            # 确保是二维数组（灰度图）
            if len(img_uint8.shape) == 2:
                # 添加通道维度以兼容imageio
                img_uint8 = np.stack([img_uint8] * 3, axis=-1)
            images.append(img_uint8)
        # 保存GIF
        imageio.mimsave('denoising_process.gif', images, fps=10)
        print("GIF saved as denoising_process.gif")

    else:
        return None
# 示例使用
# 生成数字9的去噪过程
animation = generate_and_visualize(num_samples=10, target_labels=[0,1,2,3,4,5,6,7,8,9], save_gif=True)

