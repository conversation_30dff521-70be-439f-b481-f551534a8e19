# -*- coding: utf-8 -*-
"""
文件处理工具模块
"""

import os
import json
import pickle
import shutil
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
import yaml


class FileManager:
    """文件管理器"""
    
    @staticmethod
    def ensure_dir(path: Union[str, Path]) -> Path:
        """
        确保目录存在
        
        Args:
            path: 目录路径
            
        Returns:
            Path对象
        """
        path = Path(path)
        path.mkdir(parents=True, exist_ok=True)
        return path
    
    @staticmethod
    def save_json(data: Dict[str, Any], file_path: Union[str, Path]) -> bool:
        """
        保存JSON文件
        
        Args:
            data: 要保存的数据
            file_path: 文件路径
            
        Returns:
            是否保存成功
        """
        try:
            file_path = Path(file_path)
            FileManager.ensure_dir(file_path.parent)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            return True
        except Exception as e:
            print(f"保存JSON文件失败: {e}")
            return False
    
    @staticmethod
    def load_json(file_path: Union[str, Path]) -> Optional[Dict[str, Any]]:
        """
        加载JSON文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            加载的数据或None
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载JSON文件失败: {e}")
            return None
    
    @staticmethod
    def save_yaml(data: Dict[str, Any], file_path: Union[str, Path]) -> bool:
        """
        保存YAML文件
        
        Args:
            data: 要保存的数据
            file_path: 文件路径
            
        Returns:
            是否保存成功
        """
        try:
            file_path = Path(file_path)
            FileManager.ensure_dir(file_path.parent)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump(data, f, default_flow_style=False, allow_unicode=True)
            
            return True
        except Exception as e:
            print(f"保存YAML文件失败: {e}")
            return False
    
    @staticmethod
    def load_yaml(file_path: Union[str, Path]) -> Optional[Dict[str, Any]]:
        """
        加载YAML文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            加载的数据或None
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"加载YAML文件失败: {e}")
            return None
    
    @staticmethod
    def save_pickle(data: Any, file_path: Union[str, Path]) -> bool:
        """
        保存Pickle文件
        
        Args:
            data: 要保存的数据
            file_path: 文件路径
            
        Returns:
            是否保存成功
        """
        try:
            file_path = Path(file_path)
            FileManager.ensure_dir(file_path.parent)
            
            with open(file_path, 'wb') as f:
                pickle.dump(data, f)
            
            return True
        except Exception as e:
            print(f"保存Pickle文件失败: {e}")
            return False
    
    @staticmethod
    def load_pickle(file_path: Union[str, Path]) -> Optional[Any]:
        """
        加载Pickle文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            加载的数据或None
        """
        try:
            with open(file_path, 'rb') as f:
                return pickle.load(f)
        except Exception as e:
            print(f"加载Pickle文件失败: {e}")
            return None
    
    @staticmethod
    def copy_file(src: Union[str, Path], dst: Union[str, Path]) -> bool:
        """
        复制文件
        
        Args:
            src: 源文件路径
            dst: 目标文件路径
            
        Returns:
            是否复制成功
        """
        try:
            src = Path(src)
            dst = Path(dst)
            
            FileManager.ensure_dir(dst.parent)
            shutil.copy2(src, dst)
            
            return True
        except Exception as e:
            print(f"复制文件失败: {e}")
            return False
    
    @staticmethod
    def move_file(src: Union[str, Path], dst: Union[str, Path]) -> bool:
        """
        移动文件
        
        Args:
            src: 源文件路径
            dst: 目标文件路径
            
        Returns:
            是否移动成功
        """
        try:
            src = Path(src)
            dst = Path(dst)
            
            FileManager.ensure_dir(dst.parent)
            shutil.move(str(src), str(dst))
            
            return True
        except Exception as e:
            print(f"移动文件失败: {e}")
            return False
    
    @staticmethod
    def delete_file(file_path: Union[str, Path]) -> bool:
        """
        删除文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否删除成功
        """
        try:
            file_path = Path(file_path)
            if file_path.exists():
                file_path.unlink()
            return True
        except Exception as e:
            print(f"删除文件失败: {e}")
            return False
    
    @staticmethod
    def list_files(directory: Union[str, Path], 
                  pattern: str = "*",
                  recursive: bool = False) -> List[Path]:
        """
        列出目录中的文件
        
        Args:
            directory: 目录路径
            pattern: 文件模式
            recursive: 是否递归搜索
            
        Returns:
            文件路径列表
        """
        directory = Path(directory)
        
        if not directory.exists():
            return []
        
        if recursive:
            return list(directory.rglob(pattern))
        else:
            return list(directory.glob(pattern))
    
    @staticmethod
    def get_file_size(file_path: Union[str, Path]) -> int:
        """
        获取文件大小
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件大小（字节）
        """
        try:
            return Path(file_path).stat().st_size
        except Exception:
            return 0
    
    @staticmethod
    def clean_directory(directory: Union[str, Path], 
                       keep_patterns: Optional[List[str]] = None) -> bool:
        """
        清理目录
        
        Args:
            directory: 目录路径
            keep_patterns: 要保留的文件模式列表
            
        Returns:
            是否清理成功
        """
        try:
            directory = Path(directory)
            
            if not directory.exists():
                return True
            
            keep_patterns = keep_patterns or []
            
            for item in directory.iterdir():
                should_keep = False
                
                for pattern in keep_patterns:
                    if item.match(pattern):
                        should_keep = True
                        break
                
                if not should_keep:
                    if item.is_file():
                        item.unlink()
                    elif item.is_dir():
                        shutil.rmtree(item)
            
            return True
        except Exception as e:
            print(f"清理目录失败: {e}")
            return False
