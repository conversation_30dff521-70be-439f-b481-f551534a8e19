   # Few-Shot-NID-with-Contrastive-Learning
# 当前研究思路
# 整体分为两个stage，对于stage1使用增量对比损失来训练一个NID的预训练模型，对于stage2，使用Few-shot learning对于模型进行一个迁移学习。
## 对于stage1，具体方法参考以下几篇论文：
### 1、PROTOTYPICAL CONTRASTIVE LEARNING OF  UNSUPERVISED REPRESENTATIONS（ICLR2021）（有点意思的文章，效果比simclr好很多，也有一定的可解释性）
这篇论文的思想是最开始对比学习存在大量的假负例样本，（不论两个数据的语义相似度量多么靠近，对比学习会认为其是一个正例一个负例，这样会导致正例被错误的推远了（本不应该把这种潜在正例推开的）
该文的思路是使用 proto-type来代替对比学习样本，简单来说就是先聚类，再contrastive learning，将PCL将原先InfoNce中的损失函数略微修改，对于InfoNce中的实例改为聚类原型，对于温度系数改为原型的浓度
对于用浓度系数替代原先的温度系数T，松散集群（较大的φ）中的相似性被缩小，使嵌入更接近原型。相反，紧密集群（较小的 φ）中的嵌入具有放大的相似性，因此不太鼓励接近原型。
框架是EM框架，E步：根据上一轮动量编码的输出结果计算距离进行k-means聚类，计算出原型c和浓度ϕ。M步：将计算出的原型c和浓度ϕ带入到下一轮loss计算中并进行更新。
### 2、Representation Learning with Contrastive Predictive Coding：主要提供了在不同模态来进行对比学习的一种方法，
先用一个编码器网络将原始数据映射到隐空间中，对于latent feature输入给自回归模型进行上下文建模，取出t时刻的上下文模型用于预测t+k时刻的状态
对于损失函数使用InfoNCE损失，其中，正例采用第t+k时刻的latent feature，负例使用随机采样的方法，采集n个负样本，用于contrastive learning。
### 3、Momentum Contrast for Unsupervised Visual Representation Learning（MoCo）
动量编码器，将对比学习视作字典查询任务，对于q编码器和k编码器，如果使用end2end的方法每一轮都对qk编码器进行一个反向传播，算力浪费过大，所以MoCo设计对于k编码器使用momentum的方式缓慢更新
最终的实验效果证明MoCo的效果能够趋于end2end的效果，甚至更优于end2end。

”对比学习是一种在高维连续输入（如图片）中建立离散字典的方法，字典是动态的，键值是随机采样得到的，并且key encoder在训练中进行更新。假设好的特征可以通过包含大量negative样本的字典中学习而来，并且key encoder能够在更新中尽可能保持一致，基于这种思想作者提出了MoCo算法。“
### 4、INCREMENTAL FALSE NEGATIVE DETECTION  FOR CONTRASTIVE LEARNING（ICLR2022）
用一种增量分配伪标签的方式来解决对比学习早期假负样本的问题，对于早期只对于置信度高的聚类结果使用伪标签训练，其他结果还是基于单个样本来训练。
这里的置信度可以理解为，聚类中的样本点距离该聚类中心的距离越小，距离其他聚类中心的距离越大，则置信度越高
## 对于stage2，具体方法参考以下几篇论文：
### 1、Few-Shot Classification with Contrastive  Learning（ECCV2022）
### 2、MolFeSCue: enhancing molecular property prediction in data-limited and imbalanced contexts using few-shot and contrastive learning（Bioinformatics 2024）
这篇论文为了解决分子性质预测中普遍存在的数据稀缺和类别不平衡的挑战，作者提出的框架MolFeSCue将小样本对比学习的优势与先进的大规模预训练模型相结合。MolFeSCue的主干是由大规模的预训练模型构成的。MolFeSCue结合基于序列和基于图的预训练模型，因为它们在捕获潜在分子模式方面具有独特和互补的优势。基于序列的预训练分子模型类似于NLP中基于Transformer的模型。它们有效地捕获了分子数据中固有的序列特征。MolFeSCue框架的训练过程使用了一个加和损失函数，该函数包括两个部分：与分子性质相关的监督损失和旨在强调正负样本之间差异的对比损失。其中，监督损失分量表示为预测和实际值标签之间的交叉熵损失。
在分子性质预测的背景下，分子性质的复杂性和像活性悬崖这样的建模挑战，往往会导致大量的难分类样本的出现。这样的样本实质上使实现分子特征空间内聚表示的任务复杂化。为了改进分子性质预测，MolFeSCue框架中引入了动态对比损失函数，以管理具有挑战性的负样本的波动分布。与传统对比损失函数的静态特性不同，动态方法更适合神经网络的学习阶段。动态对比损失函数在梯度下降中，确保学习率根据当前训练阶段进行调整，这突出了损失函数的自适应性质，展示了如何通过结合难分样本的衰减比率来动态调整训练过程。作者假设在训练过程中，难样本呈指数衰减，但衰减到一定程度后趋于稳定。换言之，难分负样本的梯度在训练初始阶段更大，从而在训练的早期强调它们的重要性。随着学习的进展，这种强调自然会减弱，从而降低过拟合的风险，因为这种风险可能源于过度关注这些难分负样本。
MolFeSCue将小样本学习与对比学习相结合，增强类别间的差异性。对比学习侧重于表示学习，在特征空间中紧密聚类同一类的样本，同时将不同类别的样本明显分离。
