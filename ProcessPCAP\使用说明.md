# ProcessPCAP 使用说明

## 概述

ProcessPCAP是一个四阶段的网络流量处理pipeline，用于将原始PCAP文件转换为适合机器学习的RGB图像。

## 系统要求

### 环境要求
- Python 3.7+
- Windows/Linux系统
- 建议使用conda虚拟环境

### 依赖库
```bash
pip install scapy tqdm pillow numpy pathlib
```

或使用conda：
```bash
conda install scapy tqdm pillow numpy
```

## 目录结构

### 输入目录结构
```
dataset/
├── normal/
│   ├── file1.pcap
│   └── file2.pcap
└── abnormal/
    ├── malware1/
    │   └── sample1.pcap
    └── malware2/
        └── sample2.pcap
```

### 输出目录结构
```
ProcessPCAP/
├── splitpcap/          # 阶段1输出：会话分割后的AL层PCAP
├── anonymous/          # 阶段2输出：匿名化后的PCAP
├── features/           # 阶段3输出：AL/L7/L5特征
└── final/              # 阶段4输出：RGB图像
```

## 使用方法

### 1. 完整Pipeline运行

#### 基本用法
```bash
python pipeline.py --input ./dataset --output ./final
```

#### 高级用法
```bash
python pipeline.py \
    --input ./dataset \
    --output ./final \
    --max-sessions 10000 \
    --image-size 32 \
    --cleanup
```

### 2. 分阶段运行

#### 阶段1：会话分割
```bash
python splitpcap.py
```
- 输入：`./dataset/` 中的PCAP文件
- 输出：`./splitpcap/` 中按会话分割的AL层PCAP文件

#### 阶段2：流量匿名化
```bash
python anonymizer.py
```
- 输入：`./splitpcap/` 中的AL层PCAP文件
- 输出：`./anonymous/` 中匿名化后的PCAP文件

#### 阶段3：特征提取
```bash
python feature_extractor.py
```
- 输入：`./anonymous/` 中的匿名化PCAP文件
- 输出：`./features/` 中的AL/L7/L5特征文件

#### 阶段4：RGB图像生成
```bash
python pcap2rgb.py
```
- 输入：`./features/` 中的特征文件
- 输出：`./final/` 中的RGB图像

### 3. 跳过特定阶段

```bash
# 只运行阶段1和2
python pipeline.py --skip-stages 3 4

# 只运行阶段4
python pipeline.py --skip-stages 1 2 3
```

## 参数说明

### pipeline.py 参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--input` | `./dataset` | 输入PCAP文件目录 |
| `--output` | `./final` | 最终RGB图像输出目录 |
| `--max-sessions` | `50000` | 每个PCAP文件最大处理会话数 |
| `--image-size` | `32` | 输出RGB图像尺寸 |
| `--skip-stages` | `None` | 跳过的阶段列表 |
| `--cleanup` | `False` | 处理完成后清理中间文件 |

### 各模块参数

#### SessionSplitter
- `output_base_dir`: 输出目录
- `max_sessions`: 最大会话数

#### TrafficAnonymizer
- `input_base_dir`: 输入目录
- `output_base_dir`: 输出目录

#### FeatureExtractor
- `input_base_dir`: 输入目录
- `output_base_dir`: 输出目录

#### RGBGenerator
- `input_base_dir`: 输入目录
- `output_base_dir`: 输出目录
- `image_size`: 图像尺寸

## 输出说明

### RGB图像格式
- **尺寸**: 32x32像素（可配置）
- **格式**: PNG
- **通道映射**:
  - R通道：AL层（完整包数据）
  - G通道：L7层（应用层载荷）
  - B通道：L5层（传输层数据）

### 特征文件格式
- **AL层**: `.pcap` 格式（匿名化后的完整包）
- **L7层**: `.bin` 格式（应用层载荷的二进制数据）
- **L5层**: `.bin` 格式（传输层的二进制数据）

## 性能优化建议

### 内存优化
- 对于大型PCAP文件，建议设置较小的 `max_sessions` 值
- 使用 `--cleanup` 参数自动清理中间文件

### 处理速度
- 可以分阶段运行，避免重复处理
- 对于大量文件，考虑并行处理

## 故障排除

### 常见错误

#### 1. 内存不足
```
MemoryError: Unable to allocate array
```
**解决方案**: 减少 `max_sessions` 参数值

#### 2. 文件权限错误
```
PermissionError: [Errno 13] Permission denied
```
**解决方案**: 检查输出目录权限，使用管理员权限运行

#### 3. 依赖库缺失
```
ModuleNotFoundError: No module named 'scapy'
```
**解决方案**: 安装所需依赖库

#### 4. PCAP文件损坏
```
Scapy_Exception: Invalid pcap file
```
**解决方案**: 检查输入PCAP文件完整性

### 调试模式

修改各模块中的日志级别：
```python
logger.setLevel(logging.DEBUG)
```

### 验证输出

检查各阶段输出：
```bash
# 检查会话分割结果
ls -la splitpcap/

# 检查匿名化结果
ls -la anonymous/

# 检查特征提取结果
ls -la features/

# 检查最终RGB图像
ls -la final/
```

## 注意事项

1. **数据隐私**: 匿名化只处理IP和MAC地址，其他敏感信息需要额外处理
2. **文件大小**: 大型PCAP文件可能需要较长处理时间
3. **存储空间**: 中间文件会占用大量磁盘空间，建议使用 `--cleanup` 参数
4. **兼容性**: 某些特殊格式的PCAP文件可能无法正确处理

## 示例用法

### 处理单个数据集
```bash
python pipeline.py \
    --input ./CTU-13-Dataset \
    --output ./rgb_images \
    --max-sessions 5000 \
    --cleanup
```

### 批量处理多个数据集
```bash
for dataset in dataset1 dataset2 dataset3; do
    python pipeline.py \
        --input ./$dataset \
        --output ./output/$dataset \
        --max-sessions 10000 \
        --cleanup
done
```
