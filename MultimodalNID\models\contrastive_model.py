# -*- coding: utf-8 -*-
"""
对比学习模型
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Tu<PERSON>, Optional

from .image_encoder import ImageEncoder
from .sequence_encoder import SequenceEncoder
from .fusion_module import FusionModule
from ..config.image_config import ImageConfig
from ..config.sequence_config import SequenceConfig
from ..config.training_config import TrainingConfig


class ContrastiveModel(nn.Module):
    """双分支多模态对比学习模型"""
    
    def __init__(self, 
                 image_config: ImageConfig,
                 sequence_config: SequenceConfig,
                 training_config: TrainingConfig):
        """
        初始化对比学习模型
        
        Args:
            image_config: 图像配置
            sequence_config: 序列配置
            training_config: 训练配置
        """
        super(ContrastiveModel, self).__init__()
        
        self.image_config = image_config
        self.sequence_config = sequence_config
        self.training_config = training_config
        
        # 创建编码器
        self.image_encoder = ImageEncoder(image_config)
        self.sequence_encoder = SequenceEncoder(sequence_config)
        
        # 创建融合模块
        self.fusion_module = FusionModule(
            image_feature_dim=image_config.feature_dim,
            sequence_feature_dim=sequence_config.output_feature_dim,
            config=training_config
        )
        
        # 分类头（用于下游任务）
        self.classifier = nn.Sequential(
            nn.Linear(512, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5),
            nn.Linear(256, 2)  # 二分类：正常/异常
        )
        
        # 温度参数
        self.temperature = training_config.temperature
    
    def forward(self, image: torch.Tensor, 
                sequence: torch.Tensor,
                sequence_mask: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            image: 图像数据 (batch_size, channels, height, width)
            sequence: 序列数据 (batch_size, seq_len, feature_dim)
            sequence_mask: 序列掩码 (batch_size, seq_len)
            
        Returns:
            包含各种特征和预测结果的字典
        """
        # 图像编码
        image_backbone_features, image_features = self.image_encoder(image)
        
        # 序列编码
        sequence_backbone_features, sequence_features = self.sequence_encoder(
            sequence, sequence_mask
        )
        
        # 多模态融合
        fused_features = self.fusion_module(image_features, sequence_features)
        
        # 分类预测
        logits = self.classifier(fused_features)
        
        return {
            'image_features': image_features,
            'sequence_features': sequence_features,
            'fused_features': fused_features,
            'logits': logits,
            'image_backbone_features': image_backbone_features,
            'sequence_backbone_features': sequence_backbone_features
        }
    
    def compute_contrastive_loss(self, 
                               image_features: torch.Tensor,
                               sequence_features: torch.Tensor,
                               labels: torch.Tensor) -> torch.Tensor:
        """
        计算对比损失
        
        Args:
            image_features: 图像特征
            sequence_features: 序列特征
            labels: 标签
            
        Returns:
            对比损失
        """
        batch_size = image_features.size(0)
        
        # 计算相似度矩阵
        # 图像-序列相似度（正样本对）
        positive_sim = F.cosine_similarity(image_features, sequence_features, dim=1)
        
        # 图像-图像相似度（负样本对）
        image_sim_matrix = F.cosine_similarity(
            image_features.unsqueeze(1), 
            image_features.unsqueeze(0), 
            dim=2
        )
        
        # 序列-序列相似度（负样本对）
        sequence_sim_matrix = F.cosine_similarity(
            sequence_features.unsqueeze(1),
            sequence_features.unsqueeze(0),
            dim=2
        )
        
        # 跨模态相似度矩阵
        cross_modal_sim = F.cosine_similarity(
            image_features.unsqueeze(1),
            sequence_features.unsqueeze(0),
            dim=2
        )
        
        # 计算InfoNCE损失
        # 正样本：同一样本的图像和序列特征
        # 负样本：不同样本的特征
        
        # 对于每个图像特征，计算与所有序列特征的相似度
        logits_img_to_seq = cross_modal_sim / self.temperature
        
        # 对于每个序列特征，计算与所有图像特征的相似度
        logits_seq_to_img = cross_modal_sim.t() / self.temperature
        
        # 创建标签（对角线为正样本）
        targets = torch.arange(batch_size, device=image_features.device)
        
        # 计算交叉熵损失
        loss_img_to_seq = F.cross_entropy(logits_img_to_seq, targets)
        loss_seq_to_img = F.cross_entropy(logits_seq_to_img, targets)
        
        # 总对比损失
        contrastive_loss = (loss_img_to_seq + loss_seq_to_img) / 2
        
        return contrastive_loss
    
    def compute_alignment_loss(self,
                             image_features: torch.Tensor,
                             sequence_features: torch.Tensor) -> torch.Tensor:
        """
        计算模态对齐损失
        
        Args:
            image_features: 图像特征
            sequence_features: 序列特征
            
        Returns:
            对齐损失
        """
        # 使用MSE损失促进模态对齐
        alignment_loss = F.mse_loss(image_features, sequence_features)
        
        return alignment_loss
    
    def compute_total_loss(self,
                          outputs: Dict[str, torch.Tensor],
                          labels: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        计算总损失
        
        Args:
            outputs: 模型输出
            labels: 真实标签
            
        Returns:
            包含各种损失的字典
        """
        # 分类损失
        classification_loss = F.cross_entropy(outputs['logits'], labels.squeeze())
        
        # 对比损失
        contrastive_loss = self.compute_contrastive_loss(
            outputs['image_features'],
            outputs['sequence_features'],
            labels
        )
        
        # 对齐损失
        alignment_loss = self.compute_alignment_loss(
            outputs['image_features'],
            outputs['sequence_features']
        )
        
        # 总损失
        total_loss = (
            classification_loss +
            self.training_config.contrastive_loss_weight * contrastive_loss +
            self.training_config.alignment_loss_weight * alignment_loss
        )
        
        return {
            'total_loss': total_loss,
            'classification_loss': classification_loss,
            'contrastive_loss': contrastive_loss,
            'alignment_loss': alignment_loss
        }
    
    def get_embeddings(self, image: torch.Tensor, 
                      sequence: torch.Tensor,
                      sequence_mask: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        获取特征嵌入（用于推理）
        
        Args:
            image: 图像数据
            sequence: 序列数据
            sequence_mask: 序列掩码
            
        Returns:
            特征嵌入字典
        """
        with torch.no_grad():
            outputs = self.forward(image, sequence, sequence_mask)
            
        return {
            'image_features': outputs['image_features'],
            'sequence_features': outputs['sequence_features'],
            'fused_features': outputs['fused_features']
        }
    
    def predict(self, image: torch.Tensor,
               sequence: torch.Tensor,
               sequence_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        预测类别
        
        Args:
            image: 图像数据
            sequence: 序列数据
            sequence_mask: 序列掩码
            
        Returns:
            预测概率
        """
        with torch.no_grad():
            outputs = self.forward(image, sequence, sequence_mask)
            probabilities = F.softmax(outputs['logits'], dim=1)
            
        return probabilities
    
    def freeze_encoders(self):
        """冻结编码器参数（用于微调）"""
        for param in self.image_encoder.parameters():
            param.requires_grad = False
        
        for param in self.sequence_encoder.parameters():
            param.requires_grad = False
    
    def unfreeze_encoders(self):
        """解冻编码器参数"""
        for param in self.image_encoder.parameters():
            param.requires_grad = True
        
        for param in self.sequence_encoder.parameters():
            param.requires_grad = True
