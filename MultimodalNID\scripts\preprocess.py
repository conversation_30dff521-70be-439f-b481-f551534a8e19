# -*- coding: utf-8 -*-
"""
数据预处理脚本
"""

import argparse
import sys
from pathlib import Path
from typing import List
from tqdm import tqdm
import multiprocessing as mp

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from config import ImageConfig, SequenceConfig
from data import ImageProcessor, SequenceProcessor
from utils import setup_logger, FileManager


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='PCAP数据预处理脚本')
    
    # 输入输出路径
    parser.add_argument('--input_dir', type=str, required=True,
                       help='输入PCAP文件目录')
    parser.add_argument('--output_dir', type=str, required=True,
                       help='输出处理后数据目录')
    
    # 处理选项
    parser.add_argument('--process_images', action='store_true',
                       help='是否处理图像数据')
    parser.add_argument('--process_sequences', action='store_true',
                       help='是否处理序列数据')
    parser.add_argument('--split_data', action='store_true',
                       help='是否分割数据集')
    
    # 数据分割比例
    parser.add_argument('--train_ratio', type=float, default=0.7,
                       help='训练集比例')
    parser.add_argument('--val_ratio', type=float, default=0.15,
                       help='验证集比例')
    parser.add_argument('--test_ratio', type=float, default=0.15,
                       help='测试集比例')
    
    # 并行处理
    parser.add_argument('--num_workers', type=int, default=4,
                       help='并行处理工作进程数')
    
    # 其他选项
    parser.add_argument('--config_file', type=str, default=None,
                       help='配置文件路径')
    parser.add_argument('--overwrite', action='store_true',
                       help='是否覆盖已存在的输出文件')
    
    return parser.parse_args()


def setup_configs(args):
    """设置配置"""
    if args.config_file:
        config_data = FileManager.load_yaml(args.config_file)
        if config_data:
            image_config = ImageConfig.from_dict(config_data.get('image_config', {}))
            sequence_config = SequenceConfig.from_dict(config_data.get('sequence_config', {}))
        else:
            image_config = ImageConfig()
            sequence_config = SequenceConfig()
    else:
        image_config = ImageConfig()
        sequence_config = SequenceConfig()
    
    return image_config, sequence_config


def find_pcap_files(input_dir: Path) -> List[Path]:
    """查找PCAP文件"""
    pcap_files = []
    
    # 支持的PCAP文件扩展名
    pcap_extensions = ['.pcap', '.pcapng', '.cap']
    
    for ext in pcap_extensions:
        pcap_files.extend(input_dir.rglob(f'*{ext}'))
    
    return sorted(pcap_files)


def process_single_pcap_image(args_tuple):
    """处理单个PCAP文件生成图像"""
    pcap_file, output_dir, image_config, overwrite = args_tuple
    
    try:
        # 创建输出路径
        relative_path = pcap_file.relative_to(pcap_file.parents[2])  # 保持目录结构
        output_path = output_dir / 'images' / relative_path.with_suffix('.png')
        
        # 检查是否已存在
        if output_path.exists() and not overwrite:
            return f"跳过已存在的文件: {output_path}"
        
        # 创建处理器
        processor = ImageProcessor(image_config)
        
        # 处理PCAP文件
        image_array = processor.process_pcap(pcap_file)
        
        if image_array is not None:
            # 保存图像
            success = processor.save_image(image_array, output_path)
            if success:
                return f"成功处理: {pcap_file} -> {output_path}"
            else:
                return f"保存失败: {pcap_file}"
        else:
            return f"处理失败: {pcap_file}"
            
    except Exception as e:
        return f"错误处理 {pcap_file}: {e}"


def process_single_pcap_sequence(args_tuple):
    """处理单个PCAP文件生成序列"""
    pcap_file, output_dir, sequence_config, overwrite = args_tuple
    
    try:
        # 创建输出路径
        relative_path = pcap_file.relative_to(pcap_file.parents[2])  # 保持目录结构
        output_path = output_dir / 'sequences' / relative_path.with_suffix('.pkl')
        
        # 检查是否已存在
        if output_path.exists() and not overwrite:
            return f"跳过已存在的文件: {output_path}"
        
        # 创建处理器
        processor = SequenceProcessor(sequence_config)
        
        # 处理PCAP文件
        sequence_data = processor.process_pcap(pcap_file)
        
        if sequence_data:
            # 保存序列数据
            success = processor.save_processed_data(sequence_data, output_path)
            if success:
                return f"成功处理: {pcap_file} -> {output_path}"
            else:
                return f"保存失败: {pcap_file}"
        else:
            return f"处理失败: {pcap_file}"
            
    except Exception as e:
        return f"错误处理 {pcap_file}: {e}"


def split_dataset(pcap_files: List[Path], 
                 train_ratio: float, 
                 val_ratio: float, 
                 test_ratio: float) -> dict:
    """分割数据集"""
    import random
    
    # 验证比例
    total_ratio = train_ratio + val_ratio + test_ratio
    if abs(total_ratio - 1.0) > 1e-6:
        raise ValueError(f"数据集分割比例总和必须为1.0，当前为{total_ratio}")
    
    # 随机打乱
    random.shuffle(pcap_files)
    
    # 计算分割点
    total_files = len(pcap_files)
    train_end = int(total_files * train_ratio)
    val_end = train_end + int(total_files * val_ratio)
    
    # 分割
    splits = {
        'train': pcap_files[:train_end],
        'val': pcap_files[train_end:val_end],
        'test': pcap_files[val_end:]
    }
    
    return splits


def main():
    """主函数"""
    # 解析参数
    args = parse_args()
    
    # 设置配置
    image_config, sequence_config = setup_configs(args)
    
    # 设置日志
    logger = setup_logger('DataPreprocessor')
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    FileManager.ensure_dir(output_dir)
    
    logger.info("开始数据预处理...")
    logger.info(f"输入目录: {args.input_dir}")
    logger.info(f"输出目录: {args.output_dir}")
    
    # 查找PCAP文件
    input_dir = Path(args.input_dir)
    pcap_files = find_pcap_files(input_dir)
    
    if not pcap_files:
        logger.error(f"在{input_dir}中未找到PCAP文件")
        return
    
    logger.info(f"找到{len(pcap_files)}个PCAP文件")
    
    # 分割数据集
    if args.split_data:
        logger.info("分割数据集...")
        splits = split_dataset(pcap_files, args.train_ratio, args.val_ratio, args.test_ratio)
        
        for split_name, split_files in splits.items():
            logger.info(f"{split_name}: {len(split_files)}个文件")
    else:
        splits = {'all': pcap_files}
    
    # 处理图像数据
    if args.process_images:
        logger.info("开始处理图像数据...")
        
        for split_name, split_files in splits.items():
            logger.info(f"处理{split_name}分割的图像数据...")
            
            # 准备参数
            process_args = [
                (pcap_file, output_dir / split_name, image_config, args.overwrite)
                for pcap_file in split_files
            ]
            
            # 并行处理
            with mp.Pool(processes=args.num_workers) as pool:
                results = list(tqdm(
                    pool.imap(process_single_pcap_image, process_args),
                    total=len(process_args),
                    desc=f"处理{split_name}图像"
                ))
            
            # 统计结果
            success_count = sum(1 for r in results if "成功处理" in r)
            logger.info(f"{split_name}图像处理完成: {success_count}/{len(split_files)}成功")
    
    # 处理序列数据
    if args.process_sequences:
        logger.info("开始处理序列数据...")
        
        for split_name, split_files in splits.items():
            logger.info(f"处理{split_name}分割的序列数据...")
            
            # 准备参数
            process_args = [
                (pcap_file, output_dir / split_name, sequence_config, args.overwrite)
                for pcap_file in split_files
            ]
            
            # 并行处理
            with mp.Pool(processes=args.num_workers) as pool:
                results = list(tqdm(
                    pool.imap(process_single_pcap_sequence, process_args),
                    total=len(process_args),
                    desc=f"处理{split_name}序列"
                ))
            
            # 统计结果
            success_count = sum(1 for r in results if "成功处理" in r)
            logger.info(f"{split_name}序列处理完成: {success_count}/{len(split_files)}成功")
    
    # 保存配置
    config_dir = output_dir / 'configs'
    FileManager.ensure_dir(config_dir)
    FileManager.save_yaml(image_config.to_dict(), config_dir / 'image_config.yaml')
    FileManager.save_yaml(sequence_config.to_dict(), config_dir / 'sequence_config.yaml')
    
    logger.info("数据预处理完成!")


if __name__ == '__main__':
    main()
