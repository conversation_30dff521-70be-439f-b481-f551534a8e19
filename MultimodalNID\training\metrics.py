# -*- coding: utf-8 -*-
"""
评估指标模块
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Optional
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    roc_auc_score, confusion_matrix, classification_report
)


class MetricsCalculator:
    """评估指标计算器"""
    
    def __init__(self, num_classes: int = 2):
        """
        初始化指标计算器
        
        Args:
            num_classes: 类别数量
        """
        self.num_classes = num_classes
        self.reset()
    
    def reset(self):
        """重置累积的预测和标签"""
        self.all_predictions = []
        self.all_labels = []
        self.all_probabilities = []
    
    def update(self, predictions: torch.Tensor, 
               labels: torch.Tensor, 
               probabilities: Optional[torch.Tensor] = None):
        """
        更新预测和标签
        
        Args:
            predictions: 预测结果
            labels: 真实标签
            probabilities: 预测概率
        """
        # 转换为numpy数组
        if isinstance(predictions, torch.Tensor):
            predictions = predictions.cpu().numpy()
        if isinstance(labels, torch.Tensor):
            labels = labels.cpu().numpy()
        if probabilities is not None and isinstance(probabilities, torch.Tensor):
            probabilities = probabilities.cpu().numpy()
        
        # 展平数组
        predictions = predictions.flatten()
        labels = labels.flatten()
        
        # 添加到累积列表
        self.all_predictions.extend(predictions)
        self.all_labels.extend(labels)
        
        if probabilities is not None:
            if len(probabilities.shape) > 1:
                # 多类别情况，取正类概率
                probabilities = probabilities[:, 1] if probabilities.shape[1] > 1 else probabilities[:, 0]
            self.all_probabilities.extend(probabilities.flatten())
    
    def compute_metrics(self) -> Dict[str, float]:
        """
        计算所有评估指标
        
        Returns:
            包含各种指标的字典
        """
        if not self.all_predictions or not self.all_labels:
            return {}
        
        predictions = np.array(self.all_predictions)
        labels = np.array(self.all_labels)
        
        metrics = {}
        
        # 基础分类指标
        metrics['accuracy'] = accuracy_score(labels, predictions)
        metrics['precision'] = precision_score(labels, predictions, average='weighted', zero_division=0)
        metrics['recall'] = recall_score(labels, predictions, average='weighted', zero_division=0)
        metrics['f1_score'] = f1_score(labels, predictions, average='weighted', zero_division=0)
        
        # 每个类别的指标
        if self.num_classes == 2:
            metrics['precision_class_0'] = precision_score(labels, predictions, pos_label=0, zero_division=0)
            metrics['precision_class_1'] = precision_score(labels, predictions, pos_label=1, zero_division=0)
            metrics['recall_class_0'] = recall_score(labels, predictions, pos_label=0, zero_division=0)
            metrics['recall_class_1'] = recall_score(labels, predictions, pos_label=1, zero_division=0)
            metrics['f1_class_0'] = f1_score(labels, predictions, pos_label=0, zero_division=0)
            metrics['f1_class_1'] = f1_score(labels, predictions, pos_label=1, zero_division=0)
        
        # AUC指标（如果有概率）
        if self.all_probabilities:
            probabilities = np.array(self.all_probabilities)
            try:
                if self.num_classes == 2:
                    metrics['auc_roc'] = roc_auc_score(labels, probabilities)
                else:
                    # 多类别AUC
                    metrics['auc_roc'] = roc_auc_score(labels, probabilities, multi_class='ovr')
            except ValueError:
                # 如果只有一个类别，AUC无法计算
                metrics['auc_roc'] = 0.0
        
        # 混淆矩阵相关指标
        cm = confusion_matrix(labels, predictions)
        
        if self.num_classes == 2:
            tn, fp, fn, tp = cm.ravel()
            
            # 特异性
            metrics['specificity'] = tn / (tn + fp) if (tn + fp) > 0 else 0.0
            
            # 敏感性（召回率）
            metrics['sensitivity'] = tp / (tp + fn) if (tp + fn) > 0 else 0.0
            
            # 假正率
            metrics['false_positive_rate'] = fp / (fp + tn) if (fp + tn) > 0 else 0.0
            
            # 假负率
            metrics['false_negative_rate'] = fn / (fn + tp) if (fn + tp) > 0 else 0.0
        
        return metrics
    
    def compute_confusion_matrix(self) -> np.ndarray:
        """
        计算混淆矩阵
        
        Returns:
            混淆矩阵
        """
        if not self.all_predictions or not self.all_labels:
            return np.array([])
        
        predictions = np.array(self.all_predictions)
        labels = np.array(self.all_labels)
        
        return confusion_matrix(labels, predictions)
    
    def get_classification_report(self) -> str:
        """
        获取分类报告
        
        Returns:
            分类报告字符串
        """
        if not self.all_predictions or not self.all_labels:
            return ""
        
        predictions = np.array(self.all_predictions)
        labels = np.array(self.all_labels)
        
        return classification_report(labels, predictions)
    
    def compute_contrastive_accuracy(self, 
                                   image_features: torch.Tensor,
                                   sequence_features: torch.Tensor,
                                   temperature: float = 0.07) -> float:
        """
        计算对比学习准确率
        
        Args:
            image_features: 图像特征
            sequence_features: 序列特征
            temperature: 温度参数
            
        Returns:
            对比学习准确率
        """
        batch_size = image_features.size(0)
        
        # 归一化特征
        image_features = torch.nn.functional.normalize(image_features, p=2, dim=1)
        sequence_features = torch.nn.functional.normalize(sequence_features, p=2, dim=1)
        
        # 计算相似度矩阵
        similarity_matrix = torch.matmul(image_features, sequence_features.T) / temperature
        
        # 预测（最相似的序列特征）
        predictions = torch.argmax(similarity_matrix, dim=1)
        targets = torch.arange(batch_size, device=image_features.device)
        
        # 计算准确率
        accuracy = (predictions == targets).float().mean().item()
        
        return accuracy


class EarlyStopping:
    """早停机制"""
    
    def __init__(self, patience: int = 7, min_delta: float = 0.0, 
                 restore_best_weights: bool = True):
        """
        初始化早停机制
        
        Args:
            patience: 容忍轮数
            min_delta: 最小改善幅度
            restore_best_weights: 是否恢复最佳权重
        """
        self.patience = patience
        self.min_delta = min_delta
        self.restore_best_weights = restore_best_weights
        
        self.best_score = None
        self.counter = 0
        self.best_weights = None
        self.early_stop = False
    
    def __call__(self, score: float, model: torch.nn.Module) -> bool:
        """
        检查是否应该早停
        
        Args:
            score: 当前分数（越大越好）
            model: 模型
            
        Returns:
            是否应该早停
        """
        if self.best_score is None:
            self.best_score = score
            self.save_checkpoint(model)
        elif score < self.best_score + self.min_delta:
            self.counter += 1
            if self.counter >= self.patience:
                self.early_stop = True
                if self.restore_best_weights and self.best_weights is not None:
                    model.load_state_dict(self.best_weights)
        else:
            self.best_score = score
            self.save_checkpoint(model)
            self.counter = 0
        
        return self.early_stop
    
    def save_checkpoint(self, model: torch.nn.Module):
        """保存检查点"""
        if self.restore_best_weights:
            self.best_weights = model.state_dict().copy()


class LearningRateScheduler:
    """学习率调度器"""
    
    def __init__(self, optimizer: torch.optim.Optimizer, 
                 scheduler_type: str = 'cosine',
                 **kwargs):
        """
        初始化学习率调度器
        
        Args:
            optimizer: 优化器
            scheduler_type: 调度器类型
            **kwargs: 调度器参数
        """
        self.optimizer = optimizer
        self.scheduler_type = scheduler_type
        
        if scheduler_type == 'cosine':
            from torch.optim.lr_scheduler import CosineAnnealingLR
            self.scheduler = CosineAnnealingLR(optimizer, **kwargs)
        elif scheduler_type == 'step':
            from torch.optim.lr_scheduler import StepLR
            self.scheduler = StepLR(optimizer, **kwargs)
        elif scheduler_type == 'exponential':
            from torch.optim.lr_scheduler import ExponentialLR
            self.scheduler = ExponentialLR(optimizer, **kwargs)
        elif scheduler_type == 'plateau':
            from torch.optim.lr_scheduler import ReduceLROnPlateau
            self.scheduler = ReduceLROnPlateau(optimizer, **kwargs)
        else:
            raise ValueError(f"不支持的调度器类型: {scheduler_type}")
    
    def step(self, metric: Optional[float] = None):
        """更新学习率"""
        if self.scheduler_type == 'plateau':
            if metric is not None:
                self.scheduler.step(metric)
        else:
            self.scheduler.step()
    
    def get_last_lr(self) -> List[float]:
        """获取最后的学习率"""
        return self.scheduler.get_last_lr()
