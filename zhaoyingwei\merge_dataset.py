import os
import shutil
from tqdm import tqdm


def copy_files_with_prefix(source_dirs, target_dir):
    """
    将源目录的文件复制到目标目录，添加源目录名前缀

    :param source_dirs: 源目录路径列表（例如：['source1', 'source2', 'source3']）
    :param target_dir: 目标目录路径
    """
    # 确保目标目录存在
    os.makedirs(target_dir, exist_ok=True)

    # 第一阶段：统计总文件数
    total_files = 0
    print("正在扫描文件...")
    for source_dir in source_dirs:
        source_name = os.path.basename(os.path.normpath(source_dir))
        for root, _, files in os.walk(source_dir):
            total_files += len(files)

    # 第二阶段：处理文件（带进度条）
    processed = 0
    with tqdm(total=total_files, desc="复制文件", unit="file") as pbar:
        for source_dir in source_dirs:
            source_name = os.path.basename(os.path.normpath(source_dir))

            for root, _, files in os.walk(source_dir):
                for file in files:
                    # 构建相对路径
                    rel_path = os.path.relpath(root, source_dir)
                    dest_subdir = os.path.join(target_dir, rel_path)
                    os.makedirs(dest_subdir, exist_ok=True)

                    # 构建新文件名
                    new_filename = f"{source_name}_{file}"
                    src_path = os.path.join(root, file)
                    dest_path = os.path.join(dest_subdir, new_filename)

                    # 复制文件
                    shutil.copy2(src_path, dest_path)

                    # 更新进度条
                    processed += 1
                    pbar.update(1)
                    pbar.set_postfix(file=f"{source_name}/{file}")


# 使用示例
if __name__ == "__main__":
    # 配置源目录和目标目录
    source_directories = [
        "./gen_datas"
    ]
    target_directory = "../data/GEN_DATAS"

    # 执行复制操作
    copy_files_with_prefix(source_directories, target_directory)
    print("\n所有文件处理完成！")