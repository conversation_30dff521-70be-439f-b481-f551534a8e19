import os
import numpy as np
from PIL import Image
import pickle
from tqdm import tqdm


def convert_image_to_sequence(image_path):
    """将单张灰度图像转换为1D序列"""
    img = Image.open(image_path).convert('L')  # 确保是灰度模式
    return np.array(img).flatten()  # 展平为1D数组


def process_dataset(input_root, output_root):
    """
    处理整个数据集，保持目录结构
    :param input_root: 输入数据集根目录（包含Train/Test）
    :param output_root: 输出数据集根目录
    """
    for split in ['Train', 'Test']:
        input_split_dir = os.path.join(input_root, split)
        output_split_dir = os.path.join(output_root, split)

        if not os.path.exists(input_split_dir):
            continue

        os.makedirs(output_split_dir, exist_ok=True)

        # 获取所有类别目录
        class_dirs = [d for d in os.listdir(input_split_dir)
                      if os.path.isdir(os.path.join(input_split_dir, d))]

        for class_dir in tqdm(class_dirs, desc=f"处理{split}数据集"):
            input_class_path = os.path.join(input_split_dir, class_dir)
            output_class_path = os.path.join(output_split_dir, class_dir)

            os.makedirs(output_class_path, exist_ok=True)

            # 处理每个图像文件
            image_files = [f for f in os.listdir(input_class_path)
                           if f.endswith(('.png', '.jpg', '.jpeg'))]

            for img_file in tqdm(image_files, desc=f"类别 {class_dir}", leave=False):
                input_path = os.path.join(input_class_path, img_file)
                output_path = os.path.join(output_class_path,
                                           os.path.splitext(img_file)[0] + '.pkl')

                # 转换并保存序列
                sequence = convert_image_to_sequence(input_path)
                with open(output_path, 'wb') as f:
                    pickle.dump(sequence, f)


if __name__ == "__main__":
    # 配置路径
    INPUT_DATASET = "./USTC_IMG"  # 替换为你的输入路径
    OUTPUT_DATASET = "./USTC_SEQUENCE"  # 替换为你的输出路径

    print(f"开始转换数据集: {INPUT_DATASET} → {OUTPUT_DATASET}")
    process_dataset(INPUT_DATASET, OUTPUT_DATASET)
    print("数据集转换完成！")
