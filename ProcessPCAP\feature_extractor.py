# -*- coding: utf-8 -*-
"""
Optimized Stage 3: AL/L7/L5 Feature Extraction
Handles large PCAP files with streaming approach
"""

import os
import logging
from pathlib import Path
from scapy.all import PcapReader, TCP, UDP, ICMP, Raw
from tqdm import tqdm
import gc


class FeatureExtractor:
    def __init__(self, input_base_dir="./temp_files/anonymous", output_base_dir="./temp_files/features"):
        self.input_base_dir = Path(input_base_dir)
        self.output_base_dir = Path(output_base_dir)
        self.logger = self._setup_logger()
        # 优化：设置垃圾回收阈值
        gc.set_threshold(700, 10, 10)

    def _setup_logger(self):
        logger = logging.getLogger('FeatureExtractor')
        logger.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

        # 避免重复添加handler
        if not logger.handlers:
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)

        return logger

    def _extract_l7_payload(self, packet):
        """直接返回L7负载而不转换字节对象"""
        try:
            if packet.haslayer(Raw):
                return packet[Raw].load
            return b''
        except Exception as e:
            self.logger.debug(f"L7 extraction warning: {e}")
            return b''

    def _extract_l5_data(self, packet):
        """流式传输层数据处理"""
        try:
            for layer in [TCP, UDP, ICMP]:
                if packet.haslayer(layer):
                    return packet[layer].build()
            return b''
        except Exception as e:
            self.logger.debug(f"L5 extraction warning: {e}")
            return b''

    def _process_session_pcap(self, pcap_path, output_dir, session_name):
        """流式处理PCAP文件"""
        try:
            # 提前创建输出目录
            al_dir = output_dir / 'al'
            l7_dir = output_dir / 'l7'
            l5_dir = output_dir / 'l5'

            for d in [al_dir, l7_dir, l5_dir]:
                d.mkdir(parents=True, exist_ok=True)

            # AL层直接复制文件（小文件操作）
            al_output = al_dir / f"{session_name}.pcap"
            al_output.write_bytes(pcap_path.read_bytes())

            # 流式处理L7/L5数据
            l7_output = l7_dir / f"{session_name}.bin"
            l5_output = l5_dir / f"{session_name}.bin"

            packets_processed = 0
            with open(l7_output, 'wb') as l7_writer, \
                    open(l5_output, 'wb') as l5_writer, \
                    PcapReader(str(pcap_path)) as pcap_reader:

                for packet in pcap_reader:
                    # L7处理
                    l7_data = self._extract_l7_payload(packet)
                    if l7_data:
                        l7_writer.write(l7_data)

                    # L5处理
                    l5_data = self._extract_l5_data(packet)
                    if l5_data:
                        l5_writer.write(l5_data)

                    packets_processed += 1
                    # 每1000个包主动回收内存
                    if packets_processed % 1000 == 0:
                        gc.collect()

            return True

        except MemoryError:
            self.logger.error(f"Memory overflow processing {pcap_path}")
            return False
        except Exception as e:
            self.logger.error(f"Critical error: {e}")
            return False

    def process_session_directory(self, session_dir):
        """处理会话目录（添加内存监控）"""
        session_dir = Path(session_dir)
        rel_path = session_dir.relative_to(self.input_base_dir)
        output_dir = self.output_base_dir / rel_path

        al_dir = session_dir / 'al'
        if not al_dir.exists():
            return

        session_files = list(al_dir.glob('*.pcap'))
        if not session_files:
            return

        # 使用tqdm进度条
        with tqdm(total=len(session_files),
                  desc=f"Processing {session_dir.name}",
                  position=0, leave=False) as pbar:

            success_count = 0
            for pcap_file in session_files:
                session_name = pcap_file.stem
                pbar.set_description(f"File: {session_name[:15]}...")

                if self._process_session_pcap(pcap_file, output_dir, session_name):
                    success_count += 1

                # 每个文件后立即回收资源
                gc.collect()
                pbar.update(1)

            pbar.set_description(f"Completed: {success_count}/{len(session_files)}")

    def process_directory(self):
        """主处理流程（添加异常防护）"""
        if not self.input_base_dir.exists():
            raise FileNotFoundError(str(self.input_base_dir))

        session_dirs = []
        for root, dirs, _ in os.walk(self.input_base_dir):
            root = Path(root)
            if (root / 'al').exists():
                session_dirs.append(root)

        if not session_dirs:
            self.logger.warning("No session directories found")
            return

        # 带进度条的目录处理
        with tqdm(total=len(session_dirs), desc="Overall Progress") as master_bar:
            for session_dir in session_dirs:
                master_bar.set_description(f"Dir: {session_dir.name[:20]}...")
                try:
                    self.process_session_directory(session_dir)
                except Exception as e:
                    self.logger.error(f"Session failed: {session_dir} - {e}")
                finally:
                    master_bar.update(1)
                    gc.collect()


if __name__ == "__main__":
    # 配置日志级别
    logging.basicConfig(level=logging.INFO)

    extractor = FeatureExtractor(
        input_base_dir="./temp/pcap_dataset/ISAC218/anonymous",
        output_base_dir="./temp/pcap_dataset/ISAC218/features"
    )
    extractor.process_directory()