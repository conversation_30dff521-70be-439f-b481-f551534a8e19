import torch
from torchvision import transforms
from torch import nn
from torch.nn import functional as F
from torch.utils.data import DataLoader
from matplotlib import pyplot as plt
from tqdm.auto import tqdm

class CustomDDPMScheduler:
    def __init__(self, num_train_timesteps=1000, beta_start=0.0001, beta_end=0.02, beta_schedule='linear'):
        self.num_train_timesteps = num_train_timesteps
        self.beta_schedule = beta_schedule
        # 计算beta值
        if beta_schedule == 'linear':
            self.betas = torch.linspace(beta_start, beta_end, num_train_timesteps)
        elif beta_schedule == 'squaredcos_cap_v2':
            steps = num_train_timesteps + 1
            x = torch.linspace(0, num_train_timesteps, steps)
            alphas_cumprod = torch.cos(((x / num_train_timesteps) + 0.008) / 1.008 * torch.pi * 0.5) ** 2
            alphas_cumprod = alphas_cumprod / alphas_cumprod[0]
            betas = 1 - (alphas_cumprod[1:] / alphas_cumprod[:-1])
            self.betas = torch.clip(betas, 0.0001, 0.9999)

        # 计算alpha值
        self.alphas = 1.0 - self.betas
        self.alphas_cumprod = torch.cumprod(self.alphas, dim=0)
        self.alphas_cumprod_prev = F.pad(self.alphas_cumprod[:-1], (1, 0), value=1.0)

        # 计算参数
        self.sqrt_alphas_cumprod = torch.sqrt(self.alphas_cumprod)
        self.sqrt_one_minus_alphas_cumprod = torch.sqrt(1.0 - self.alphas_cumprod)

    def add_noise(self, original_samples, noise, timesteps):
        sqrt_alpha = self.sqrt_alphas_cumprod[timesteps].to(original_samples.device)
        sqrt_one_minus_alpha = self.sqrt_one_minus_alphas_cumprod[timesteps].to(original_samples.device)

        # 调整形状以适应批量处理
        sqrt_alpha = sqrt_alpha.reshape(-1, 1, 1, 1)
        sqrt_one_minus_alpha = sqrt_one_minus_alpha.reshape(-1, 1, 1, 1)

        noisy_samples = sqrt_alpha * original_samples + sqrt_one_minus_alpha * noise
        return noisy_samples
    # 这是DDIM的采样方式，不是DDPM的采样方式
    # def step(self, model_output, timestep, sample):
    #     t = timestep
    #     prev_t = t - 1 if t > 0 else 0
    #     device = sample.device
    #
    #     # 从GPU获取参数
    #     alpha_prod_t = self.alphas_cumprod[t].to(device)
    #     alpha_prod_t_prev = self.alphas_cumprod_prev[prev_t].to(device) if t > 0 else torch.tensor(1.0).to(device)
    #     beta_prod_t = 1 - alpha_prod_t
    #     current_beta_t = self.betas[t].to(device)
    #
    #
    #     # 计算预测的原始样本
    #     pred_original_sample = (sample - torch.sqrt(beta_prod_t) * model_output) / torch.sqrt(alpha_prod_t)
    #
    #     # 计算前一步的均值
    #     pred_sample_direction = torch.sqrt(1 - alpha_prod_t_prev) * model_output
    #     mean = (torch.sqrt(alpha_prod_t_prev) * pred_original_sample + pred_sample_direction)
    #
    #     # 计算方差
    #     variance = (1 - alpha_prod_t_prev) / (1 - alpha_prod_t) * current_beta_t
    #     variance = torch.clamp(variance, min=1e-20)
    #
    #
    #
    #     # 生成新样本
    #     if t > 0:
    #         noise = torch.randn_like(sample)
    #         prev_sample = mean + torch.sqrt(variance) * noise
    #     else:
    #         prev_sample = mean
    #
    #     class StepOutput:
    #         def __init__(self, prev_sample):
    #             self.prev_sample = prev_sample
    #
    #     return StepOutput(prev_sample)
    def step(self, model_output, timestep, sample):
        t = timestep
        prev_t = t - 1 if t > 0 else 0
        device = sample.device

        # 获取参数
        alpha_prod_t = self.alphas_cumprod[t].to(device)
        beta_t = self.betas[t].to(device)
        alpha_t = 1 - beta_t

        # DDPM核心公式
        # 计算均值部分：1/sqrt(α_t) * (x_t - β_t/sqrt(1-ᾱ_t) * ε_θ)
        mean = (sample - beta_t * model_output / torch.sqrt(1 - alpha_prod_t)) / torch.sqrt(alpha_t)
        # DDPM使用固定方差β_t
        variance = beta_t
        # 生成新样本
        if t > 0:
            noise = torch.randn_like(sample)
            prev_sample = mean + torch.sqrt(variance) * noise
        else:
            prev_sample = mean

        class StepOutput:
            def __init__(self, prev_sample):
                self.prev_sample = prev_sample

        return StepOutput(prev_sample)


class SimpleUNetBlock(nn.Module):
    def __init__(self, in_channels, out_channels, time_emb_dim=None):
        super().__init__()
        self.conv1 = nn.Conv2d(in_channels, out_channels, 3, padding=1)
        self.conv2 = nn.Conv2d(out_channels, out_channels, 3, padding=1)
        self.norm1 = nn.BatchNorm2d(out_channels)
        self.norm2 = nn.BatchNorm2d(out_channels)
        self.relu = nn.ReLU()

        if time_emb_dim:
            self.time_mlp = nn.Linear(time_emb_dim, out_channels)

    def forward(self, x, t=None):
        h = self.relu(self.norm1(self.conv1(x)))
        if t is not None:
            t_emb = self.time_mlp(t)
            t_emb = t_emb.view(t_emb.shape[0], t_emb.shape[1], 1, 1)
            h = h + t_emb
        h = self.relu(self.norm2(self.conv2(h)))
        return h


class SimpleDownBlock(nn.Module):
    def __init__(self, in_channels, out_channels, time_emb_dim=None):
        super().__init__()
        self.block = SimpleUNetBlock(in_channels, out_channels, time_emb_dim)
        self.downsample = nn.Conv2d(out_channels, out_channels, 4, stride=2, padding=1)

    def forward(self, x, t=None):
        h = self.block(x, t)
        return h, self.downsample(h)


class SimpleUpBlock(nn.Module):
    def __init__(self, in_channels, out_channels, skip_channels, time_emb_dim=None):
        super().__init__()
        self.upsample = nn.ConvTranspose2d(in_channels, in_channels // 2, 4, stride=2, padding=1)
        self.adjust_skip = nn.Conv2d(skip_channels, out_channels, kernel_size=1)
        self.block = SimpleUNetBlock(in_channels // 2 + out_channels, out_channels, time_emb_dim)

    def forward(self, x, skip=None, t=None):
        x = self.upsample(x)
        if skip is not None:
            skip = self.adjust_skip(skip)
            x = torch.cat([x, skip], dim=1)
        return self.block(x, t)


class SimpleUNet2DModel(nn.Module):
    def __init__(self,
                 sample_size=28,
                 in_channels=1,
                 out_channels=1,
                 block_out_channels=(32, 64, 128)):
        super().__init__()
        self.sample_size = sample_size
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.time_emb_dim = 128

        self.time_mlp = nn.Sequential(
            nn.Linear(self.time_emb_dim, self.time_emb_dim),
            nn.ReLU(),
            nn.Linear(self.time_emb_dim, self.time_emb_dim)
        )

        self.init_conv = nn.Conv2d(in_channels, block_out_channels[0], 3, padding=1)

        self.down_blocks = nn.ModuleList()
        in_chs = block_out_channels[0]
        for out_chs in block_out_channels[1:]:
            self.down_blocks.append(SimpleDownBlock(in_chs, out_chs, self.time_emb_dim))
            in_chs = out_chs

        self.middle_block = SimpleUNetBlock(block_out_channels[-1], block_out_channels[-1], self.time_emb_dim)

        self.up_blocks = nn.ModuleList()
        reversed_chs = list(reversed(block_out_channels))
        for i in range(len(block_out_channels) - 1):
            in_chs = reversed_chs[i]
            out_chs = reversed_chs[i + 1]
            self.up_blocks.append(SimpleUpBlock(in_chs, out_chs, in_chs, self.time_emb_dim))

        self.final_conv = nn.Conv2d(block_out_channels[0], out_channels, 3, padding=1)

    def forward(self, x, t):
        t_emb = self.get_time_embedding(t)
        x = self.init_conv(x)
        skips = [x]

        for down_block in self.down_blocks:
            skip, x = down_block(x, t_emb)
            skips.append(skip)

        x = self.middle_block(x, t_emb)

        for up_block in self.up_blocks:
            skip = skips.pop()
            x = up_block(x, skip, t_emb)

        x = self.final_conv(x)
        return x

    def get_time_embedding(self, timesteps):
        half_dim = self.time_emb_dim // 2
        emb = torch.log(torch.tensor(10000.0)) / (half_dim - 1)
        emb = torch.exp(torch.arange(half_dim, dtype=torch.float32, device=timesteps.device) * -emb)
        emb = timesteps.float().unsqueeze(-1) * emb.unsqueeze(0)
        emb = torch.cat([torch.sin(emb), torch.cos(emb)], dim=-1)
        return self.time_mlp(emb)


device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f'Using device: {device}')

transform = transforms.Compose([
    transforms.Resize((28, 28)),
    transforms.Grayscale(num_output_channels=1),
    transforms.ToTensor(),
    transforms.Normalize([0.5], [0.5])
])

from torchvision.datasets import ImageFolder

dataset = ImageFolder(root='./mnist_dataset/test', transform=transform)
train_dataloader = DataLoader(dataset, batch_size=256, shuffle=True)


class ClassConditionedUnet(nn.Module):
    def __init__(self, num_classes=10, class_emb_size=4):
        super().__init__()
        self.class_emb = nn.Embedding(num_classes, class_emb_size)
        self.model = SimpleUNet2DModel(
            sample_size=28,
            in_channels=1 + class_emb_size,
            out_channels=1,
            block_out_channels=(32, 64, 128)
        )

    def forward(self, x, t, class_labels):
        bs, ch, w, h = x.shape
        class_cond = self.class_emb(class_labels)
        class_cond = class_cond.view(bs, class_cond.shape[1], 1, 1).expand(-1, -1, w, h)
        net_input = torch.cat((x, class_cond), dim=1)
        return self.model(net_input, t)


model = ClassConditionedUnet().to(device)
noisy_xb = torch.randn(8, 1, 28, 28).to(device)
timesteps = torch.tensor([500, 500, 500, 500, 500, 500, 500, 500]).to(device)
y = torch.tensor([1, 1, 1, 1, 1, 1, 1, 1]).to(device)
with torch.no_grad():
    model_prediction = model(noisy_xb, timesteps, y)
print("Model output shape:", model_prediction.shape)

n_epochs = 100
noise_scheduler = CustomDDPMScheduler(num_train_timesteps=1000, beta_schedule='squaredcos_cap_v2')
noise_scheduler.betas = noise_scheduler.betas.to(device)
noise_scheduler.alphas = noise_scheduler.alphas.to(device)
noise_scheduler.alphas_cumprod = noise_scheduler.alphas_cumprod.to(device)
noise_scheduler.alphas_cumprod_prev = noise_scheduler.alphas_cumprod_prev.to(device)
noise_scheduler.sqrt_alphas_cumprod = noise_scheduler.sqrt_alphas_cumprod.to(device)
noise_scheduler.sqrt_one_minus_alphas_cumprod = noise_scheduler.sqrt_one_minus_alphas_cumprod.to(device)

net = ClassConditionedUnet().to(device)
loss_fn = nn.MSELoss()
opt = torch.optim.Adam(net.parameters(), lr=1e-3)

losses = []
for epoch in range(n_epochs):
    net.train()
    for x, y in tqdm(train_dataloader):
        x = x.to(device)
        y = y.to(device)
        noise = torch.randn_like(x)
        timesteps = torch.randint(
            0, noise_scheduler.num_train_timesteps,
            (x.shape[0],), device=device
        ).long()

        noisy_x = noise_scheduler.add_noise(x, noise, timesteps)
        pred = net(noisy_x, timesteps, y)
        loss = loss_fn(pred, noise)

        opt.zero_grad()
        loss.backward()
        opt.step()

        losses.append(loss.item())

    avg_loss = sum(losses[-len(train_dataloader):]) / len(train_dataloader)
    print(f'Epoch {epoch + 1}/{n_epochs} | Loss: {avg_loss:.5f}')

torch.save(net.state_dict(), "conditional_diffusion_model.pth")

plt.figure(dpi=300)
plt.plot(losses)
plt.xlabel("Iteration")
plt.ylabel("Loss")
plt.title("Training Loss")
plt.savefig("training_loss.png")
plt.show()

# 加载模型
net = ClassConditionedUnet().to(device)
net.load_state_dict(torch.load("conditional_diffusion_model.pth", map_location=device))
net.eval()

# 创建噪声输入
x = torch.randn(10, 1, 28, 28).to(device)
y = torch.arange(0, 10, device=device)

# 倒序时间步
timesteps = list(range(noise_scheduler.num_train_timesteps))[::-1]

# 采样循环
with torch.no_grad():
    for t in tqdm(timesteps):
        timestep_tensor = torch.full((x.size(0),), t, device=device, dtype=torch.long)
        residual = net(x, timestep_tensor, y)
        step_output = noise_scheduler.step(residual, t, x)
        x = step_output.prev_sample
        x = torch.clamp(x, -1, 1)  # 裁剪到[-1, 1]范围

    # 最终处理后保存结果
    x = (x + 1) / 2  # 转换到[0, 1]
    x = x.detach().cpu()

    fig, axes = plt.subplots(2, 5, figsize=(15, 6))
    for i, ax in enumerate(axes.flatten()):
        ax.imshow(x[i, 0], cmap='gray', vmin=0, vmax=1)
        ax.set_title(f"Label: {y[i].item()}")
        ax.axis('off')
    plt.tight_layout()
    plt.savefig("generated_samples.png")
    plt.show()