# -*- coding: utf-8 -*-
"""
Stage 4: RGB Image Generation with Progress Bar
Converts extracted AL/L7/L5 layer features into RGB images (32x32, no normalization)
"""

import os
import logging
import numpy as np
from pathlib import Path
from PIL import Image
from scapy.utils import Pcap<PERSON>eader  # 使用流式读取器
from tqdm import tqdm  # 添加进度条库


class RGBGenerator:
    """
    RGB image generator that combines AL/L7/L5 layers into RGB channels
    """

    def __init__(self, input_base_dir="./temp_files/features", output_base_dir="./img_datasets", image_size=32):
        """
        Initialize RGB generator

        Args:
            input_base_dir: Base directory containing extracted features
            output_base_dir: Base directory for RGB images
            image_size: Size of output images (default: 32x32)
        """
        self.input_base_dir = Path(input_base_dir)
        self.output_base_dir = Path(output_base_dir)
        self.image_size = image_size
        self.target_pixels = image_size * image_size
        self.logger = self._setup_logger()

    def _setup_logger(self):
        """Setup logging"""
        logger = logging.getLogger('RGBGenerator')
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger

    def _extract_bytes_from_pcap(self, pcap_path):
        """
        Extract raw bytes from PCAP file (streaming)

        Args:
            pcap_path: Path to PCAP file

        Returns:
            bytes: Raw packet data (max 1024 bytes)
        """
        try:
            total_bytes = self.target_pixels
            raw_data = b''
            with PcapReader(str(pcap_path)) as pcap_reader:
                for pkt in pcap_reader:
                    raw_data += bytes(pkt)[:total_bytes - len(raw_data)]
                    if len(raw_data) >= total_bytes:
                        break
            return raw_data
        except Exception as e:
            self.logger.warning(f"Failed to extract bytes from {pcap_path}: {e}")
            return b''

    def _extract_bytes_from_bin(self, bin_path):
        """
        Extract raw bytes from binary file (only first 1024 bytes)

        Args:
            bin_path: Path to binary file

        Returns:
            bytes: Raw binary data (max 1024 bytes)
        """
        try:
            with open(bin_path, 'rb') as f:
                return f.read(self.target_pixels)
        except Exception as e:
            self.logger.warning(f"Failed to extract bytes from {bin_path}: {e}")
            return b''

    def _bytes_to_matrix(self, data, target_size):
        """
        Convert bytes to matrix with padding/truncation

        Args:
            data: Raw bytes data
            target_size: Target number of pixels

        Returns:
            np.ndarray: Matrix of uint8 values
        """
        if not data:
            # Return zero matrix for empty data
            return np.zeros(target_size, dtype=np.uint8)

        # Convert bytes to numpy array
        byte_array = np.frombuffer(data, dtype=np.uint8)

        # Pad or truncate to target size
        if len(byte_array) < target_size:
            # Pad with zeros
            padded = np.zeros(target_size, dtype=np.uint8)
            padded[:len(byte_array)] = byte_array
            return padded
        else:
            # Truncate to target size
            return byte_array[:target_size]

    def _find_layer_files(self, session_dir, session_name):
        """
        Find corresponding layer files for a session

        Args:
            session_dir: Session directory containing al/l7/l5 subdirs
            session_name: Base name of session files

        Returns:
            dict: Paths to layer files {'al': path, 'l7': path, 'l5': path}
        """
        layer_files = {}

        # AL layer (PCAP files)
        al_file = session_dir / 'al' / f"{session_name}.pcap"
        if al_file.exists():
            layer_files['al'] = al_file

        # L7 layer (binary files)
        l7_file = session_dir / 'l7' / f"{session_name}.bin"
        if l7_file.exists():
            layer_files['l7'] = l7_file

        # L5 layer (binary files)
        l5_file = session_dir / 'l5' / f"{session_name}.bin"
        if l5_file.exists():
            layer_files['l5'] = l5_file

        return layer_files

    def _generate_rgb_image(self, layer_files, output_path):
        """
        Generate RGB image from layer files

        Args:
            layer_files: Dictionary of layer file paths
            output_path: Output path for RGB image
        """
        try:
            # Extract data from each layer
            channels = []

            # Red channel (AL layer)
            if 'al' in layer_files:
                al_data = self._extract_bytes_from_pcap(layer_files['al'])
            else:
                al_data = b''
            al_matrix = self._bytes_to_matrix(al_data, self.target_pixels)
            channels.append(al_matrix.reshape(self.image_size, self.image_size))

            # Green channel (L7 layer)
            if 'l7' in layer_files:
                l7_data = self._extract_bytes_from_bin(layer_files['l7'])
            else:
                l7_data = b''
            l7_matrix = self._bytes_to_matrix(l7_data, self.target_pixels)
            channels.append(l7_matrix.reshape(self.image_size, self.image_size))

            # Blue channel (L5 layer)
            if 'l5' in layer_files:
                l5_data = self._extract_bytes_from_bin(layer_files['l5'])
            else:
                l5_data = b''
            l5_matrix = self._bytes_to_matrix(l5_data, self.target_pixels)
            channels.append(l5_matrix.reshape(self.image_size, self.image_size))

            # Stack channels to create RGB image
            rgb_array = np.stack(channels, axis=-1)

            # Create and save image (no normalization as requested)
            image = Image.fromarray(rgb_array, 'RGB')

            # Ensure output directory exists
            output_path.parent.mkdir(parents=True, exist_ok=True)

            # Save image
            image.save(output_path)

            return True

        except Exception as e:
            self.logger.error(f"Failed to generate RGB image {output_path}: {e}")
            return False

    def process_session_directory(self, session_dir, pbar=None):
        """
        Process a single session directory with optional progress bar

        Args:
            session_dir: Directory containing AL/L7/L5 session files
            pbar: Optional progress bar instance
        """
        session_dir = Path(session_dir)

        if not session_dir.exists():
            self.logger.warning(f"Session directory not found: {session_dir}")
            return

        # Calculate output directory
        rel_path = session_dir.relative_to(self.input_base_dir)
        output_dir = self.output_base_dir / rel_path

        # Find all session files (use AL layer as reference)
        al_dir = session_dir / 'al'
        if not al_dir.exists():
            self.logger.warning(f"AL directory not found: {al_dir}")
            return

        session_files = []
        for file_path in al_dir.iterdir():
            if file_path.is_file() and file_path.suffix == '.pcap':
                session_name = file_path.stem
                session_files.append(session_name)

        if not session_files:
            self.logger.warning(f"No session files found in {al_dir}")
            return

        # 添加本地进度条（如果未提供全局进度条）
        local_pbar = None
        if pbar is None:
            local_pbar = tqdm(total=len(session_files), desc=f"Processing {session_dir.name}", position=0, leave=True)

        # Process each session
        success_count = 0
        for session_name in session_files:
            try:
                # 更新进度条
                if pbar:
                    pbar.set_description(f"Processing: {session_dir.name}/{session_name}")
                    pbar.update(1)
                elif local_pbar:
                    local_pbar.set_description(f"Processing: {session_name}")
                    local_pbar.update(1)

                # Find layer files for this session
                layer_files = self._find_layer_files(session_dir, session_name)

                if not layer_files:
                    self.logger.warning(f"No layer files found for session {session_name}")
                    continue

                # Generate output path
                output_path = output_dir / f"{session_name}.png"

                # Generate RGB image
                if self._generate_rgb_image(layer_files, output_path):
                    success_count += 1

            except Exception as e:
                self.logger.error(f"Failed to process session {session_name}: {e}")

        # 关闭本地进度条
        if local_pbar:
            local_pbar.close()

        self.logger.info(f"Successfully generated {success_count}/{len(session_files)} RGB images")

    def process_directory(self):
        """
        Process all session directories with a master progress bar
        """
        if not self.input_base_dir.exists():
            raise FileNotFoundError(f"Input directory not found: {self.input_base_dir}")

        # Find all session directories
        session_dirs = []
        for root, dirs, files in os.walk(self.input_base_dir):
            root_path = Path(root)
            if all((root_path / layer).exists() for layer in ['al', 'l7', 'l5']):
                session_dirs.append(root_path)

        if not session_dirs:
            self.logger.warning(f"No session directories found in {self.input_base_dir}")
            return

        self.logger.info(f"Found {len(session_dirs)} session directories to process")

        # 计算总文件数用于进度条
        total_files = 0
        for session_dir in session_dirs:
            al_dir = session_dir / 'al'
            if al_dir.exists():
                total_files += len([f for f in al_dir.iterdir() if f.is_file() and f.suffix == '.pcap'])

        # 创建主进度条
        master_pbar = tqdm(total=total_files, desc="Overall Progress", position=0, leave=True)

        total_images = 0
        for session_dir in session_dirs:
            try:
                self.logger.info(f"Processing session directory: {session_dir}")
                initial_count = self._count_existing_images()
                self.process_session_directory(session_dir, pbar=master_pbar)
                final_count = self._count_existing_images()
                session_images = final_count - initial_count
                total_images += session_images

            except Exception as e:
                self.logger.error(f"Failed to process session directory {session_dir}: {e}")

        # 关闭主进度条
        master_pbar.close()
        self.logger.info(f"RGB generation completed. Total images generated: {total_images}")

    def _count_existing_images(self):
        """Count existing PNG images in output directory"""
        if not self.output_base_dir.exists():
            return 0
        return len(list(self.output_base_dir.rglob("*.png")))


if __name__ == "__main__":
    # Example usage
    generator = RGBGenerator(
        input_base_dir="./temp/features",
        output_base_dir="./img_dataset",
        image_size=32
    )

    # Process all session directories
    generator.process_directory()