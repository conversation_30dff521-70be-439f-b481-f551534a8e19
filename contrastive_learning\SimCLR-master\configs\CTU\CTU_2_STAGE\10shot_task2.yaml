CUDA_ VISIBLE_DEVICES: 1
train_data_path: "../../data/CTU_10_shot/dataset2"
val_data_path: "../../data/CTU_Processed/CTU_IMG/Test"
pretrained_model_path:  "../../ProtoSimCLR/checkpoints/ProtoNet_pretrained/checkpoint_epoch_600.pth"
save_dir: "./test_performance_logs"
log_name: "CTU_2_stage/task2"
save_name: "test.pth"
img_size: 32
batch_size: 128
learning_rate: 0.001
freeze_train: True
finetune_epochs: 150
freeze_epoch: 20
base_model: "resnet18"
out_dim: 128

transform:
  mean: [0.485, 0.456, 0.406]
  std: [0.229, 0.224, 0.225]