import torch
import torch.nn as nn
from transformers import BertConfig, BertPreTrainedModel, BertModel
from transformers import Trainer, TrainingArguments
from torch.utils.data import Dataset, DataLoader
import numpy as np


# 自定义Transformer配置
class CustomTransformerConfig(BertConfig):
    def __init__(self,
                 input_dim=1024,
                 conv_output_dim=64,
                 output_channels=128,
                 output_embedding_length=16,
                 **kwargs):
        super().__init__(**kwargs)
        # 自定义参数
        self.input_dim = input_dim  # 输入字节流维度 (1024b)
        self.conv_output_dim = conv_output_dim  # 卷积降维后的维度 (32或64)
        self.output_channels = output_channels  # 输出特征向量通道数
        self.output_embedding_length = output_embedding_length  # 输出嵌入长度

        # Transformer参数调整
        self.hidden_size = conv_output_dim  # 隐藏层维度与卷积输出一致
        self.num_hidden_layers = 6  # Transformer层数
        self.num_attention_heads = 8  # 注意力头数
        self.intermediate_size = 256  # FFN维度


# 自定义Transformer模型
class CustomTransformerModel(BertPreTrainedModel):
    def __init__(self, config):
        super().__init__(config)
        self.config = config

        # 输入卷积层 (降维)
        self.conv = nn.Conv1d(
            in_channels=1,  # 输入通道数 (字节流视为单通道)
            out_channels=config.conv_output_dim,
            kernel_size=1,  # 1x1卷积
            stride=1
        )

        # Transformer编码器
        self.transformer = BertModel(config)

        # 输出投影层 (生成指定维度的特征向量)
        self.output_projection = nn.Linear(
            config.hidden_size,
            config.output_channels * config.output_embedding_length
        )

        # 初始化权重
        self.init_weights()

    def forward(self, input_bytes, attention_mask=None):
        """
        前向传播
        :param input_bytes: 输入字节流 [batch_size, seq_len, input_dim]
        :param attention_mask: 注意力掩码 [batch_size, seq_len]
        :return: 特征向量 [batch_size, output_channels, output_embedding_length]
        """
        # 输入形状转换 [batch_size, seq_len, input_dim] -> [batch_size, input_dim, seq_len]
        x = input_bytes.permute(0, 2, 1)

        # 添加通道维度 [batch_size, input_dim, seq_len] -> [batch_size, 1, input_dim, seq_len]
        x = x.unsqueeze(1)

        # 应用卷积降维 [batch_size, 1, input_dim, seq_len] -> [batch_size, conv_output_dim, seq_len]
        x = self.conv(x)

        # 移除通道维度 [batch_size, conv_output_dim, seq_len] -> [batch_size, seq_len, conv_output_dim]
        x = x.squeeze(1).permute(0, 2, 1)

        # Transformer处理
        transformer_outputs = self.transformer(
            inputs_embeds=x,
            attention_mask=attention_mask
        )

        # 获取序列输出 [batch_size, seq_len, hidden_size]
        sequence_output = transformer_outputs[0]

        # 取序列平均作为整体表示 [batch_size, hidden_size]
        pooled_output = sequence_output.mean(dim=1)

        # 投影到目标维度 [batch_size, output_channels * output_embedding_length]
        projected = self.output_projection(pooled_output)

        # 重塑为特征向量 [batch_size, output_channels, output_embedding_length]
        feature_vector = projected.view(
            projected.size(0),
            self.config.output_channels,
            self.config.output_embedding_length
        )

        return feature_vector


# 示例数据集
class ByteDataset(Dataset):
    def __init__(self, num_samples=100, seq_len=128, input_dim=1024):
        self.num_samples = num_samples
        self.seq_len = seq_len
        self.input_dim = input_dim
        self.data = np.random.randint(0, 256, (num_samples, seq_len, input_dim), dtype=np.uint8)
        self.labels = np.random.randint(0, 2, num_samples, dtype=np.long)

    def __len__(self):
        return self.num_samples

    def __getitem__(self, idx):
        return {
            'input_bytes': torch.tensor(self.data[idx], dtype=torch.float32),
            'labels': torch.tensor(self.labels[idx], dtype=torch.long)
        }


# 自定义训练器
class CustomTrainer(Trainer):
    def compute_loss(self, model, inputs, return_outputs=False):
        # 获取输入
        input_bytes = inputs.get("input_bytes")
        labels = inputs.get("labels")

        # 前向传播
        outputs = model(input_bytes)

        # 计算损失 - 这里使用简单的分类任务作为示例
        # 实际应用中应根据您的任务设计损失函数
        logits = outputs.mean(dim=2)  # 平均嵌入维度 [batch_size, output_channels]
        loss_fct = nn.CrossEntropyLoss()
        loss = loss_fct(logits, labels)

        return (loss, outputs) if return_outputs else loss


# 训练函数
def train_model():
    # 创建配置
    config = CustomTransformerConfig(
        input_dim=1024,
        conv_output_dim=64,  # 降维到64维
        output_channels=128,  # 输出128通道
        output_embedding_length=16,  # 每个通道16维嵌入
        vocab_size=1,  # 不使用词汇表
        hidden_dropout_prob=0.1,
        attention_probs_dropout_prob=0.1
    )

    # 初始化模型
    model = CustomTransformerModel(config)

    # 创建数据集
    train_dataset = ByteDataset(num_samples=1000, seq_len=128, input_dim=1024)
    eval_dataset = ByteDataset(num_samples=200, seq_len=128, input_dim=1024)

    # 训练参数
    training_args = TrainingArguments(
        output_dir='./results',
        num_train_epochs=10,
        per_device_train_batch_size=8,
        per_device_eval_batch_size=8,
        warmup_steps=500,
        weight_decay=0.01,
        logging_dir='./logs',
        logging_steps=50,
        evaluation_strategy="epoch",
        save_strategy="epoch",
        load_best_model_at_end=True
    )

    # 创建训练器
    trainer = CustomTrainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=eval_dataset
    )

    # 开始训练
    trainer.train()

    # 保存模型
    model.save_pretrained("./custom_transformer")
    print("Model saved to ./custom_transformer")


# 加载与推理
def load_and_inference():
    # 加载配置和模型
    config = CustomTransformerConfig.from_pretrained("./custom_transformer")
    model = CustomTransformerModel.from_pretrained("./custom_transformer")

    # 设置为评估模式
    model.eval()

    # 创建测试样本
    test_sample = np.random.randint(0, 256, (1, 128, 1024), dtype=np.uint8)
    test_input = torch.tensor(test_sample, dtype=torch.float32)

    # 推理
    with torch.no_grad():
        feature_vector = model(test_input)

    print("\nInput shape:", test_input.shape)
    print("Output feature vector shape:", feature_vector.shape)
    print("Feature vector sample:")
    print(feature_vector[0, :3, :5])  # 打印前3个通道的前5个嵌入值


# 执行训练和推理
if __name__ == "__main__":
    print("=== Training Custom Transformer ===")
    train_model()

    print("\n=== Running Inference ===")
    load_and_inference()