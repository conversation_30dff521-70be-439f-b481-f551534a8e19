# -*- coding: utf-8 -*-
"""
图像分支配置类
"""

from dataclasses import dataclass
from typing import Tuple, List, Optional
from .base_config import BaseConfig


@dataclass
class ImageConfig(BaseConfig):
    """图像分支配置类"""
    
    # 图像数据配置
    image_size: Tuple[int, int] = (32, 32)
    channels: int = 3
    normalize: bool = True
    mean: Tuple[float, float, float] = (0.485, 0.456, 0.406)
    std: Tuple[float, float, float] = (0.229, 0.224, 0.225)
    
    # PCAP到图像转换配置
    max_sessions: int = 6000
    min_file_bytes: int = 199
    max_file_bytes: int = 1024 * 1024  # 1MB
    target_pixels: int = 1024  # 32x32
    
    # 图像增强配置
    use_augmentation: bool = True
    augmentation_params: dict = None
    
    # 编码器配置
    encoder_type: str = "resnet18"  # resnet18, resnet50, efficientnet
    pretrained: bool = True
    feature_dim: int = 512
    dropout_rate: float = 0.1
    
    # 层特征映射配置
    al_layer_channel: str = "R"  # AL层映射到R通道
    l7_layer_channel: str = "G"  # L7层映射到G通道
    l5_layer_channel: str = "B"  # L5层映射到B通道
    
    def __post_init__(self):
        super().__post_init__()
        
        if self.augmentation_params is None:
            self.augmentation_params = {
                "rotation_range": 10,
                "width_shift_range": 0.1,
                "height_shift_range": 0.1,
                "horizontal_flip": True,
                "zoom_range": 0.1,
                "brightness_range": [0.8, 1.2],
                "contrast_range": [0.8, 1.2]
            }
    
    def get_image_shape(self) -> Tuple[int, int, int]:
        """获取图像形状"""
        return (*self.image_size, self.channels)
    
    def validate(self) -> bool:
        """验证图像配置参数"""
        super().validate()
        
        # 验证图像尺寸
        if len(self.image_size) != 2 or any(s <= 0 for s in self.image_size):
            raise ValueError("图像尺寸必须为正整数的二元组")
        
        # 验证通道数
        if self.channels not in [1, 3]:
            raise ValueError("通道数必须为1或3")
        
        # 验证编码器类型
        supported_encoders = ["resnet18", "resnet50", "efficientnet", "vgg16", "densenet"]
        if self.encoder_type not in supported_encoders:
            raise ValueError(f"不支持的编码器类型: {self.encoder_type}")
        
        # 验证特征维度
        if self.feature_dim <= 0:
            raise ValueError("特征维度必须为正整数")
        
        # 验证dropout率
        if not 0 <= self.dropout_rate <= 1:
            raise ValueError("Dropout率必须在[0, 1]范围内")
        
        return True
