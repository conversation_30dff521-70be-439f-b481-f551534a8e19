import json
from scapy.all import rdpcap
from scapy.layers.inet import IP, TCP, UDP


def enhanced_packet_to_dict(packet):
    """增强版数据包特征提取"""
    packet_dict = {
        'time': float(packet.time) if hasattr(packet, 'time') else 0,
        'len': len(packet),
        'raw': str(packet)  # 原始包信息
    }

    # 数据链路层信息
    if hasattr(packet, 'src'):
        packet_dict['eth_src'] = packet.src
    if hasattr(packet, 'dst'):
        packet_dict['eth_dst'] = packet.dst

    # 网络层信息 (IP)
    if IP in packet:
        ip = packet[IP]
        packet_dict.update({
            'ip': {
                'src': ip.src,
                'dst': ip.dst,
                'proto': ip.proto,
                'ttl': ip.ttl,
                'len': ip.len,  # IP包长度
                'id': ip.id,  # IP标识
                'flags': str(ip.flags) if hasattr(ip, 'flags') else None,
                'chksum': ip.chksum  # 校验和
            }
        })

        # 传输层信息 (TCP/UDP)
        if TCP in packet:
            tcp = packet[TCP]
            packet_dict['transport'] = {
                'type': 'tcp',
                'sport': tcp.sport,
                'dport': tcp.dport,
                'flags': str(tcp.flags),
                'seq': tcp.seq,  # 序列号
                'ack': tcp.ack,  # 确认号
                'window': tcp.window,  # 窗口大小
                'chksum': tcp.chksum,  # TCP校验和
                'urgptr': tcp.urgptr,  # 紧急指针
                'options': str(tcp.options) if tcp.options else None  # TCP选项
            }
        elif UDP in packet:
            udp = packet[UDP]
            packet_dict['transport'] = {
                'type': 'udp',
                'sport': udp.sport,
                'dport': udp.dport,
                'len': udp.len,  # UDP长度
                'chksum': udp.chksum  # UDP校验和
            }

    # 应用层信息（如果存在）
    # 可以根据具体协议提取更多特征

    return packet_dict


def packet_to_dict(packet):
    """将单个包转换为字典格式"""
    print(packet)
    packet_dict = {
        'time': float(packet.time),  # 时间戳
        'len': len(packet),  # 包长度
    }

    # 网络层信息 (IP)
    if IP in packet:
        ip = packet[IP]
        packet_dict['ip'] = {
            'src': ip.src,
            'dst': ip.dst,
            'proto': ip.proto,
            'ttl': ip.ttl
        }

        # 传输层信息 (TCP/UDP)
        if TCP in packet:
            tcp = packet[TCP]
            packet_dict['transport'] = {
                'type': 'tcp',
                'sport': tcp.sport,
                'dport': tcp.dport,
                'flags': str(tcp.flags)
            }
        elif UDP in packet:
            udp = packet[UDP]
            packet_dict['transport'] = {
                'type': 'udp',
                'sport': udp.sport,
                'dport': udp.dport
            }

    return packet_dict


def pcap_to_json(pcap_path, output_path, max_packets=None):
    """将PCAP文件转换为JSON序列"""
    packets = rdpcap(pcap_path)
    packet_list = []

    for i, pkt in enumerate(packets):
        if max_packets and i >= max_packets:
            break
        packet_list.append(packet_to_dict(pkt))

    with open(output_path, 'w') as f:
        json.dump(packet_list, f, indent=2)


if __name__ == '__main__':
    import sys

    if len(sys.argv) != 3:
        print("Usage: python pcap2json.py input.pcap output.json")
        sys.exit(1)

    pcap_to_json(sys.argv[1], sys.argv[2])
    print(f"Converted {sys.argv[1]} to JSON sequence at {sys.argv[2]}")