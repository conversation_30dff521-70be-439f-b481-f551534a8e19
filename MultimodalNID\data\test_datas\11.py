import json
import numpy as np
from scapy.all import rdpcap, IP, TCP, UDP
import sys


def packet_to_feature_vector(packet):
    """
    将单个数据包转换为固定长度的特征向量
    返回一个数值数组，作为序列数据的一个时间步
    """
    # 初始化特征向量
    features = []

    # 1. 基本包特征
    features.append(len(packet))  # 包长度
    features.append(float(packet.time) if hasattr(packet, 'time') else 0)  # 时间戳

    # 2. IP层特征 (如果没有IP层，填充默认值)
    if IP in packet:
        ip = packet[IP]
        features.extend([
            ip.len if hasattr(ip, 'len') else len(packet),  # IP长度
            ip.ttl,  # TTL
            ip.proto,  # 协议
            1 if ip.flags.DF else 0,  # DF标志位
            1 if ip.flags.MF else 0,  # MF标志位
        ])
        # 将IP地址转换为数值 (简化处理)
        src_ip_parts = [int(x) for x in ip.src.split('.')] if hasattr(ip, 'src') else [0, 0, 0, 0]
        dst_ip_parts = [int(x) for x in ip.dst.split('.')] if hasattr(ip, 'dst') else [0, 0, 0, 0]
        features.extend(src_ip_parts)
        features.extend(dst_ip_parts)
    else:
        # 无IP层时填充默认值
        features.extend([0, 64, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0])

    # 3. 传输层特征
    has_tcp, has_udp = 0, 0
    sport, dport = 0, 0
    tcp_flags = [0] * 8  # TCP标志位: FIN, SYN, RST, PSH, ACK, URG, ECE, CWR

    if TCP in packet:
        tcp = packet[TCP]
        has_tcp = 1
        sport, dport = tcp.sport, tcp.dport
        # 解析TCP标志位
        flags = int(tcp.flags) if hasattr(tcp.flags, 'value') else int(str(tcp.flags), 16) if str(
            tcp.flags).isdigit() == False else int(tcp.flags)
        for i in range(8):
            tcp_flags[i] = (flags >> i) & 1
        features.extend([tcp.seq if hasattr(tcp, 'seq') else 0, tcp.ack if hasattr(tcp, 'ack') else 0])
    elif UDP in packet:
        udp = packet[UDP]
        has_udp = 1
        sport, dport = udp.sport, udp.dport
        features.extend([0, 0])  # UDP没有seq和ack
    else:
        features.extend([0, 0])  # 无传输层

    features.extend([has_tcp, has_udp, sport, dport])
    features.extend(tcp_flags)

    return np.array(features, dtype=np.float32)


def packets_to_sequence(pcap_path, max_packets=100):
    """
    将PCAP文件中的数据包转换为序列数据
    每行代表一个数据包，每列代表一个特征
    """
    packets = rdpcap(pcap_path)

    # 限制数据包数量
    if max_packets and len(packets) > max_packets:
        packets = packets[:max_packets]

    # 转换每个数据包为特征向量
    sequence = []
    for pkt in packets:
        feature_vector = packet_to_feature_vector(pkt)
        sequence.append(feature_vector)

    # 转换为numpy数组
    if sequence:
        sequence_array = np.array(sequence)
        return sequence_array
    else:
        return np.array([])


def extract_flow_features(sequence):
    """
    从数据包序列中提取流级别特征
    """
    if len(sequence) == 0:
        return np.array([])

    # 计算统计特征
    mean_features = np.mean(sequence, axis=0)
    std_features = np.std(sequence, axis=0)
    min_features = np.min(sequence, axis=0)
    max_features = np.max(sequence, axis=0)
    sum_features = np.sum(sequence, axis=0)

    # 组合流特征
    flow_features = np.concatenate([
        mean_features, std_features, min_features, max_features, sum_features
    ])

    return flow_features


def extract_statistical_features(sequence):
    """
    提取统计特征向量
    """
    if len(sequence) == 0:
        return np.array([])

    # 基本统计信息
    total_packets = len(sequence)
    total_bytes = np.sum(sequence[:, 0]) if len(sequence) > 0 else 0  # 基于包长度列

    # 包大小统计
    packet_sizes = sequence[:, 0]  # 第一列是包长度
    avg_packet_size = np.mean(packet_sizes)
    std_packet_size = np.std(packet_sizes)
    min_packet_size = np.min(packet_sizes)
    max_packet_size = np.max(packet_sizes)

    # 时间间隔统计 (如果时间戳可用)
    if len(sequence) > 1:
        time_stamps = sequence[:, 1]  # 第二列是时间戳
        time_intervals = np.diff(time_stamps)
        avg_time_interval = np.mean(time_intervals)
        std_time_interval = np.std(time_intervals)
    else:
        avg_time_interval = std_time_interval = 0

    statistical_features = np.array([
        total_packets, total_bytes, avg_packet_size, min_packet_size,
        max_packet_size, std_packet_size, avg_time_interval, std_time_interval
    ])

    return statistical_features


def process_pcap_to_features(pcap_path, output_path=None, max_packets=100):
    """
    处理PCAP文件并提取所有特征
    """
    print(f"处理PCAP文件: {pcap_path}")

    # 1. 转换为序列数据
    sequence = packets_to_sequence(pcap_path, max_packets)
    print(f"序列数据形状: {sequence.shape}")

    # 2. 提取流特征
    flow_features = extract_flow_features(sequence)
    print(f"流特征形状: {flow_features.shape}")

    # 3. 提取统计特征
    statistical_features = extract_statistical_features(sequence)
    print(f"统计特征形状: {statistical_features.shape}")

    # 4. 保存结果
    result = {
        "sequence": str(sequence.tolist()) if sequence.size > 0 else "[]",
        "packet_features": str(sequence.tolist()) if sequence.size > 0 else "[]",
        "flow_features": str(flow_features.tolist()) if flow_features.size > 0 else "[]",
        "statistical_features": str(statistical_features.tolist()) if statistical_features.size > 0 else "[]"
    }

    if output_path:
        with open(output_path, 'w') as f:
            json.dump(result, f, indent=2)
        print(f"特征已保存到: {output_path}")

    return result


def main():
    if len(sys.argv) < 2:
        print("用法: python script.py input.pcap [output.json] [max_packets]")
        print("示例: python script.py data.pcap output.json 100")
        return

    pcap_path = sys.argv[1]
    output_path = sys.argv[2] if len(sys.argv) > 2 else None
    max_packets = int(sys.argv[3]) if len(sys.argv) > 3 else 100

    result = process_pcap_to_features(pcap_path, output_path, max_packets)

    # 打印部分结果预览
    print("\n=== 特征预览 ===")
    for key, value in result.items():
        print(f"{key}: {value[:100]}..." if len(value) > 100 else f"{key}: {value}")


if __name__ == "__main__":
    main()
