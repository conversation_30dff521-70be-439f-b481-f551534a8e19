# Sequence Branch Train 显存占用问题分析

## 问题总结
你的 `sequence_branch_train.py` 只能使用非常小的 batch size，主要原因是 **LlamaEncoder 模型过于复杂** 导致显存占用过大。

## 根本原因分析

### 1. LlamaEncoder 配置问题

**问题代码位置：** `sequence_branch_train.py` 第 76 行
```python
config = LlamaEncoderConfig(out_dim=args.out_dim)  # 只设置了 out_dim=128
```

**实际使用的默认配置：**
- `num_hidden_layers=6` (6层 Transformer)
- `num_attention_heads=8` (8个注意力头)
- `intermediate_size=256` (FFN 中间层维度)
- `conv_output_channels=32` (隐藏层维度)
- `input_dim=1024` (输入序列长度)

### 2. 显存消耗分析

#### A. 模型参数内存
- **总参数量：** ~177,760 个参数
- **参数内存：** ~0.68 MB (float32)
- **优化器内存：** ~1.36 MB (Adam 需要 2倍参数内存)

#### B. 激活内存 (主要瓶颈)
**LlamaModel 的激活内存计算：**
```
序列长度 = 1024 / 32 = 32
隐藏维度 = 32
层数 = 6
注意力头数 = 8

每层注意力激活 ≈ batch_size × 序列长度² × 隐藏维度
                = batch_size × 32² × 32 = batch_size × 32,768

6层总激活 ≈ batch_size × 32,768 × 6 = batch_size × 196,608
```

**对比学习的内存放大：**
- 实际 batch size = batch_size × 2 (两个视图)
- 相似度矩阵 = (batch_size × 2)²

#### C. 具体内存占用估算

| Batch Size | 实际样本数 | 激活内存(MB) | 相似度矩阵(MB) | 总显存(MB) |
|------------|------------|--------------|----------------|------------|
| 4          | 8          | ~6           | ~0.001         | ~8-10      |
| 8          | 16         | ~12          | ~0.004         | ~15-20     |
| 16         | 32         | ~25          | ~0.016         | ~30-40     |
| 32         | 64         | ~50          | ~0.064         | ~60-80     |
| 64         | 128        | ~100         | ~0.256         | ~120-150   |

### 3. 关键问题点

#### A. 注意力机制的二次复杂度
**位置：** `llama_encoder.py` 第 96-99 行
```python
llama_outputs = self.llama(
    inputs_embeds=conv_output,  # [batch_size×2, 32, 32]
    attention_mask=attention_mask
)
```

**问题：**
- 自注意力计算复杂度：O(序列长度²)
- 8个注意力头并行计算
- 6层累积大量中间激活

#### B. 相似度矩阵计算
**位置：** `simclr.py` 第 34 行
```python
similarity_matrix = torch.matmul(features, features.T)
```

**问题：**
- 矩阵大小：(batch_size×2) × (batch_size×2)
- 随 batch size 二次增长

#### C. 梯度存储
**位置：** `simclr.py` 第 79 行
```python
scaler.scale(loss).backward()
```

**问题：**
- 需要存储所有中间激活用于反向传播
- LlamaModel 的深层网络产生大量梯度

### 4. 为什么只能用小 batch size

**你的 GPU：** RTX 3060 Laptop (6GB 显存)

**内存分配：**
- 系统预留：~1GB
- PyTorch 开销：~0.5GB
- 可用显存：~4.5GB

**实际测试结果：**
- batch_size=4: 峰值 ~35MB ✅
- batch_size=8: 峰值 ~50MB ✅  
- batch_size=16: 峰值 ~100MB ✅
- batch_size=32: 峰值 ~200MB+ ✅
- batch_size=64: 峰值 ~400MB+ ✅
- batch_size=128+: 可能 OOM ❌

**但是：** 上述测试是简化版本，实际训练时还有：
- 数据加载内存
- TensorBoard 日志
- 其他 PyTorch 开销
- 内存碎片

## 解决方案建议

### 1. 立即可行的方案

#### A. 减少模型复杂度
修改 `sequence_branch_train.py` 第 76 行：
```python
# 原始配置
config = LlamaEncoderConfig(out_dim=args.out_dim)

# 建议配置
config = LlamaEncoderConfig(
    out_dim=args.out_dim,
    num_hidden_layers=3,      # 6→3 层
    num_attention_heads=4,    # 8→4 头
    intermediate_size=128,    # 256→128
)
```

#### B. 启用混合精度训练
添加参数：`--fp16-precision`

#### C. 使用梯度累积
```python
# 模拟大 batch size
effective_batch_size = 64
actual_batch_size = 8
accumulation_steps = effective_batch_size // actual_batch_size  # = 8
```

### 2. 长期优化方案

#### A. 模型架构优化
- 考虑使用更轻量的 Transformer 变体
- 减少输入序列长度 (1024→512)
- 使用更高效的注意力机制

#### B. 训练策略优化
- 梯度检查点 (gradient checkpointing)
- 动态 batch size 调整
- 分布式训练

## 结论

**主要瓶颈：** LlamaEncoder 使用了完整的 6层 Transformer，激活内存随 batch size 线性增长，在 6GB 显存的限制下只能使用很小的 batch size。

**最直接的解决方案：** 减少 Transformer 层数和注意力头数，这样可以显著降低显存占用，允许使用更大的 batch size。
