# ZhaoYingwei项目使用教程

## 📁 项目概述

本项目包含两个主要模块：
1. **Renet_new**: ResNet网络的实现和训练框架，用于图像分类任务
2. **improved_diffusion**: 改进的扩散模型实现，用于图像生成和超分辨率任务

## 🏗️ 项目结构

```
zhaoyingwei/
├── Renet_new/                    # ResNet分类模型
│   ├── renet_model.py           # 标准ResNet模型实现
│   ├── renet_model_improved.py  # 改进版ResNet模型
│   ├── renet_model_improved_v2.py # 进一步改进版本
│   ├── renet_train.py           # 训练脚本
│   ├── renet_predict.py         # 预测和评估脚本
│   └── class_indices.json       # 类别索引文件
└── improved_diffusion/           # 扩散模型
    ├── scripts/                 # 训练和采样脚本
    │   ├── image_train.py       # 图像扩散模型训练
    │   ├── image_sample.py      # 图像生成采样
    │   ├── super_res_train.py   # 超分辨率训练
    │   └── super_res_sample.py  # 超分辨率采样
    ├── gaussian_diffusion.py    # 高斯扩散核心实现
    ├── unet.py                  # U-Net模型架构
    ├── train_util.py            # 训练工具函数
    └── script_util.py           # 脚本工具函数
```

## 🚀 Part 1: ResNet分类模型使用指南

### 1.1 环境准备

```bash
# 安装依赖
pip install torch torchvision
pip install tqdm prettytable
pip install scikit-learn matplotlib
pip install pillow numpy
```

### 1.2 数据准备

数据集应按以下结构组织：
```
datasets/USTC-gray-splite/
├── train/
│   ├── 0/          # 类别0的训练图像
│   ├── 1/          # 类别1的训练图像
│   └── ...
└── val/
    ├── 0/          # 类别0的验证图像
    ├── 1/          # 类别1的验证图像
    └── ...
```

### 1.3 模型训练

#### 基本训练
```bash
cd Renet_new
python renet_train.py
```

#### 训练配置说明
在 `renet_train.py` 中可以调整的主要参数：

```python
# 模型选择
net = resnet34()        # ResNet-34
# net = resnet50()      # ResNet-50
# net = resnet101()     # ResNet-101

# 训练参数
batch_size = 16         # 批次大小
epochs = 3              # 训练轮数
EP = 5                  # 重复训练次数
lr = 0.0001            # 学习率

# 数据路径
image_path = "../datasets/USTC-gray-splite"
```

#### 预训练模型使用
```python
# 加载预训练权重
model_weight_path = "./resNet34_new8.pth"
net.load_state_dict(torch.load(model_weight_path, map_location=device))

# 冻结部分参数进行微调
for param in net.parameters():
    param.requires_grad = False
# 只训练分类器层
net.fc.requires_grad = True
```

### 1.4 模型预测和评估

#### 单张图像预测
```bash
python renet_predict.py
```

#### 评估功能
- **混淆矩阵**: 自动生成分类混淆矩阵
- **性能指标**: 计算Precision、Recall、F1-Score、FPR
- **ROC-AUC**: 计算多类别ROC-AUC分数
- **可视化**: 生成混淆矩阵热图

#### 批量评估示例
```python
# 在renet_predict.py中修改路径
weights_path = "./resnet-model-{}.pth".format(i)  # 模型权重路径
test_data_path = "../datasets/test/"               # 测试数据路径

# 运行评估
python renet_predict.py
```

### 1.5 模型架构选择

| 模型 | 参数量 | 适用场景 |
|------|--------|----------|
| ResNet-34 | 21.8M | 中等复杂度任务，训练速度快 |
| ResNet-50 | 25.6M | 高精度要求，标准选择 |
| ResNet-101 | 44.5M | 复杂任务，需要更深网络 |
| ResNeXt-50 | 25.0M | 提升精度，计算效率高 |
| ResNeXt-101 | 44.2M | 最高精度要求 |

## 🎨 Part 2: 扩散模型使用指南

### 2.1 环境准备

```bash
# 安装额外依赖
pip install blobfile
pip install mpi4py  # 用于分布式训练
```

### 2.2 图像生成模型

#### 训练扩散模型
```bash
cd improved_diffusion/scripts

# 基础图像生成训练
python image_train.py \
    --data_dir /path/to/your/dataset \
    --image_size 64 \
    --num_channels 128 \
    --num_res_blocks 2 \
    --diffusion_steps 1000 \
    --noise_schedule linear \
    --lr 1e-4 \
    --batch_size 16
```

#### 主要训练参数说明

| 参数 | 说明 | 推荐值 |
|------|------|--------|
| `--image_size` | 生成图像尺寸 | 64, 128, 256 |
| `--num_channels` | U-Net通道数 | 128, 192, 256 |
| `--num_res_blocks` | 残差块数量 | 2, 3 |
| `--diffusion_steps` | 扩散步数 | 1000 |
| `--noise_schedule` | 噪声调度 | linear, cosine |
| `--learn_sigma` | 学习方差 | True/False |
| `--class_cond` | 类别条件生成 | True/False |

#### 图像采样生成
```bash
python image_sample.py \
    --model_path /path/to/trained/model.pt \
    --num_samples 1000 \
    --batch_size 16 \
    --image_size 64
```

### 2.3 超分辨率模型

#### 训练超分辨率模型
```bash
python super_res_train.py \
    --data_dir /path/to/dataset \
    --large_size 256 \
    --small_size 64 \
    --num_channels 128 \
    --lr 1e-4 \
    --batch_size 8
```

#### 超分辨率采样
```bash
python super_res_sample.py \
    --model_path /path/to/sr_model.pt \
    --base_samples /path/to/low_res_images.npz \
    --large_size 256 \
    --small_size 64
```

### 2.4 高级配置

#### 分布式训练
```bash
# 多GPU训练
mpiexec -n 4 python image_train.py [参数]
```

#### 混合精度训练
```bash
python image_train.py \
    --use_fp16 True \
    --fp16_scale_growth 1e-3 \
    [其他参数]
```

#### 检查点恢复
```bash
python image_train.py \
    --resume_checkpoint /path/to/checkpoint.pt \
    [其他参数]
```

## 📊 性能优化建议

### ResNet模型优化
1. **数据增强**: 使用RandomHorizontalFlip、ColorJitter等
2. **学习率调度**: 使用余弦退火或步长衰减
3. **批次大小**: 根据GPU内存调整，通常16-64
4. **预训练**: 使用ImageNet预训练权重

### 扩散模型优化
1. **采样步数**: 训练时1000步，推理时可减少到50-250步
2. **模型尺寸**: 根据数据复杂度选择合适的通道数
3. **噪声调度**: cosine调度通常比linear效果更好
4. **EMA**: 使用指数移动平均提升生成质量

## 🔧 常见问题解决

### 1. 内存不足
```python
# 减少批次大小
batch_size = 8  # 或更小

# 使用梯度累积
accumulation_steps = 4
```

### 2. 训练不收敛
```python
# 调整学习率
lr = 1e-5  # 降低学习率

# 使用预训练模型
net.load_state_dict(torch.load(pretrained_path))
```

### 3. GPU设置
```python
# 指定GPU
os.environ['CUDA_VISIBLE_DEVICES'] = '0'  # 使用第0号GPU
device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
```

## 📈 实验建议

### ResNet实验
1. **消融实验**: 比较不同深度的ResNet性能
2. **数据增强**: 测试不同增强策略的效果
3. **迁移学习**: 比较从头训练vs预训练微调

### 扩散模型实验
1. **采样质量**: 使用FID、IS等指标评估
2. **生成多样性**: 测试不同随机种子的生成结果
3. **条件生成**: 实验类别条件生成效果

## 📝 引用和参考

如果使用本项目，请引用相关论文：
- ResNet: "Deep Residual Learning for Image Recognition"
- Diffusion Models: "Denoising Diffusion Probabilistic Models"
- Improved Diffusion: "Improved Denoising Diffusion Probabilistic Models"

## 💡 实际使用示例

### ResNet分类完整流程

#### 1. 准备数据集
```bash
# 假设你有一个7类分类任务
mkdir -p datasets/USTC-gray-splite/{train,val}/{0,1,2,3,4,5,6}

# 将图像文件放入对应目录
# train/0/ - 类别0的训练图像
# train/1/ - 类别1的训练图像
# ...
```

#### 2. 修改训练脚本
```python
# 在renet_train.py中修改关键参数
num_classes = 7  # 你的类别数
epochs = 50      # 增加训练轮数
batch_size = 32  # 根据GPU内存调整

# 选择合适的模型
net = resnet34(num_classes=num_classes)  # 推荐起始选择
```

#### 3. 运行训练
```bash
cd Renet_new
python renet_train.py
```

#### 4. 评估模型
```python
# 修改renet_predict.py中的模型路径
weights_path = "./resnet-USTC-gray-splite-0.pth"  # 最佳模型

# 运行评估
python renet_predict.py
```

### 扩散模型生成流程

#### 1. 准备图像数据
```bash
# 数据格式：所有图像放在一个目录下
mkdir -p datasets/my_images
# 将所有训练图像放入该目录
```

#### 2. 训练基础扩散模型
```bash
cd improved_diffusion/scripts

python image_train.py \
    --data_dir ../../datasets/my_images \
    --image_size 64 \
    --num_channels 128 \
    --num_res_blocks 2 \
    --diffusion_steps 1000 \
    --lr 1e-4 \
    --batch_size 16 \
    --save_interval 10000 \
    --log_interval 100
```

#### 3. 生成新图像
```bash
python image_sample.py \
    --model_path ./model010000.pt \
    --num_samples 100 \
    --batch_size 10 \
    --image_size 64
```

## 🎯 最佳实践指南

### ResNet训练最佳实践

1. **数据预处理**
```python
# 推荐的数据变换
data_transform = {
    "train": transforms.Compose([
        transforms.Resize(256),
        transforms.RandomResizedCrop(224),
        transforms.RandomHorizontalFlip(p=0.5),
        transforms.ColorJitter(brightness=0.2, contrast=0.2),
        transforms.ToTensor(),
        transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
    ]),
    "val": transforms.Compose([
        transforms.Resize(256),
        transforms.CenterCrop(224),
        transforms.ToTensor(),
        transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
    ])
}
```

2. **学习率调度**
```python
# 添加学习率调度器
scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=epochs)

# 在训练循环中使用
for epoch in range(epochs):
    # ... 训练代码 ...
    scheduler.step()
```

3. **早停机制**
```python
# 实现早停
patience = 10
best_val_acc = 0
patience_counter = 0

for epoch in range(epochs):
    # ... 训练和验证 ...

    if val_accuracy > best_val_acc:
        best_val_acc = val_accuracy
        patience_counter = 0
        torch.save(model.state_dict(), 'best_model.pth')
    else:
        patience_counter += 1

    if patience_counter >= patience:
        print("Early stopping triggered")
        break
```

### 扩散模型训练最佳实践

1. **渐进式训练**
```bash
# 先在小尺寸上训练
python image_train.py --image_size 32 --diffusion_steps 1000

# 然后在大尺寸上微调
python image_train.py --image_size 64 --resume_checkpoint model.pt
```

2. **采样质量优化**
```python
# 使用DDIM采样加速
python image_sample.py \
    --timestep_respacing "ddim25" \  # 25步DDIM采样
    --model_path model.pt
```

3. **条件生成**
```bash
# 训练类别条件模型
python image_train.py \
    --class_cond True \
    --num_classes 10 \
    [其他参数]
```

## 📋 性能基准

### ResNet性能参考

| 数据集 | 模型 | Top-1 Acc | 训练时间 | GPU内存 |
|--------|------|-----------|----------|---------|
| CIFAR-10 | ResNet-34 | ~95% | 2小时 | 4GB |
| CIFAR-100 | ResNet-50 | ~78% | 3小时 | 6GB |
| ImageNet | ResNet-101 | ~77% | 3天 | 16GB |

### 扩散模型性能参考

| 图像尺寸 | 模型参数 | FID分数 | 训练时间 | GPU内存 |
|----------|----------|---------|----------|---------|
| 32x32 | 35M | ~10 | 1天 | 8GB |
| 64x64 | 55M | ~15 | 3天 | 16GB |
| 128x128 | 100M | ~20 | 7天 | 32GB |

## 🚨 注意事项

1. **硬件要求**
   - ResNet训练：至少4GB GPU内存
   - 扩散模型训练：至少8GB GPU内存
   - 推荐使用RTX 3080或更高配置

2. **数据要求**
   - 图像质量要高，避免模糊或损坏的图像
   - 类别分布尽量均衡
   - 扩散模型建议至少10K张图像

3. **训练稳定性**
   - 监控损失曲线，避免过拟合
   - 定期保存检查点
   - 使用验证集评估模型性能

---

**注意**: 使用前请确保数据路径、模型路径等配置正确，并根据实际硬件资源调整训练参数。建议先在小数据集上测试流程，确认无误后再进行大规模训练。
