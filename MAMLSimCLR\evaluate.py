"""
模型评估脚本
"""

import os
import sys
import torch
import numpy as np
from tqdm import tqdm
import argparse
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns

# 添加当前目录到路径
sys.path.append(os.path.dirname(__file__))

from config import get_config
from maml_with_simclr import create_maml_learner
from network_traffic_dataset import create_few_shot_dataloader


def evaluate_model(maml_learner, dataloader, config):
    """
    评估模型性能
    """
    maml_learner.model.eval()
    
    all_predictions = []
    all_targets = []
    episode_accuracies = []
    
    print("Evaluating model...")
    
    with torch.no_grad():
        for batch_idx, (support_x, support_y, query_x, query_y) in enumerate(tqdm(dataloader)):
            batch_size = support_x.size(0)
            
            for i in range(batch_size):
                # 获取单个任务的数据
                s_x = support_x[i:i+1]
                s_y = support_y[i:i+1]
                q_x = query_x[i:i+1]
                q_y = query_y[i:i+1]
                
                # 进行微调和预测
                accuracy = maml_learner.finetune(s_x, s_y, q_x, q_y)
                episode_accuracies.append(accuracy)
                
                # 获取详细预测结果
                predictions = get_detailed_predictions(maml_learner, s_x, s_y, q_x, q_y)
                
                all_predictions.extend(predictions)
                all_targets.extend(q_y[0].cpu().numpy())
    
    return np.array(all_predictions), np.array(all_targets), episode_accuracies


def get_detailed_predictions(maml_learner, support_x, support_y, query_x, query_y):
    """
    获取详细的预测结果
    """
    maml_learner.model.eval()
    
    s_x = support_x.to(maml_learner.device)
    s_y = support_y.to(maml_learner.device)
    q_x = query_x.to(maml_learner.device)
    
    # 获取快速权重
    fast_weights = list(maml_learner.model.get_classifier_params())
    
    # 内循环更新
    for k in range(maml_learner.update_step_test):
        logits_spt = maml_learner.model(s_x)
        loss_spt = torch.nn.functional.cross_entropy(logits_spt, s_y)
        
        grad = torch.autograd.grad(loss_spt, fast_weights)
        fast_weights = [p - maml_learner.update_lr * g for p, g in zip(fast_weights, grad)]
    
    # 在查询集上预测
    with torch.no_grad():
        logits_q = maml_learner.model.functional_forward(
            maml_learner.model.feature_extractor(q_x), 
            fast_weights
        )
        predictions = torch.nn.functional.softmax(logits_q, dim=1).argmax(dim=1)
    
    return predictions[0].cpu().numpy().tolist()


def calculate_metrics(predictions, targets):
    """
    计算评估指标
    """
    accuracy = accuracy_score(targets, predictions)
    precision = precision_score(targets, predictions, average='weighted', zero_division=0)
    recall = recall_score(targets, predictions, average='weighted', zero_division=0)
    f1 = f1_score(targets, predictions, average='weighted', zero_division=0)
    
    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1
    }


def plot_confusion_matrix(targets, predictions, class_names, save_path):
    """
    绘制混淆矩阵
    """
    cm = confusion_matrix(targets, predictions)
    
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                xticklabels=class_names, yticklabels=class_names)
    plt.title('Confusion Matrix')
    plt.xlabel('Predicted Label')
    plt.ylabel('True Label')
    plt.tight_layout()
    plt.savefig(save_path)
    plt.close()
    
    print(f"Confusion matrix saved to {save_path}")


def plot_accuracy_distribution(episode_accuracies, save_path):
    """
    绘制准确率分布
    """
    plt.figure(figsize=(10, 6))
    
    plt.subplot(1, 2, 1)
    plt.hist(episode_accuracies, bins=20, alpha=0.7, edgecolor='black')
    plt.xlabel('Episode Accuracy')
    plt.ylabel('Frequency')
    plt.title('Distribution of Episode Accuracies')
    plt.grid(True, alpha=0.3)
    
    plt.subplot(1, 2, 2)
    plt.plot(episode_accuracies)
    plt.xlabel('Episode')
    plt.ylabel('Accuracy')
    plt.title('Episode Accuracies Over Time')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(save_path)
    plt.close()
    
    print(f"Accuracy distribution plot saved to {save_path}")


def print_evaluation_results(metrics, episode_accuracies):
    """
    打印评估结果
    """
    print("\n" + "="*50)
    print("EVALUATION RESULTS")
    print("="*50)
    
    print(f"Overall Accuracy: {metrics['accuracy']:.4f}")
    print(f"Precision: {metrics['precision']:.4f}")
    print(f"Recall: {metrics['recall']:.4f}")
    print(f"F1 Score: {metrics['f1_score']:.4f}")
    
    print(f"\nEpisode Statistics:")
    print(f"Mean Episode Accuracy: {np.mean(episode_accuracies):.4f} ± {np.std(episode_accuracies):.4f}")
    print(f"Min Episode Accuracy: {np.min(episode_accuracies):.4f}")
    print(f"Max Episode Accuracy: {np.max(episode_accuracies):.4f}")
    print(f"Median Episode Accuracy: {np.median(episode_accuracies):.4f}")
    
    print("="*50)


def main():
    """
    主评估函数
    """
    parser = argparse.ArgumentParser(description='Evaluate SimCLR+MAML model')
    parser.add_argument('--model_path', type=str, required=True,
                        help='Path to the trained model checkpoint')
    parser.add_argument('--output_dir', type=str, default='./evaluation_results',
                        help='Directory to save evaluation results')
    
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 获取配置（这里需要从检查点加载或使用默认配置）
    config = get_config()
    
    # 设置设备
    device = torch.device(config.device if torch.cuda.is_available() else 'cpu')
    config.device = device
    print(f"Using device: {device}")
    
    # 创建测试数据加载器
    print("Creating test data loader...")
    test_loader = create_few_shot_dataloader(config, split='test')
    
    # 创建模型
    print("Creating MAML learner...")
    maml_learner = create_maml_learner(config)
    
    # 加载模型权重
    print(f"Loading model from {args.model_path}")
    if os.path.exists(args.model_path):
        checkpoint = torch.load(args.model_path, map_location=device)
        maml_learner.model.load_state_dict(checkpoint['model_state_dict'])
        print(f"Model loaded successfully. Best accuracy: {checkpoint.get('best_acc', 'N/A')}")
    else:
        print(f"Error: Model file {args.model_path} not found!")
        return
    
    # 评估模型
    predictions, targets, episode_accuracies = evaluate_model(maml_learner, test_loader, config)
    
    # 计算指标
    metrics = calculate_metrics(predictions, targets)
    
    # 打印结果
    print_evaluation_results(metrics, episode_accuracies)
    
    # 保存结果
    results_file = os.path.join(args.output_dir, 'evaluation_results.txt')
    with open(results_file, 'w') as f:
        f.write("EVALUATION RESULTS\n")
        f.write("="*50 + "\n")
        f.write(f"Overall Accuracy: {metrics['accuracy']:.4f}\n")
        f.write(f"Precision: {metrics['precision']:.4f}\n")
        f.write(f"Recall: {metrics['recall']:.4f}\n")
        f.write(f"F1 Score: {metrics['f1_score']:.4f}\n")
        f.write(f"\nEpisode Statistics:\n")
        f.write(f"Mean Episode Accuracy: {np.mean(episode_accuracies):.4f} ± {np.std(episode_accuracies):.4f}\n")
        f.write(f"Min Episode Accuracy: {np.min(episode_accuracies):.4f}\n")
        f.write(f"Max Episode Accuracy: {np.max(episode_accuracies):.4f}\n")
        f.write(f"Median Episode Accuracy: {np.median(episode_accuracies):.4f}\n")
    
    print(f"Results saved to {results_file}")
    
    # 绘制图表
    class_names = ['Normal', 'Abnormal']
    
    # 混淆矩阵
    cm_path = os.path.join(args.output_dir, 'confusion_matrix.png')
    plot_confusion_matrix(targets, predictions, class_names, cm_path)
    
    # 准确率分布
    acc_dist_path = os.path.join(args.output_dir, 'accuracy_distribution.png')
    plot_accuracy_distribution(episode_accuracies, acc_dist_path)
    
    print("Evaluation completed successfully!")


if __name__ == '__main__':
    main()
