"""
网络流量数据集处理模块
支持多种数据集格式
"""

import os
import torch
import numpy as np
from PIL import Image
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
import random
from collections import defaultdict


class ISACDataset(Dataset):
    """
    ISAC数据集格式
    数据结构: normal/abnormal -> Train/Test -> 子类别
    """
    def __init__(self, **kwargs):
        # 从kwargs中提取参数，设置默认值
        self.data_root = kwargs.get('data_root')
        self.split = kwargs.get('split', 'train').lower()
        self.transform = kwargs.get('transform', None)
        self.classification_type = kwargs.get('classification_type', 'binary')
        self.seed = kwargs.get('seed', 42)

        if self.data_root is None:
            raise ValueError("data_root is required")

        # 设置随机种子
        random.seed(self.seed)
        np.random.seed(self.seed)

        # 验证数据集目录结构
        self._validate_dataset_structure()

        # 建立类别映射
        self.class_to_idx, self.idx_to_class = self._build_class_mapping()

        # 加载数据
        self.data = self._load_data()

        print(f"ISAC Dataset - {self.split} split")
        print(f"Loaded {len(self.data)} samples")
        print(f"Classification type: {self.classification_type}")
        print(f"Number of classes: {len(self.class_to_idx)}")
        print(f"Classes: {list(self.class_to_idx.keys())}")
        self._print_class_distribution()

    def _validate_dataset_structure(self):
        """验证ISAC数据集目录结构"""
        if not os.path.exists(self.data_root):
            raise ValueError(f"Dataset root directory does not exist: {self.data_root}")

        # 检查normal和abnormal目录
        normal_dir = os.path.join(self.data_root, 'normal')
        abnormal_dir = os.path.join(self.data_root, 'abnormal')

        if not os.path.exists(normal_dir):
            raise ValueError(f"Normal directory does not exist: {normal_dir}")

        if not os.path.exists(abnormal_dir):
            raise ValueError(f"Abnormal directory does not exist: {abnormal_dir}")

        # 检查Train/Test子目录
        for main_dir, name in [(normal_dir, 'normal'), (abnormal_dir, 'abnormal')]:
            for subdir in ['Train', 'Test']:
                subdir_path = os.path.join(main_dir, subdir)
                if not os.path.exists(subdir_path):
                    print(f"Warning: {name}/{subdir} directory does not exist: {subdir_path}")

    def _build_class_mapping(self):
        """构建类别映射"""
        class_to_idx = {}
        idx = 0

        if self.classification_type == 'binary':
            # 二分类：normal vs abnormal
            class_to_idx['normal'] = 0
            class_to_idx['abnormal'] = 1
        else:
            # 多分类：normal + 各种恶意流量类型
            class_to_idx['normal'] = idx
            idx += 1

            # 扫描abnormal目录下的恶意流量类型
            abnormal_dir = os.path.join(self.data_root, 'abnormal')
            if os.path.exists(abnormal_dir):
                # 检查Train和Test目录，确保获取所有类别
                for subdir in ['Train', 'Test']:
                    subdir_path = os.path.join(abnormal_dir, subdir)
                    if os.path.exists(subdir_path):
                        for malware_type in sorted(os.listdir(subdir_path)):
                            malware_path = os.path.join(subdir_path, malware_type)
                            if os.path.isdir(malware_path) and malware_type not in class_to_idx:
                                class_to_idx[malware_type] = idx
                                idx += 1

        if len(class_to_idx) == 0:
            raise ValueError(f"No classes found in {self.data_root}")

        idx_to_class = {v: k for k, v in class_to_idx.items()}
        return class_to_idx, idx_to_class

    def _load_data(self):
        """加载数据"""
        all_data = []

        # 映射split到目录名
        split_map = {'train': 'Train', 'test': 'Test', 'val': 'Train'}

        if self.split not in split_map:
            raise ValueError(f"Unknown split: {self.split}")

        # 加载normal数据
        normal_dir = os.path.join(self.data_root, 'normal', split_map[self.split])
        if os.path.exists(normal_dir):
            normal_files = self._get_image_files(normal_dir)
            for file_path in normal_files:
                all_data.append((file_path, self.class_to_idx['normal']))

        # 加载abnormal数据
        abnormal_base_dir = os.path.join(self.data_root, 'abnormal', split_map[self.split])
        if os.path.exists(abnormal_base_dir):
            for malware_type in os.listdir(abnormal_base_dir):
                malware_dir = os.path.join(abnormal_base_dir, malware_type)
                if os.path.isdir(malware_dir):
                    if self.classification_type == 'binary':
                        # 二分类模式：所有恶意流量都标记为abnormal
                        label = self.class_to_idx['abnormal']
                    else:
                        # 多分类模式：使用具体的恶意流量类型
                        if malware_type in self.class_to_idx:
                            label = self.class_to_idx[malware_type]
                        else:
                            print(f"Warning: Unknown malware type '{malware_type}', skipping...")
                            continue

                    malware_files = self._get_image_files(malware_dir)
                    for file_path in malware_files:
                        all_data.append((file_path, label))

        # 如果是验证集，从训练数据中分割
        if self.split == 'val':
            all_data = self._create_val_split(all_data)

        random.shuffle(all_data)
        return all_data

    def _get_image_files(self, directory):
        """获取目录下的所有图像文件"""
        image_files = []
        for root, dirs, files in os.walk(directory):
            for file in files:
                if file.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff')):
                    image_files.append(os.path.join(root, file))
        return image_files

    def _create_val_split(self, train_data):
        """从训练数据创建验证集"""
        class_data = defaultdict(list)
        for file_path, label in train_data:
            class_data[label].append((file_path, label))

        val_data = []
        val_ratio = 0.2

        for label, files in class_data.items():
            random.shuffle(files)
            n_val = max(1, int(len(files) * val_ratio))
            val_data.extend(files[:n_val])

        return val_data

    def _print_class_distribution(self):
        """打印类别分布"""
        class_counts = defaultdict(int)
        for _, label in self.data:
            class_counts[label] += 1

        print("Class distribution:")
        for class_idx, count in class_counts.items():
            class_name = self.idx_to_class[class_idx]
            print(f"  {class_name}: {count} samples")

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        file_path, label = self.data[idx]

        try:
            image = Image.open(file_path).convert('RGB')
        except Exception as e:
            print(f"Error loading image {file_path}: {e}")
            image = Image.new('RGB', (84, 84), color='black')

        if self.transform:
            image = self.transform(image)

        return image, label


class DefaultDataset(Dataset):
    """
    默认数据集格式
    数据结构: train/test -> 类别目录
    """
    def __init__(self, **kwargs):
        # 从kwargs中提取参数，设置默认值
        self.data_root = kwargs.get('data_root')
        self.split = kwargs.get('split', 'train').lower()
        self.transform = kwargs.get('transform', None)
        self.classification_type = kwargs.get('classification_type', 'multiclass')
        self.seed = kwargs.get('seed', 42)

        if self.data_root is None:
            raise ValueError("data_root is required")

        # 设置随机种子
        random.seed(self.seed)
        np.random.seed(self.seed)

        # 建立类别映射
        self.class_to_idx, self.idx_to_class = self._build_class_mapping()

        # 加载数据
        self.data = self._load_data()

        print(f"Default Dataset - {self.split} split")
        print(f"Loaded {len(self.data)} samples")
        print(f"Number of classes: {len(self.class_to_idx)}")
        self._print_class_distribution()

    def _build_class_mapping(self):
        """构建类别映射"""
        class_to_idx = {}
        idx = 0

        # 扫描train目录获取所有类别
        train_dir = os.path.join(self.data_root, 'train')
        if os.path.exists(train_dir):
            for class_name in sorted(os.listdir(train_dir)):
                class_path = os.path.join(train_dir, class_name)
                if os.path.isdir(class_path):
                    class_to_idx[class_name] = idx
                    idx += 1

        # 如果train目录不存在，扫描test目录
        if not class_to_idx:
            test_dir = os.path.join(self.data_root, 'test')
            if os.path.exists(test_dir):
                for class_name in sorted(os.listdir(test_dir)):
                    class_path = os.path.join(test_dir, class_name)
                    if os.path.isdir(class_path):
                        class_to_idx[class_name] = idx
                        idx += 1

        if not class_to_idx:
            raise ValueError(f"No class directories found in {self.data_root}")

        idx_to_class = {v: k for k, v in class_to_idx.items()}
        return class_to_idx, idx_to_class

    def _load_data(self):
        """加载数据"""
        all_data = []

        if self.split == 'val':
            # 验证集从训练集分割
            split_dir = os.path.join(self.data_root, 'train')
            all_data = self._load_split_dir(split_dir)
            all_data = self._create_val_split(all_data)
        else:
            # 直接加载对应split目录
            split_dir = os.path.join(self.data_root, self.split)
            if os.path.exists(split_dir):
                all_data = self._load_split_dir(split_dir)
            else:
                print(f"Warning: Split directory {split_dir} does not exist")

        random.shuffle(all_data)
        return all_data

    def _load_split_dir(self, split_dir):
        """加载指定split目录的数据"""
        all_data = []

        for class_name in os.listdir(split_dir):
            class_path = os.path.join(split_dir, class_name)
            if os.path.isdir(class_path) and class_name in self.class_to_idx:
                class_label = self.class_to_idx[class_name]
                image_files = self._get_image_files(class_path)

                for file_path in image_files:
                    all_data.append((file_path, class_label))

        return all_data

    def _get_image_files(self, directory):
        """获取目录下的所有图像文件"""
        image_files = []
        for root, dirs, files in os.walk(directory):
            for file in files:
                if file.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff')):
                    image_files.append(os.path.join(root, file))
        return image_files

    def _create_val_split(self, train_data):
        """从训练数据创建验证集"""
        class_data = defaultdict(list)
        for file_path, label in train_data:
            class_data[label].append((file_path, label))

        val_data = []
        val_ratio = 0.2

        for label, files in class_data.items():
            random.shuffle(files)
            n_val = max(1, int(len(files) * val_ratio))
            val_data.extend(files[:n_val])

        return val_data

    def _print_class_distribution(self):
        """打印类别分布"""
        class_counts = defaultdict(int)
        for _, label in self.data:
            class_counts[label] += 1

        print("Class distribution:")
        for class_idx, count in class_counts.items():
            class_name = self.idx_to_class[class_idx]
            print(f"  {class_name}: {count} samples")

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        file_path, label = self.data[idx]

        try:
            image = Image.open(file_path).convert('RGB')
        except Exception as e:
            print(f"Error loading image {file_path}: {e}")
            image = Image.new('RGB', (84, 84), color='black')

        if self.transform:
            image = self.transform(image)

        return image, label

    def _print_class_distribution(self):
        """
        打印类别分布
        """
        class_counts = defaultdict(int)
        for _, label in self.data:
            class_counts[label] += 1

        print("Class distribution:")
        for class_idx, count in class_counts.items():
            class_name = self.idx_to_class[class_idx]
            print(f"  {class_name}: {count} samples")

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        file_path, label = self.data[idx]

        # 加载图像
        try:
            image = Image.open(file_path).convert('RGB')
        except Exception as e:
            print(f"Error loading image {file_path}: {e}")
            # 返回一个默认图像
            image = Image.new('RGB', (84, 84), color='black')

        # 应用变换
        if self.transform:
            image = self.transform(image)

        return image, label


class FewShotDataset:
    """
    Few-shot学习数据集
    """
    def __init__(self, dataset, n_way=2, k_shot=5, k_query=15, num_episodes=100):
        self.dataset = dataset
        self.n_way = n_way
        self.k_shot = k_shot
        self.k_query = k_query
        self.num_episodes = num_episodes

        # 按类别组织数据
        self.class_data = defaultdict(list)
        for idx, (_, label) in enumerate(dataset.data):
            self.class_data[label].append(idx)

        self.classes = list(self.class_data.keys())

        # 检查数据充足性
        self._check_data_sufficiency()

    def _check_data_sufficiency(self):
        """
        检查每个类别是否有足够的数据
        """
        min_samples_needed = self.k_shot + self.k_query

        for class_idx, indices in self.class_data.items():
            if len(indices) < min_samples_needed:
                print(f"Warning: Class {class_idx} has only {len(indices)} samples, "
                      f"but needs at least {min_samples_needed}")

    def __len__(self):
        return self.num_episodes

    def __getitem__(self, idx):
        """
        生成一个few-shot episode
        """
        # 确保有足够的类别
        available_classes = [cls for cls in self.classes if len(self.class_data[cls]) >= self.k_shot + self.k_query]

        if len(available_classes) < self.n_way:
            # 如果可用类别不足，使用所有可用类别
            selected_classes = available_classes
            if len(selected_classes) == 0:
                raise ValueError("No classes have enough samples for few-shot learning")
        else:
            # 随机选择n_way个类别
            selected_classes = random.sample(available_classes, self.n_way)

        support_images = []
        support_labels = []
        query_images = []
        query_labels = []

        for i, class_idx in enumerate(selected_classes):
            # 获取该类别的所有样本
            class_indices = self.class_data[class_idx]

            # 随机选择k_shot + k_query个样本
            total_needed = self.k_shot + self.k_query
            if len(class_indices) >= total_needed:
                selected_indices = random.sample(class_indices, total_needed)
            else:
                # 如果样本不够，进行重复采样
                selected_indices = random.choices(class_indices, k=total_needed)

            # 分割为支持集和查询集
            support_indices = selected_indices[:self.k_shot]
            query_indices = selected_indices[self.k_shot:self.k_shot + self.k_query]

            # 加载支持集图像
            for idx in support_indices:
                image, _ = self.dataset[idx]
                support_images.append(image)
                support_labels.append(i)  # 使用重新映射的标签

            # 加载查询集图像
            for idx in query_indices:
                image, _ = self.dataset[idx]
                query_images.append(image)
                query_labels.append(i)  # 使用重新映射的标签

        # 转换为tensor
        support_images = torch.stack(support_images)
        support_labels = torch.tensor(support_labels, dtype=torch.long)
        query_images = torch.stack(query_images)
        query_labels = torch.tensor(query_labels, dtype=torch.long)

        return support_images, support_labels, query_images, query_labels


def get_transforms(config, is_training=True):
    """
    获取数据变换
    """
    # 获取配置参数
    img_size = config.data.img_size
    aug_config = config.augmentation

    if is_training:
        transform_list = [
            transforms.Resize((img_size, img_size)),
        ]

        # 添加数据增强
        if aug_config.horizontal_flip:
            transform_list.append(transforms.RandomHorizontalFlip(p=0.5))

        if aug_config.vertical_flip:
            transform_list.append(transforms.RandomVerticalFlip(p=0.5))

        if aug_config.rotation > 0:
            transform_list.append(transforms.RandomRotation(aug_config.rotation))

        # 颜色抖动
        if hasattr(aug_config, 'color_jitter'):
            cj = aug_config.color_jitter
            transform_list.append(transforms.ColorJitter(
                brightness=cj.brightness,
                contrast=cj.contrast,
                saturation=cj.saturation,
                hue=cj.hue
            ))

        transform_list.append(transforms.ToTensor())

        # 归一化
        if hasattr(aug_config, 'normalize'):
            norm = aug_config.normalize
            transform_list.append(transforms.Normalize(mean=norm.mean, std=norm.std))

        transform = transforms.Compose(transform_list)
    else:
        transform = transforms.Compose([
            transforms.Resize((img_size, img_size)),
            transforms.ToTensor(),
            transforms.Normalize(
                mean=aug_config.normalize.mean,
                std=aug_config.normalize.std
            )
        ])

    return transform


def few_shot_collate_fn(batch):
    """
    自定义collate函数处理few-shot batch
    """
    support_images = []
    support_labels = []
    query_images = []
    query_labels = []

    for s_img, s_lbl, q_img, q_lbl in batch:
        support_images.append(s_img)
        support_labels.append(s_lbl)
        query_images.append(q_img)
        query_labels.append(q_lbl)

    return (torch.stack(support_images), torch.stack(support_labels),
            torch.stack(query_images), torch.stack(query_labels))


def create_few_shot_dataloader(**kwargs):
    """
    创建few-shot数据加载器

    Args:
        **kwargs: 包含所有必要参数的字典
            - config: 配置对象
            - split: 数据集分割（默认'train'）
            - dataset_type: 数据集类型（可选，从config读取）
            - classification_type: 分类类型（可选，从config读取）
            - transform: 数据变换（可选，自动生成）
    """
    config = kwargs.get('config')
    if config is None:
        raise ValueError("config is required")

    split = kwargs.get('split', 'train')

    # 获取变换
    is_training = (split == 'train')
    transform = kwargs.get('transform', get_transforms(config, is_training))

    # 根据配置选择数据集类
    dataset_type = kwargs.get('dataset_type', getattr(config.data, 'dataset_type', 'ISAC')).upper()
    classification_type = kwargs.get('classification_type', getattr(config.data, 'classification_type', 'binary'))

    # 准备数据集参数
    dataset_kwargs = {
        'data_root': config.data.data_root,
        'split': split,
        'transform': transform,
        'classification_type': classification_type,
        'seed': config.training.seed
    }

    if dataset_type == 'ISAC':
        dataset = ISACDataset(**dataset_kwargs)
    elif dataset_type == 'DEFAULT':
        dataset = DefaultDataset(**dataset_kwargs)
    else:
        raise ValueError(f"Unsupported dataset type: {dataset_type}. Use 'ISAC' or 'DEFAULT'")

    # 创建few-shot数据集
    few_shot_dataset = FewShotDataset(
        dataset=dataset,
        n_way=config.maml.n_way,
        k_shot=config.maml.k_shot,
        k_query=config.maml.k_query,
        num_episodes=config.evaluation.test_episodes if split == 'test' else 1000
    )

    # 创建数据加载器
    # 在Windows上使用多进程可能有问题，设置num_workers=0
    import platform
    num_workers = 0 if platform.system() == 'Windows' else config.training.num_workers

    dataloader = DataLoader(
        few_shot_dataset,
        batch_size=config.maml.task_num,
        shuffle=is_training,
        num_workers=num_workers,
        collate_fn=few_shot_collate_fn,
        pin_memory=True
    )

    return dataloader


def split_dataset(data_root, train_ratio=0.7, val_ratio=0.15, test_ratio=0.15, seed=42):
    """
    分割数据集为训练/验证/测试集

    Args:
        data_root: 数据根目录
        train_ratio: 训练集比例
        val_ratio: 验证集比例
        test_ratio: 测试集比例
        seed: 随机种子

    Returns:
        dict: 包含各个split的文件路径
    """
    random.seed(seed)
    np.random.seed(seed)

    splits = {'train': [], 'val': [], 'test': []}

    for class_name in ['normal', 'abnormal']:
        class_dir = os.path.join(data_root, class_name)
        if not os.path.exists(class_dir):
            continue

        # 获取所有文件
        files = [f for f in os.listdir(class_dir)
                if f.lower().endswith(('.png', '.jpg', '.jpeg'))]

        # 随机打乱
        random.shuffle(files)

        # 计算分割点
        n_total = len(files)
        n_train = int(n_total * train_ratio)
        n_val = int(n_total * val_ratio)

        # 分割文件
        train_files = files[:n_train]
        val_files = files[n_train:n_train + n_val]
        test_files = files[n_train + n_val:]

        # 添加到splits
        for f in train_files:
            splits['train'].append(os.path.join(class_dir, f))
        for f in val_files:
            splits['val'].append(os.path.join(class_dir, f))
        for f in test_files:
            splits['test'].append(os.path.join(class_dir, f))

    # 打印统计信息
    for split, files in splits.items():
        print(f"{split}: {len(files)} files")

    return splits
