# 高性能配置 - 用于最佳性能训练

# 数据相关配置
data:
  data_root: "../data/ISAC_LWH_RGB"
  img_size: 84
  img_channels: 3
  dataset_type: "ISAC"
  classification_type: "multiclass"  # 使用多分类以获得更好的性能

# SimCLR预训练模型配置
simclr:
  pretrained_path: null
  backbone: "resnet50"   # 使用更大的骨干网络
  feature_dim: 2048      # 更大的特征维度
  freeze_backbone: true   # ✅ 冻结encoder

# MAML配置
maml:
  n_way: 2
  k_shot: 10            # 更多的shot数
  k_query: 20           # 更多的查询样本
  task_num: 8           # 更多的任务数
  update_lr: 0.005      # 更小的内循环学习率
  meta_lr: 0.0005       # 更小的元学习率
  update_step: 10       # 更多的更新步数
  update_step_test: 20

# 训练配置
training:
  epochs: 200           # 更多的训练轮数
  batch_size: 64        # 更大的批次大小
  num_workers: 8
  device: "cuda"
  seed: 42
  early_stopping_patience: 25
  gradient_clip_norm: 0.5

# 优化器配置
optimizer:
  type: "adam"
  lr_scheduler:
    type: "cosine"
    T_max: 200
    eta_min: 0.0000001

# 保存和日志配置
logging:
  save_dir: "./checkpoints_hp"
  log_dir: "./logs_hp"
  save_freq: 20
  log_freq: 50
  experiment_name: "high_performance"

# 评估配置
evaluation:
  eval_freq: 10
  test_episodes: 200
  metrics: ["accuracy", "precision", "recall", "f1_score"]

# 数据增强配置（增强版）
augmentation:
  horizontal_flip: true
  vertical_flip: true   # 启用垂直翻转
  rotation: 15          # 更大的旋转角度
  color_jitter:
    brightness: 0.3
    contrast: 0.3
    saturation: 0.3
    hue: 0.15
  gaussian_blur:
    kernel_size: 5
    sigma: [0.1, 3.0]
  normalize:
    mean: [0.485, 0.456, 0.406]
    std: [0.229, 0.224, 0.225]
