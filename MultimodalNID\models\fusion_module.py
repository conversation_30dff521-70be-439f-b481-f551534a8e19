# -*- coding: utf-8 -*-
"""
多模态融合模块
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Optional

from ..config.training_config import TrainingConfig


class FusionModule(nn.Module):
    """多模态融合模块，融合图像和序列特征"""
    
    def __init__(self, 
                 image_feature_dim: int,
                 sequence_feature_dim: int,
                 config: TrainingConfig):
        """
        初始化融合模块
        
        Args:
            image_feature_dim: 图像特征维度
            sequence_feature_dim: 序列特征维度
            config: 训练配置
        """
        super(FusionModule, self).__init__()
        self.config = config
        self.image_feature_dim = image_feature_dim
        self.sequence_feature_dim = sequence_feature_dim
        
        # 根据融合策略创建相应的模块
        if config.fusion_strategy == 'concat':
            self.fusion_layer = self._create_concat_fusion()
        elif config.fusion_strategy == 'attention':
            self.fusion_layer = self._create_attention_fusion()
        elif config.fusion_strategy == 'gated':
            self.fusion_layer = self._create_gated_fusion()
        else:
            raise ValueError(f"不支持的融合策略: {config.fusion_strategy}")
        
        # 输出投影层
        self.output_projection = self._create_output_projection()
        
        # Dropout层
        self.dropout = nn.Dropout(config.fusion_dropout)
    
    def _create_concat_fusion(self) -> nn.Module:
        """创建简单拼接融合"""
        total_dim = self.image_feature_dim + self.sequence_feature_dim
        
        return nn.Sequential(
            nn.Linear(total_dim, total_dim // 2),
            nn.ReLU(inplace=True),
            nn.Dropout(self.config.fusion_dropout),
            nn.Linear(total_dim // 2, 512)
        )
    
    def _create_attention_fusion(self) -> nn.Module:
        """创建注意力融合"""
        return AttentionFusion(
            image_dim=self.image_feature_dim,
            sequence_dim=self.sequence_feature_dim,
            hidden_dim=512,
            dropout=self.config.fusion_dropout
        )
    
    def _create_gated_fusion(self) -> nn.Module:
        """创建门控融合"""
        return GatedFusion(
            image_dim=self.image_feature_dim,
            sequence_dim=self.sequence_feature_dim,
            hidden_dim=512,
            dropout=self.config.fusion_dropout
        )
    
    def _create_output_projection(self) -> nn.Module:
        """创建输出投影层"""
        return nn.Sequential(
            nn.Linear(512, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(self.config.fusion_dropout),
            nn.Linear(256, 512)
        )
    
    def forward(self, image_features: torch.Tensor, 
                sequence_features: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            image_features: 图像特征 (batch_size, image_feature_dim)
            sequence_features: 序列特征 (batch_size, sequence_feature_dim)
            
        Returns:
            融合后的特征 (batch_size, output_dim)
        """
        # 根据融合策略进行融合
        if self.config.fusion_strategy == 'concat':
            # 简单拼接
            combined_features = torch.cat([image_features, sequence_features], dim=1)
            fused_features = self.fusion_layer(combined_features)
        else:
            # 注意力或门控融合
            fused_features = self.fusion_layer(image_features, sequence_features)
        
        # 应用dropout
        fused_features = self.dropout(fused_features)
        
        # 输出投影
        output_features = self.output_projection(fused_features)
        
        # L2归一化
        output_features = F.normalize(output_features, p=2, dim=1)
        
        return output_features


class AttentionFusion(nn.Module):
    """注意力融合模块"""
    
    def __init__(self, image_dim: int, sequence_dim: int, 
                 hidden_dim: int, dropout: float = 0.1):
        """
        初始化注意力融合
        
        Args:
            image_dim: 图像特征维度
            sequence_dim: 序列特征维度
            hidden_dim: 隐藏层维度
            dropout: Dropout率
        """
        super(AttentionFusion, self).__init__()
        
        # 特征投影层
        self.image_projection = nn.Linear(image_dim, hidden_dim)
        self.sequence_projection = nn.Linear(sequence_dim, hidden_dim)
        
        # 注意力计算层
        self.attention_layer = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.Tanh(),
            nn.Linear(hidden_dim, 2),
            nn.Softmax(dim=1)
        )
        
        # 融合层
        self.fusion_layer = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout)
        )
    
    def forward(self, image_features: torch.Tensor, 
                sequence_features: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            image_features: 图像特征
            sequence_features: 序列特征
            
        Returns:
            融合后的特征
        """
        # 投影到相同维度
        image_proj = self.image_projection(image_features)
        sequence_proj = self.sequence_projection(sequence_features)
        
        # 计算注意力权重
        combined = torch.cat([image_proj, sequence_proj], dim=1)
        attention_weights = self.attention_layer(combined)
        
        # 加权融合
        weighted_image = image_proj * attention_weights[:, 0:1]
        weighted_sequence = sequence_proj * attention_weights[:, 1:2]
        
        fused_features = weighted_image + weighted_sequence
        
        # 最终融合层
        output = self.fusion_layer(fused_features)
        
        return output


class GatedFusion(nn.Module):
    """门控融合模块"""
    
    def __init__(self, image_dim: int, sequence_dim: int, 
                 hidden_dim: int, dropout: float = 0.1):
        """
        初始化门控融合
        
        Args:
            image_dim: 图像特征维度
            sequence_dim: 序列特征维度
            hidden_dim: 隐藏层维度
            dropout: Dropout率
        """
        super(GatedFusion, self).__init__()
        
        # 特征投影层
        self.image_projection = nn.Linear(image_dim, hidden_dim)
        self.sequence_projection = nn.Linear(sequence_dim, hidden_dim)
        
        # 门控网络
        self.gate_network = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(inplace=True),
            nn.Linear(hidden_dim, hidden_dim),
            nn.Sigmoid()
        )
        
        # 融合层
        self.fusion_layer = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout)
        )
    
    def forward(self, image_features: torch.Tensor, 
                sequence_features: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            image_features: 图像特征
            sequence_features: 序列特征
            
        Returns:
            融合后的特征
        """
        # 投影到相同维度
        image_proj = self.image_projection(image_features)
        sequence_proj = self.sequence_projection(sequence_features)
        
        # 计算门控权重
        combined = torch.cat([image_proj, sequence_proj], dim=1)
        gate = self.gate_network(combined)
        
        # 门控融合
        gated_image = image_proj * gate
        gated_sequence = sequence_proj * (1 - gate)
        
        fused_features = gated_image + gated_sequence
        
        # 最终融合层
        output = self.fusion_layer(fused_features)
        
        return output
