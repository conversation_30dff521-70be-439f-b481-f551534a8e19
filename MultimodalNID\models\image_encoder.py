# -*- coding: utf-8 -*-
"""
图像编码器模型
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Tuple
import torchvision.models as models

from ..config.image_config import ImageConfig


class ImageEncoder(nn.Module):
    """图像编码器，将RGB图像编码为特征向量"""
    
    def __init__(self, config: ImageConfig):
        """
        初始化图像编码器
        
        Args:
            config: 图像配置对象
        """
        super(ImageEncoder, self).__init__()
        self.config = config
        
        # 创建骨干网络
        self.backbone = self._create_backbone()
        
        # 获取骨干网络输出维度
        self.backbone_dim = self._get_backbone_dim()
        
        # 创建投影头
        self.projection_head = self._create_projection_head()
        
        # Dropout层
        self.dropout = nn.Dropout(config.dropout_rate)
    
    def _create_backbone(self) -> nn.Module:
        """创建骨干网络"""
        if self.config.encoder_type == 'resnet18':
            backbone = models.resnet18(pretrained=self.config.pretrained)
            # 移除最后的分类层
            backbone = nn.Sequential(*list(backbone.children())[:-1])
            
        elif self.config.encoder_type == 'resnet50':
            backbone = models.resnet50(pretrained=self.config.pretrained)
            backbone = nn.Sequential(*list(backbone.children())[:-1])
            
        elif self.config.encoder_type == 'efficientnet':
            # 使用EfficientNet-B0
            try:
                from torchvision.models import efficientnet_b0
                backbone = efficientnet_b0(pretrained=self.config.pretrained)
                backbone.classifier = nn.Identity()
            except ImportError:
                # 如果没有EfficientNet，回退到ResNet18
                backbone = models.resnet18(pretrained=self.config.pretrained)
                backbone = nn.Sequential(*list(backbone.children())[:-1])
                
        elif self.config.encoder_type == 'vgg16':
            backbone = models.vgg16(pretrained=self.config.pretrained)
            backbone.classifier = nn.Identity()
            
        elif self.config.encoder_type == 'densenet':
            backbone = models.densenet121(pretrained=self.config.pretrained)
            backbone.classifier = nn.Identity()
            
        else:
            raise ValueError(f"不支持的编码器类型: {self.config.encoder_type}")
        
        return backbone
    
    def _get_backbone_dim(self) -> int:
        """获取骨干网络输出维度"""
        # 创建一个测试输入来确定输出维度
        test_input = torch.randn(1, self.config.channels, *self.config.image_size)
        
        with torch.no_grad():
            test_output = self.backbone(test_input)
            
        # 展平输出以获取特征维度
        if len(test_output.shape) > 2:
            test_output = test_output.view(test_output.size(0), -1)
            
        return test_output.shape[1]
    
    def _create_projection_head(self) -> nn.Module:
        """创建投影头"""
        return nn.Sequential(
            nn.Linear(self.backbone_dim, self.backbone_dim // 2),
            nn.ReLU(inplace=True),
            nn.Dropout(self.config.dropout_rate),
            nn.Linear(self.backbone_dim // 2, self.config.feature_dim),
            nn.ReLU(inplace=True)
        )
    
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            x: 输入图像张量 (batch_size, channels, height, width)
            
        Returns:
            (backbone_features, projected_features): 骨干特征和投影特征
        """
        # 骨干网络特征提取
        backbone_features = self.backbone(x)
        
        # 展平特征
        if len(backbone_features.shape) > 2:
            backbone_features = backbone_features.view(backbone_features.size(0), -1)
        
        # 应用dropout
        backbone_features = self.dropout(backbone_features)
        
        # 投影到目标维度
        projected_features = self.projection_head(backbone_features)
        
        # L2归一化
        projected_features = F.normalize(projected_features, p=2, dim=1)
        
        return backbone_features, projected_features
    
    def get_features(self, x: torch.Tensor) -> torch.Tensor:
        """
        仅获取投影后的特征（用于推理）
        
        Args:
            x: 输入图像张量
            
        Returns:
            投影特征
        """
        _, projected_features = self.forward(x)
        return projected_features


class ResidualBlock(nn.Module):
    """残差块（用于自定义网络）"""
    
    def __init__(self, in_channels: int, out_channels: int, stride: int = 1):
        """
        初始化残差块
        
        Args:
            in_channels: 输入通道数
            out_channels: 输出通道数
            stride: 步长
        """
        super(ResidualBlock, self).__init__()
        
        self.conv1 = nn.Conv2d(in_channels, out_channels, 
                              kernel_size=3, stride=stride, padding=1, bias=False)
        self.bn1 = nn.BatchNorm2d(out_channels)
        
        self.conv2 = nn.Conv2d(out_channels, out_channels,
                              kernel_size=3, stride=1, padding=1, bias=False)
        self.bn2 = nn.BatchNorm2d(out_channels)
        
        # 快捷连接
        self.shortcut = nn.Sequential()
        if stride != 1 or in_channels != out_channels:
            self.shortcut = nn.Sequential(
                nn.Conv2d(in_channels, out_channels,
                         kernel_size=1, stride=stride, bias=False),
                nn.BatchNorm2d(out_channels)
            )
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        out = F.relu(self.bn1(self.conv1(x)))
        out = self.bn2(self.conv2(out))
        out += self.shortcut(x)
        out = F.relu(out)
        return out


class CustomCNN(nn.Module):
    """自定义CNN网络（用于小尺寸图像）"""
    
    def __init__(self, input_channels: int = 3, feature_dim: int = 512):
        """
        初始化自定义CNN
        
        Args:
            input_channels: 输入通道数
            feature_dim: 输出特征维度
        """
        super(CustomCNN, self).__init__()
        
        # 卷积层
        self.conv_layers = nn.Sequential(
            # 第一个卷积块
            nn.Conv2d(input_channels, 64, kernel_size=3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, 2),
            
            # 第二个卷积块
            nn.Conv2d(64, 128, kernel_size=3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, 2),
            
            # 第三个卷积块
            nn.Conv2d(128, 256, kernel_size=3, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, 2),
            
            # 第四个卷积块
            nn.Conv2d(256, 512, kernel_size=3, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.AdaptiveAvgPool2d((1, 1))
        )
        
        # 全连接层
        self.fc_layers = nn.Sequential(
            nn.Linear(512, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5),
            nn.Linear(256, feature_dim)
        )
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        # 卷积特征提取
        features = self.conv_layers(x)
        
        # 展平
        features = features.view(features.size(0), -1)
        
        # 全连接层
        output = self.fc_layers(features)
        
        return output
