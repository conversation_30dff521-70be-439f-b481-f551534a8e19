CUDA_ VISIBLE_DEVICES: 0
train_data_path: "../../data/CTU_10_SHOT"
val_data_path: "../../data/CTU_Processed/CTU_IMG/Test"
pretrained_model_path: "../../ProtoSimCLR/checkpoints/ProtoNet_pretrained/checkpoint_epoch_2000.pth" #"./runs/Jul08_22-42-52_amax/checkpoint_2000.pth.tar"  # 或指定路径如: "./runs/model.pth"
save_dir: "./test_performance_logs"
log_name: "CTU_ProtoNet_pretrained_10"
save_name: "test.pth"
img_size: 32
batch_size: 128
learning_rate: 0.001
freeze_train: True
finetune_epochs: 150
freeze_epoch: 20
base_model: "resnet18"
out_dim: 128

transform:
  mean: [0.485, 0.456, 0.406]
  std: [0.229, 0.224, 0.225]