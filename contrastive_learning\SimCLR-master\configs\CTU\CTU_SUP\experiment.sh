#!/bin/bash

# 记录当前目录（YAML文件所在位置）
YAML_DIR=$(pwd)
echo "YAML文件目录: $YAML_DIR"
echo "10shot_task1.yaml 路径: $YAML_DIR/10shot_task1.yaml"
echo "10shot_task2.yaml 路径: $YAML_DIR/10shot_task2.yaml"
echo "10shot_task3.yaml 路径: $YAML_DIR/10shot_task3.yaml"
echo "10shot_task4.yaml 路径: $YAML_DIR/10shot_task4.yaml"

# 切换到工作目录（supervised_train.py所在目录）
WORK_DIR="../../../"
cd "$WORK_DIR" || { echo "无法切换到工作目录 $WORK_DIR"; exit 1; }
echo "当前工作目录: $(pwd)"

# 使用记录的YAML路径并行运行四个任务
python supervised_train.py --config "$YAML_DIR/10shot_task1.yaml" &
python supervised_train.py --config "$YAML_DIR/10shot_task2.yaml" &
python supervised_train.py --config "$YAML_DIR/10shot_task3.yaml" &
python supervised_train.py --config "$YAML_DIR/10shot_task4.yaml" &

# 等待所有后台进程完成
wait

echo "所有脚本执行完毕"
