# -*- coding: utf-8 -*-
"""
训练器模块
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter
import numpy as np
import logging
from pathlib import Path
from typing import Dict, Optional, Tuple, Any
from tqdm import tqdm
import time

from ..models.contrastive_model import ContrastiveModel
from ..config.image_config import ImageConfig
from ..config.sequence_config import SequenceConfig
from ..config.training_config import TrainingConfig
from .losses import ContrastiveLoss, AlignmentLoss, FocalLoss
from .metrics import MetricsCalculator, EarlyStopping, LearningRateScheduler


class Trainer:
    """多模态对比学习训练器"""
    
    def __init__(self,
                 model: ContrastiveModel,
                 train_loader: DataLoader,
                 val_loader: DataLoader,
                 config: TrainingConfig,
                 logger: Optional[logging.Logger] = None):
        """
        初始化训练器
        
        Args:
            model: 对比学习模型
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            config: 训练配置
            logger: 日志记录器
        """
        self.model = model
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.config = config
        self.logger = logger or self._setup_logger()
        
        # 设置设备
        self.device = torch.device(config.device if torch.cuda.is_available() else 'cpu')
        self.model.to(self.device)
        
        # 创建优化器
        self.optimizer = self._create_optimizer()
        
        # 创建学习率调度器
        self.scheduler = self._create_scheduler()
        
        # 创建损失函数
        self.contrastive_loss = ContrastiveLoss(
            temperature=config.temperature,
            use_hard_negatives=config.use_hard_negatives
        )
        self.alignment_loss = AlignmentLoss(loss_type='mse')
        self.classification_loss = nn.CrossEntropyLoss()
        
        # 创建指标计算器
        self.train_metrics = MetricsCalculator(num_classes=2)
        self.val_metrics = MetricsCalculator(num_classes=2)
        
        # 早停机制
        self.early_stopping = EarlyStopping(
            patience=config.early_stopping_patience,
            restore_best_weights=True
        )
        
        # TensorBoard记录器
        if config.use_tensorboard:
            self.writer = SummaryWriter(log_dir=Path(config.log_dir) / 'tensorboard')
        else:
            self.writer = None
        
        # 训练状态
        self.current_epoch = 0
        self.best_val_score = 0.0
        self.training_history = {
            'train_loss': [],
            'val_loss': [],
            'train_acc': [],
            'val_acc': [],
            'contrastive_acc': []
        }
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('Trainer')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _create_optimizer(self) -> optim.Optimizer:
        """创建优化器"""
        optimizer_config = self.config.get_optimizer_config()
        
        if optimizer_config['type'] == 'adam':
            return optim.Adam(
                self.model.parameters(),
                lr=optimizer_config['lr'],
                weight_decay=optimizer_config['weight_decay'],
                betas=optimizer_config['betas'],
                eps=optimizer_config['eps']
            )
        elif optimizer_config['type'] == 'sgd':
            return optim.SGD(
                self.model.parameters(),
                lr=optimizer_config['lr'],
                weight_decay=optimizer_config['weight_decay'],
                momentum=optimizer_config['momentum'],
                nesterov=optimizer_config['nesterov']
            )
        elif optimizer_config['type'] == 'adamw':
            return optim.AdamW(
                self.model.parameters(),
                lr=optimizer_config['lr'],
                weight_decay=optimizer_config['weight_decay'],
                betas=optimizer_config['betas'],
                eps=optimizer_config['eps']
            )
        else:
            raise ValueError(f"不支持的优化器类型: {optimizer_config['type']}")
    
    def _create_scheduler(self) -> LearningRateScheduler:
        """创建学习率调度器"""
        scheduler_config = self.config.get_scheduler_config()
        
        return LearningRateScheduler(
            optimizer=self.optimizer,
            scheduler_type=scheduler_config['type'],
            **{k: v for k, v in scheduler_config.items() if k != 'type'}
        )
    
    def train_epoch(self) -> Dict[str, float]:
        """训练一个epoch"""
        self.model.train()
        self.train_metrics.reset()
        
        total_loss = 0.0
        total_contrastive_loss = 0.0
        total_alignment_loss = 0.0
        total_classification_loss = 0.0
        
        progress_bar = tqdm(self.train_loader, desc=f'Epoch {self.current_epoch + 1}')
        
        for batch_idx, batch in enumerate(progress_bar):
            # 移动数据到设备
            images = batch['image'].to(self.device)
            sequences = batch['sequence'].to(self.device)
            labels = batch['label'].to(self.device)
            
            # 前向传播
            outputs = self.model(images, sequences)
            
            # 计算损失
            loss_dict = self.model.compute_total_loss(outputs, labels)
            
            # 反向传播
            self.optimizer.zero_grad()
            loss_dict['total_loss'].backward()
            
            # 梯度裁剪
            if self.config.gradient_clip_norm > 0:
                torch.nn.utils.clip_grad_norm_(
                    self.model.parameters(), 
                    self.config.gradient_clip_norm
                )
            
            self.optimizer.step()
            
            # 更新指标
            predictions = torch.argmax(outputs['logits'], dim=1)
            probabilities = torch.softmax(outputs['logits'], dim=1)
            
            self.train_metrics.update(
                predictions=predictions,
                labels=labels.squeeze(),
                probabilities=probabilities
            )
            
            # 累积损失
            total_loss += loss_dict['total_loss'].item()
            total_contrastive_loss += loss_dict['contrastive_loss'].item()
            total_alignment_loss += loss_dict['alignment_loss'].item()
            total_classification_loss += loss_dict['classification_loss'].item()
            
            # 更新进度条
            progress_bar.set_postfix({
                'Loss': f"{loss_dict['total_loss'].item():.4f}",
                'Acc': f"{self.train_metrics.compute_metrics().get('accuracy', 0):.4f}"
            })
            
            # 记录到TensorBoard
            if self.writer and batch_idx % self.config.log_frequency == 0:
                global_step = self.current_epoch * len(self.train_loader) + batch_idx
                self.writer.add_scalar('Train/BatchLoss', loss_dict['total_loss'].item(), global_step)
                self.writer.add_scalar('Train/ContrastiveLoss', loss_dict['contrastive_loss'].item(), global_step)
                self.writer.add_scalar('Train/AlignmentLoss', loss_dict['alignment_loss'].item(), global_step)
                self.writer.add_scalar('Train/ClassificationLoss', loss_dict['classification_loss'].item(), global_step)
        
        # 计算epoch指标
        epoch_metrics = self.train_metrics.compute_metrics()
        
        return {
            'total_loss': total_loss / len(self.train_loader),
            'contrastive_loss': total_contrastive_loss / len(self.train_loader),
            'alignment_loss': total_alignment_loss / len(self.train_loader),
            'classification_loss': total_classification_loss / len(self.train_loader),
            **epoch_metrics
        }
    
    def validate_epoch(self) -> Dict[str, float]:
        """验证一个epoch"""
        self.model.eval()
        self.val_metrics.reset()
        
        total_loss = 0.0
        total_contrastive_loss = 0.0
        total_alignment_loss = 0.0
        total_classification_loss = 0.0
        
        with torch.no_grad():
            for batch in tqdm(self.val_loader, desc='Validation'):
                # 移动数据到设备
                images = batch['image'].to(self.device)
                sequences = batch['sequence'].to(self.device)
                labels = batch['label'].to(self.device)
                
                # 前向传播
                outputs = self.model(images, sequences)
                
                # 计算损失
                loss_dict = self.model.compute_total_loss(outputs, labels)
                
                # 更新指标
                predictions = torch.argmax(outputs['logits'], dim=1)
                probabilities = torch.softmax(outputs['logits'], dim=1)
                
                self.val_metrics.update(
                    predictions=predictions,
                    labels=labels.squeeze(),
                    probabilities=probabilities
                )
                
                # 累积损失
                total_loss += loss_dict['total_loss'].item()
                total_contrastive_loss += loss_dict['contrastive_loss'].item()
                total_alignment_loss += loss_dict['alignment_loss'].item()
                total_classification_loss += loss_dict['classification_loss'].item()
        
        # 计算epoch指标
        epoch_metrics = self.val_metrics.compute_metrics()
        
        return {
            'total_loss': total_loss / len(self.val_loader),
            'contrastive_loss': total_contrastive_loss / len(self.val_loader),
            'alignment_loss': total_alignment_loss / len(self.val_loader),
            'classification_loss': total_classification_loss / len(self.val_loader),
            **epoch_metrics
        }

    def train(self) -> Dict[str, Any]:
        """完整训练流程"""
        self.logger.info("开始训练...")
        self.logger.info(f"设备: {self.device}")
        self.logger.info(f"训练样本数: {len(self.train_loader.dataset)}")
        self.logger.info(f"验证样本数: {len(self.val_loader.dataset)}")

        start_time = time.time()

        for epoch in range(self.config.num_epochs):
            self.current_epoch = epoch

            # 训练
            train_metrics = self.train_epoch()

            # 验证
            if epoch % self.config.validation_frequency == 0:
                val_metrics = self.validate_epoch()
            else:
                val_metrics = {}

            # 更新学习率
            if val_metrics:
                self.scheduler.step(val_metrics.get('total_loss'))
            else:
                self.scheduler.step()

            # 记录指标
            self.training_history['train_loss'].append(train_metrics['total_loss'])
            self.training_history['train_acc'].append(train_metrics.get('accuracy', 0))

            if val_metrics:
                self.training_history['val_loss'].append(val_metrics['total_loss'])
                self.training_history['val_acc'].append(val_metrics.get('accuracy', 0))

            # 记录到TensorBoard
            if self.writer:
                self.writer.add_scalar('Epoch/TrainLoss', train_metrics['total_loss'], epoch)
                self.writer.add_scalar('Epoch/TrainAcc', train_metrics.get('accuracy', 0), epoch)
                self.writer.add_scalar('Epoch/LearningRate', self.scheduler.get_last_lr()[0], epoch)

                if val_metrics:
                    self.writer.add_scalar('Epoch/ValLoss', val_metrics['total_loss'], epoch)
                    self.writer.add_scalar('Epoch/ValAcc', val_metrics.get('accuracy', 0), epoch)

            # 打印训练信息
            self.logger.info(f"Epoch {epoch + 1}/{self.config.num_epochs}")
            self.logger.info(f"Train Loss: {train_metrics['total_loss']:.4f}, "
                           f"Train Acc: {train_metrics.get('accuracy', 0):.4f}")

            if val_metrics:
                self.logger.info(f"Val Loss: {val_metrics['total_loss']:.4f}, "
                               f"Val Acc: {val_metrics.get('accuracy', 0):.4f}")

            # 保存检查点
            if epoch % self.config.save_frequency == 0:
                self.save_checkpoint(epoch, val_metrics.get('accuracy', 0))

            # 早停检查
            if val_metrics and self.early_stopping(val_metrics.get('accuracy', 0), self.model):
                self.logger.info(f"早停触发，在第{epoch + 1}轮停止训练")
                break

        # 训练完成
        total_time = time.time() - start_time
        self.logger.info(f"训练完成，总用时: {total_time:.2f}秒")

        # 保存最终模型
        self.save_checkpoint(self.current_epoch, self.best_val_score, is_final=True)

        # 关闭TensorBoard
        if self.writer:
            self.writer.close()

        return {
            'training_history': self.training_history,
            'best_val_score': self.best_val_score,
            'total_time': total_time
        }

    def save_checkpoint(self, epoch: int, score: float, is_final: bool = False):
        """保存检查点"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.scheduler.state_dict(),
            'score': score,
            'training_history': self.training_history,
            'config': {
                'image_config': self.model.image_config.to_dict(),
                'sequence_config': self.model.sequence_config.to_dict(),
                'training_config': self.config.to_dict()
            }
        }

        # 保存路径
        checkpoint_dir = Path(self.config.checkpoint_dir)
        checkpoint_dir.mkdir(parents=True, exist_ok=True)

        if is_final:
            checkpoint_path = checkpoint_dir / 'final_model.pth'
        elif score > self.best_val_score:
            checkpoint_path = checkpoint_dir / 'best_model.pth'
            self.best_val_score = score
        else:
            checkpoint_path = checkpoint_dir / f'checkpoint_epoch_{epoch}.pth'

        torch.save(checkpoint, checkpoint_path)
        self.logger.info(f"检查点已保存: {checkpoint_path}")

    def load_checkpoint(self, checkpoint_path: str) -> Dict[str, Any]:
        """加载检查点"""
        checkpoint = torch.load(checkpoint_path, map_location=self.device)

        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.scheduler.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])

        self.current_epoch = checkpoint['epoch']
        self.best_val_score = checkpoint['score']
        self.training_history = checkpoint['training_history']

        self.logger.info(f"检查点已加载: {checkpoint_path}")

        return checkpoint

    def evaluate(self, test_loader: DataLoader) -> Dict[str, Any]:
        """评估模型"""
        self.logger.info("开始评估...")

        self.model.eval()
        test_metrics = MetricsCalculator(num_classes=2)

        all_embeddings = {
            'image_features': [],
            'sequence_features': [],
            'fused_features': []
        }

        with torch.no_grad():
            for batch in tqdm(test_loader, desc='Evaluation'):
                images = batch['image'].to(self.device)
                sequences = batch['sequence'].to(self.device)
                labels = batch['label'].to(self.device)

                # 前向传播
                outputs = self.model(images, sequences)

                # 更新指标
                predictions = torch.argmax(outputs['logits'], dim=1)
                probabilities = torch.softmax(outputs['logits'], dim=1)

                test_metrics.update(
                    predictions=predictions,
                    labels=labels.squeeze(),
                    probabilities=probabilities
                )

                # 收集特征嵌入
                all_embeddings['image_features'].append(outputs['image_features'].cpu())
                all_embeddings['sequence_features'].append(outputs['sequence_features'].cpu())
                all_embeddings['fused_features'].append(outputs['fused_features'].cpu())

        # 计算最终指标
        final_metrics = test_metrics.compute_metrics()
        confusion_matrix = test_metrics.compute_confusion_matrix()
        classification_report = test_metrics.get_classification_report()

        # 合并所有嵌入
        for key in all_embeddings:
            all_embeddings[key] = torch.cat(all_embeddings[key], dim=0)

        self.logger.info("评估完成")
        self.logger.info(f"测试准确率: {final_metrics.get('accuracy', 0):.4f}")
        self.logger.info(f"测试F1分数: {final_metrics.get('f1_score', 0):.4f}")

        return {
            'metrics': final_metrics,
            'confusion_matrix': confusion_matrix,
            'classification_report': classification_report,
            'embeddings': all_embeddings
        }
