#!/usr/bin/env python3
"""
测试LlamaEncoder的实际显存占用
"""

import torch
import sys
import os

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.llama_encoder import LlamaEncoder, LlamaEncoderConfig
import torch.nn as nn
def get_gpu_memory():
    """获取GPU内存使用情况"""
    if torch.cuda.is_available():
        return {
            'allocated_mb': torch.cuda.memory_allocated() / 1024 / 1024,
            'cached_mb': torch.cuda.memory_reserved() / 1024 / 1024,
            'max_allocated_mb': torch.cuda.max_memory_allocated() / 1024 / 1024,
        }
    return {'allocated_mb': 0, 'cached_mb': 0, 'max_allocated_mb': 0}

def test_model_memory(config_name, config, batch_sizes):
    """测试特定配置下的内存使用"""
    print(f"\n=== 测试配置: {config_name} ===")
    print(f"层数: {getattr(config, 'num_hidden_layers', 6)}")
    print(f"注意力头: {getattr(config, 'num_attention_heads', 8)}")
    print(f"FFN维度: {getattr(config, 'intermediate_size', 256)}")
    print(f"隐藏维度: {config.conv_output_channels}")
    
    # 创建模型
    model = LlamaEncoder(config)
    
    if torch.cuda.is_available():
        model = model.cuda()
        device = 'cuda'
    else:
        device = 'cpu'
        print("⚠️ CUDA不可用，使用CPU测试")
        return
    torch.save(model.state_dict(), 'model_state_dict.pt')
    # 统计参数
    total_params = sum(p.numel() for p in model.parameters())
    print(f"总参数量: {total_params:,}")


    for batch_size in batch_sizes:
        try:
            # 清理GPU缓存
            torch.cuda.empty_cache()
            torch.cuda.reset_peak_memory_stats()

            print(f"\n  Batch Size: {batch_size}")

            # 记录初始内存
            initial_memory = get_gpu_memory()
            print(f"    初始GPU内存: {initial_memory['allocated_mb']:.2f} MB")

            # 创建输入数据 (模拟对比学习的2个视图)
            input_data = torch.randn(batch_size * 2, 1024, device=device)

            # 记录输入后内存
            input_memory = get_gpu_memory()
            print(f"    输入后GPU内存: {input_memory['allocated_mb']:.2f} MB")

            # 前向传播 - 使用混合精度
            model.train()  # 训练模式
            with torch.cuda.amp.autocast():  # 启用自动混合精度
                features = model(input_data)
                targets = torch.zeros(batch_size * 2, dtype=torch.long, device=device)
                loss = nn.CrossEntropyLoss()(features, targets)
            # 记录前向传播后内存
            forward_memory = get_gpu_memory()
            print(f"    前向传播后GPU内存: {forward_memory['allocated_mb']:.2f} MB")

            # 模拟反向传播
            # 反向传播 - 使用混合精度
            loss.backward()

            # 记录反向传播后内存
            backward_memory = get_gpu_memory()
            print(f"    反向传播后GPU内存: {backward_memory['allocated_mb']:.2f} MB")
            print(f"    峰值GPU内存: {torch.cuda.max_memory_allocated()/1024/1024:.2f} MB")
            
            # 清理梯度
            model.zero_grad()
            del input_data, features, loss
            
            print(f"    ✅ 成功处理 batch_size={batch_size}")
            
        except RuntimeError as e:
            if "out of memory" in str(e):
                print(f"    ❌ OOM! batch_size={batch_size} 显存不足")
                print(f"    峰值GPU内存: {torch.cuda.max_memory_allocated()/1024/1024:.2f} MB")
                break
            else:
                print(f"    ❌ 错误: {e}")
                break
        except Exception as e:
            print(f"    ❌ 未知错误: {e}")
            break

def main():
    print("LlamaEncoder 显存占用测试")
    print("=" * 50)
    
    if not torch.cuda.is_available():
        print("❌ CUDA不可用，无法进行GPU测试")
        return
    
    print(f"GPU设备: {torch.cuda.get_device_name()}")
    print(f"GPU总内存: {torch.cuda.get_device_properties(0).total_memory / 1024 / 1024:.0f} MB")
    
    # 测试不同配置
    configs = {
        "原始配置": LlamaEncoderConfig(out_dim=128),  # 默认配置
        
        "减少层数": LlamaEncoderConfig(
            out_dim=128,
            num_hidden_layers=3,  # 6->3
        ),
        
        "减少注意力头": LlamaEncoderConfig(
            out_dim=128,
            num_attention_heads=4,  # 8->4
        ),
        
        "减少FFN维度": LlamaEncoderConfig(
            out_dim=128,
            intermediate_size=128,  # 256->128
        ),
        
        "全面优化": LlamaEncoderConfig(
            out_dim=128,
            input_dim=1024,
            conv_kernel_size=16,  # 卷积核大小 = 步长
            conv_output_channels=256,  # 卷积输出通道数
            num_hidden_layers=16,  # 6->3
            num_attention_heads=8,
            num_key_value_heads=4,
            intermediate_size=1024,  # 256->128
        ),
    }
    
    # 测试的batch sizes
    batch_sizes = [1, 2, 4, 8, 16, 32, 64, 128, 256]
    
    # for config_name, config in configs.items():
    #     test_model_memory(config_name, config, batch_sizes)
    #
    #     # 清理内存
    #     torch.cuda.empty_cache()
    test_model_memory("全面优化", configs["全面优化"], batch_sizes)
    print("\n=== 总结 ===")
    print("如果原始配置在小batch size下就OOM，说明问题确实在LlamaModel的实现上")
    print("建议尝试减少层数、注意力头数或FFN维度来降低显存占用")

if __name__ == "__main__":
    config = LlamaEncoderConfig(out_dim=128)
    print(config)
    main()
