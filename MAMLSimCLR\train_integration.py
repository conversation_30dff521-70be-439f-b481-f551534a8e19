"""
SimCLR + MAML 集成训练脚本
支持YAML配置文件训练
"""

import os
import sys
import time
import torch
import numpy as np
from tqdm import tqdm
import matplotlib.pyplot as plt
from torch.utils.tensorboard import SummaryWriter
import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(__file__))

from config import get_config
from maml_with_simclr import create_maml_learner, EarlyStopping
from network_traffic_dataset import create_few_shot_dataloader
from utils import set_seed, create_experiment_dir


def train_epoch(**kwargs):
    """
    训练一个epoch

    Args:
        **kwargs: 包含所有必要参数的字典
            - maml_learner: MAML学习器
            - dataloader: 数据加载器
            - epoch: 当前epoch
            - writer: tensorboard writer
            - config: 配置对象
    """
    maml_learner = kwargs.get('maml_learner')
    dataloader = kwargs.get('dataloader')
    epoch = kwargs.get('epoch')
    writer = kwargs.get('writer')
    config = kwargs.get('config')

    if any(x is None for x in [maml_learner, dataloader, epoch, writer, config]):
        raise ValueError("maml_learner, dataloader, epoch, writer, and config are required")

    maml_learner.model.train()

    total_loss = 0.0
    total_acc = 0.0
    num_batches = len(dataloader)

    progress_bar = tqdm(dataloader, desc=f'Epoch {epoch}')

    for batch_idx, (support_x, support_y, query_x, query_y) in enumerate(progress_bar):
        # 前向传播和元更新
        accuracies, losses = maml_learner(support_x, support_y, query_x, query_y)

        # 记录指标
        final_acc = accuracies[-1]
        final_loss = losses[-1] / config.maml.task_num

        total_acc += final_acc
        total_loss += final_loss

        # 更新进度条
        progress_bar.set_postfix({
            'Loss': f'{final_loss:.4f}',
            'Acc': f'{final_acc:.4f}'
        })

        # 记录到tensorboard
        global_step = epoch * num_batches + batch_idx
        if batch_idx % config.logging.log_freq == 0:
            writer.add_scalar('Train/Loss', final_loss, global_step)
            writer.add_scalar('Train/Accuracy', final_acc, global_step)

            # 记录各步骤的准确率
            for step, acc in enumerate(accuracies):
                writer.add_scalar(f'Train/Accuracy_Step_{step}', acc, global_step)

    avg_loss = total_loss / num_batches
    avg_acc = total_acc / num_batches

    return avg_loss, avg_acc


def validate_epoch(**kwargs):
    """
    验证一个epoch

    Args:
        **kwargs: 包含所有必要参数的字典
            - maml_learner: MAML学习器
            - dataloader: 数据加载器
            - epoch: 当前epoch
            - writer: tensorboard writer
            - config: 配置对象
    """
    maml_learner = kwargs.get('maml_learner')
    dataloader = kwargs.get('dataloader')
    epoch = kwargs.get('epoch')
    writer = kwargs.get('writer')
    config = kwargs.get('config')

    if any(x is None for x in [maml_learner, dataloader, epoch, writer, config]):
        raise ValueError("maml_learner, dataloader, epoch, writer, and config are required")

    maml_learner.model.eval()

    total_acc = 0.0
    num_episodes = 0

    with torch.no_grad():
        for support_x, support_y, query_x, query_y in tqdm(dataloader, desc='Validation'):
            batch_size = support_x.size(0)

            for i in range(batch_size):
                # 对每个任务进行微调测试
                acc = maml_learner.finetune(
                    support_x[i:i+1], support_y[i:i+1],
                    query_x[i:i+1], query_y[i:i+1]
                )
                total_acc += acc
                num_episodes += 1

    avg_acc = total_acc / num_episodes

    # 记录到tensorboard
    writer.add_scalar('Val/Accuracy', avg_acc, epoch)

    return avg_acc


def plot_training_curves(train_losses, train_accs, val_accs, save_path):
    """
    绘制训练曲线
    """
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))

    # 损失曲线
    ax1.plot(train_losses, label='Train Loss')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.set_title('Training Loss')
    ax1.legend()
    ax1.grid(True)

    # 准确率曲线
    ax2.plot(train_accs, label='Train Acc')
    ax2.plot(val_accs, label='Val Acc')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Accuracy')
    ax2.set_title('Training and Validation Accuracy')
    ax2.legend()
    ax2.grid(True)

    plt.tight_layout()
    plt.savefig(save_path)
    plt.close()


def setup_experiment(**kwargs):
    """
    设置实验环境

    Args:
        **kwargs: 包含所有必要参数的字典
            - config: 配置对象
            - experiment_name: 实验名称（可选）
            - base_dir: 基础目录（默认"./experiments"）
    """
    config = kwargs.get('config')
    if config is None:
        raise ValueError("config is required")

    experiment_name = kwargs.get('experiment_name')
    base_dir = kwargs.get('base_dir', "./experiments")

    # 设置随机种子
    set_seed(config.training.seed)

    # 设置实验名称
    if experiment_name is not None:
        config.logging.experiment_name = experiment_name
    elif config.logging.experiment_name is None:
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        config.logging.experiment_name = f"simclr_maml_{timestamp}"

    # 创建实验目录
    exp_dir = create_experiment_dir(base_dir, config.logging.experiment_name)

    # 更新保存路径
    config.logging.save_dir = os.path.join(exp_dir, "checkpoints")
    config.logging.log_dir = os.path.join(exp_dir, "logs")

    # 创建目录
    os.makedirs(config.logging.save_dir, exist_ok=True)
    os.makedirs(config.logging.log_dir, exist_ok=True)

    # 保存配置文件
    config_save_path = os.path.join(exp_dir, "config.yaml")
    config.save_config(config_save_path)

    return exp_dir


def main():
    """
    主训练函数
    """
    # 获取配置
    config = get_config()

    # 打印配置信息
    print("="*60)
    print("SimCLR + MAML 集成训练")
    print("="*60)
    config.print_config()

    # 设置实验环境
    exp_dir = setup_experiment(config=config)
    print(f"Experiment directory: {exp_dir}")

    # 设置设备
    device = torch.device(config.training.device if torch.cuda.is_available() else 'cpu')
    config.training.device = device
    print(f"Using device: {device}")

    # 验证必要的配置
    if config.simclr.pretrained_path is None:
        print("Error: pretrained_path is required!")
        print("Please specify it in the config file or use --pretrained_path argument")
        return

    if not os.path.exists(config.simclr.pretrained_path):
        print(f"Error: Pretrained model not found at {config.simclr.pretrained_path}")
        return

    # 创建数据加载器
    print("Creating data loaders...")
    train_loader = create_few_shot_dataloader(config=config, split='train')
    val_loader = create_few_shot_dataloader(config=config, split='val')

    # 检查数据加载器
    if len(train_loader) == 0:
        print("Error: Training dataset is empty!")
        return

    # 如果验证集为空，给出警告
    if len(val_loader) == 0:
        print("Warning: Validation dataset is empty! Validation will be skipped.")
        val_loader = None

    # 获取实际的类别数量
    train_dataset = train_loader.dataset.dataset
    actual_num_classes = len(train_dataset.class_to_idx)
    print(f"Detected {actual_num_classes} classes: {list(train_dataset.class_to_idx.keys())}")

    # 更新配置中的n_way以匹配实际类别数
    if config.maml.n_way != actual_num_classes:
        print(f"Updating n_way from {config.maml.n_way} to {actual_num_classes}")
        config.maml.n_way = actual_num_classes

    # 创建模型
    print("Creating MAML learner...")
    maml_learner = create_maml_learner(config=config)

    # 创建tensorboard writer
    writer = SummaryWriter(config.logging.log_dir)

    # 创建早停机制
    early_stopping = EarlyStopping(
        patience=config.training.early_stopping_patience,
        min_delta=0.001
    )

    # 训练记录
    train_losses = []
    train_accs = []
    val_accs = []
    best_val_acc = 0.0

    print("Starting training...")
    start_time = time.time()

    for epoch in range(config.training.epochs):
        print(f"\nEpoch {epoch + 1}/{config.training.epochs}")

        # 训练
        train_loss, train_acc = train_epoch(
            maml_learner=maml_learner,
            dataloader=train_loader,
            epoch=epoch,
            writer=writer,
            config=config
        )

        # 验证
        if (epoch + 1) % config.evaluation.eval_freq == 0 and val_loader is not None:
            val_acc = validate_epoch(
                maml_learner=maml_learner,
                dataloader=val_loader,
                epoch=epoch,
                writer=writer,
                config=config
            )
            val_accs.append(val_acc)

            print(f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}, Val Acc: {val_acc:.4f}")

            # 保存最佳模型
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                best_model_path = os.path.join(config.logging.save_dir, 'best_model.pth')
                maml_learner.save_checkpoint(best_model_path, epoch, best_val_acc)
                print(f"New best model saved with validation accuracy: {best_val_acc:.4f}")

            # 早停检查
            if early_stopping(val_acc):
                print(f"Early stopping triggered at epoch {epoch + 1}")
                break
        else:
            print(f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}")
            # 如果没有验证集，定期保存模型
            if val_loader is None and train_acc > best_val_acc:
                best_val_acc = train_acc
                best_model_path = os.path.join(config.logging.save_dir, 'best_model.pth')
                maml_learner.save_checkpoint(best_model_path, epoch, best_val_acc)
                print(f"New best model saved with training accuracy: {best_val_acc:.4f}")

        # 记录训练指标
        train_losses.append(train_loss)
        train_accs.append(train_acc)

        # 定期保存检查点
        if (epoch + 1) % config.logging.save_freq == 0:
            checkpoint_path = os.path.join(config.logging.save_dir, f'checkpoint_epoch_{epoch + 1}.pth')
            maml_learner.save_checkpoint(checkpoint_path, epoch, best_val_acc)

    # 训练结束
    total_time = time.time() - start_time
    print(f"\nTraining completed in {total_time:.2f} seconds")
    print(f"Best validation accuracy: {best_val_acc:.4f}")

    # 保存最终模型
    final_model_path = os.path.join(config.logging.save_dir, 'final_model.pth')
    maml_learner.save_checkpoint(final_model_path, config.training.epochs - 1, best_val_acc)

    # 绘制训练曲线
    if val_accs:  # 确保有验证数据
        plot_path = os.path.join(config.logging.save_dir, 'training_curves.png')
        plot_training_curves(train_losses, train_accs, val_accs, plot_path)
        print(f"Training curves saved to {plot_path}")

    # 关闭tensorboard writer
    writer.close()

    print("Training completed successfully!")
    print(f"Results saved in: {exp_dir}")

    # 打印最终结果摘要
    print("\n" + "="*60)
    print("TRAINING SUMMARY")
    print("="*60)
    print(f"Experiment: {config.logging.experiment_name}")
    print(f"Total epochs: {len(train_losses)}")
    print(f"Best validation accuracy: {best_val_acc:.4f}")
    print(f"Training time: {total_time:.2f} seconds")
    print(f"Results directory: {exp_dir}")
    print("="*60)


if __name__ == '__main__':
    main()
