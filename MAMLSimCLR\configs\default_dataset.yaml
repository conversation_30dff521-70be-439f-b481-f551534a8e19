# DEFAULT数据集配置 - 标准train/test目录结构

# 数据相关配置
data:
  data_root: "../data/your_dataset"  # 修改为您的数据集路径
  img_size: 84
  img_channels: 3
  dataset_type: "DEFAULT"           # 使用DEFAULT数据集格式
  classification_type: "multiclass" # 多分类，自动检测所有类别

# SimCLR预训练模型配置
simclr:
  pretrained_path: null
  backbone: "resnet18"
  feature_dim: 512
  freeze_backbone: true   # ✅ 冻结encoder

# MAML配置 - 将根据实际类别数自动调整
maml:
  n_way: 5              # 将根据数据集自动调整
  k_shot: 3             # 3-shot学习
  k_query: 12           # 查询集样本数
  task_num: 4           # 每个batch的任务数
  update_lr: 0.01       # 内循环学习率
  meta_lr: 0.001        # 元学习率
  update_step: 5        # 内循环更新步数
  update_step_test: 10  # 测试时内循环更新步数

# 训练配置
training:
  epochs: 100
  batch_size: 32
  num_workers: 4
  device: "cuda"
  seed: 42
  early_stopping_patience: 15
  gradient_clip_norm: 1.0

# 优化器配置
optimizer:
  type: "adam"
  lr_scheduler:
    type: "cosine"
    T_max: 100
    eta_min: 0.000001

# 保存和日志配置
logging:
  save_dir: "./checkpoints"
  log_dir: "./logs"
  save_freq: 10
  log_freq: 100
  experiment_name: "default_dataset"

# 评估配置
evaluation:
  eval_freq: 5
  test_episodes: 100
  metrics: ["accuracy", "precision", "recall", "f1_score"]

# 数据增强配置
augmentation:
  horizontal_flip: true
  vertical_flip: false
  rotation: 10
  color_jitter:
    brightness: 0.2
    contrast: 0.2
    saturation: 0.2
    hue: 0.1
  gaussian_blur:
    kernel_size: 3
    sigma: [0.1, 2.0]
  normalize:
    mean: [0.485, 0.456, 0.406]
    std: [0.229, 0.224, 0.225]
