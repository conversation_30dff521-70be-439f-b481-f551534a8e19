# -*- coding: utf-8 -*-
"""
多模态数据集
"""

import torch
import numpy as np
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union
from torch.utils.data import Dataset
import pickle

from .image_processor import ImageProcessor
from .sequence_processor import SequenceProcessor
from config.image_config import ImageConfig
from config.sequence_config import SequenceConfig


class MultimodalDataset(Dataset):
    """多模态网络入侵检测数据集"""
    
    def __init__(self, 
                 data_dir: Union[str, Path],
                 image_config: ImageConfig,
                 sequence_config: SequenceConfig,
                 split: str = 'train',
                 transform: Optional[Any] = None,
                 cache_data: bool = True):
        """
        初始化多模态数据集
        
        Args:
            data_dir: 数据目录路径
            image_config: 图像配置
            sequence_config: 序列配置
            split: 数据集分割 ('train', 'val', 'test')
            transform: 数据变换
            cache_data: 是否缓存处理后的数据
        """
        self.data_dir = Path(data_dir)
        self.image_config = image_config
        self.sequence_config = sequence_config
        self.split = split
        self.transform = transform
        self.cache_data = cache_data
        
        # 初始化处理器
        self.image_processor = ImageProcessor(image_config)
        self.sequence_processor = SequenceProcessor(sequence_config)
        
        # 数据缓存
        self.image_cache = {}
        self.sequence_cache = {}
        
        # 加载数据文件列表
        self.data_files = self._load_data_files()
        self.labels = self._load_labels()
        
        # 验证数据
        self._validate_data()
    
    def _load_data_files(self) -> List[Path]:
        """加载数据文件列表"""
        split_dir = self.data_dir / self.split
        if not split_dir.exists():
            raise FileNotFoundError(f"数据分割目录不存在: {split_dir}")
        
        # 查找PCAP文件
        pcap_files = list(split_dir.rglob("*.pcap"))
        if not pcap_files:
            raise ValueError(f"在{split_dir}中未找到PCAP文件")
        
        return sorted(pcap_files)
    
    def _load_labels(self) -> List[int]:
        """加载标签"""
        labels = []
        
        for pcap_file in self.data_files:
            # 根据文件路径推断标签
            # 假设目录结构为: split/class_name/file.pcap
            class_name = pcap_file.parent.name.lower()
            
            # 简单的二分类：normal vs abnormal
            if 'normal' in class_name:
                label = 0
            else:
                label = 1
            
            labels.append(label)
        
        return labels
    
    def _validate_data(self):
        """验证数据完整性"""
        if len(self.data_files) != len(self.labels):
            raise ValueError("数据文件数量与标签数量不匹配")
        
        if len(self.data_files) == 0:
            raise ValueError("数据集为空")
    
    def __len__(self) -> int:
        """返回数据集大小"""
        return len(self.data_files)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """
        获取数据项
        
        Args:
            idx: 数据索引
            
        Returns:
            包含图像、序列数据和标签的字典
        """
        pcap_file = self.data_files[idx]
        label = self.labels[idx]
        
        # 获取图像数据
        image_data = self._get_image_data(pcap_file, idx)
        
        # 获取序列数据
        sequence_data = self._get_sequence_data(pcap_file, idx)
        
        # 应用变换
        if self.transform:
            image_data, sequence_data = self.transform(image_data, sequence_data)
        
        return {
            'image': torch.FloatTensor(image_data),
            'sequence': torch.FloatTensor(sequence_data),
            'label': torch.LongTensor([label]),
            'file_path': str(pcap_file)
        }
    
    def _get_image_data(self, pcap_file: Path, idx: int) -> np.ndarray:
        """
        获取图像数据
        
        Args:
            pcap_file: PCAP文件路径
            idx: 数据索引
            
        Returns:
            图像数据数组
        """
        # 检查缓存
        if self.cache_data and idx in self.image_cache:
            return self.image_cache[idx]
        
        # 处理PCAP文件生成图像
        image = self.image_processor.process_pcap(pcap_file)
        
        if image is None:
            # 如果处理失败，返回零图像
            height, width = self.image_config.image_size
            image = np.zeros((height, width, 3), dtype=np.uint8)
        
        # 应用归一化
        if self.image_config.normalize:
            image = self.image_processor.apply_normalization(image)
        
        # 转换维度顺序 (H, W, C) -> (C, H, W)
        if len(image.shape) == 3:
            image = np.transpose(image, (2, 0, 1))
        
        # 缓存数据
        if self.cache_data:
            self.image_cache[idx] = image
        
        return image
    
    def _get_sequence_data(self, pcap_file: Path, idx: int) -> np.ndarray:
        """
        获取序列数据
        
        Args:
            pcap_file: PCAP文件路径
            idx: 数据索引
            
        Returns:
            序列数据数组
        """
        # 检查缓存
        if self.cache_data and idx in self.sequence_cache:
            return self.sequence_cache[idx]
        
        # 处理PCAP文件生成序列
        sequence_result = self.sequence_processor.process_pcap(pcap_file)
        
        if not sequence_result or 'sequence' not in sequence_result:
            # 如果处理失败，返回零序列
            sequence = np.zeros(
                (self.sequence_config.max_sequence_length, 
                 self.sequence_config.get_total_feature_dim()),
                dtype=np.float32
            )
        else:
            sequence = sequence_result['sequence']
        
        # 归一化序列数据
        sequence = self.sequence_processor.normalize_features(sequence)
        
        # 缓存数据
        if self.cache_data:
            self.sequence_cache[idx] = sequence
        
        return sequence
    
    def get_class_weights(self) -> torch.Tensor:
        """计算类别权重用于平衡训练"""
        unique_labels, counts = np.unique(self.labels, return_counts=True)
        total_samples = len(self.labels)
        
        weights = []
        for label in unique_labels:
            weight = total_samples / (len(unique_labels) * counts[label])
            weights.append(weight)
        
        return torch.FloatTensor(weights)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取数据集统计信息"""
        unique_labels, counts = np.unique(self.labels, return_counts=True)
        
        stats = {
            'total_samples': len(self.data_files),
            'num_classes': len(unique_labels),
            'class_distribution': dict(zip(unique_labels, counts)),
            'image_shape': self.image_config.get_image_shape(),
            'sequence_length': self.sequence_config.max_sequence_length,
            'sequence_feature_dim': self.sequence_config.get_total_feature_dim()
        }
        
        return stats
    
    def save_cache(self, cache_dir: Union[str, Path]):
        """保存缓存数据"""
        if not self.cache_data:
            return
        
        cache_dir = Path(cache_dir)
        cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存图像缓存
        if self.image_cache:
            image_cache_path = cache_dir / f"{self.split}_image_cache.pkl"
            with open(image_cache_path, 'wb') as f:
                pickle.dump(self.image_cache, f)
        
        # 保存序列缓存
        if self.sequence_cache:
            sequence_cache_path = cache_dir / f"{self.split}_sequence_cache.pkl"
            with open(sequence_cache_path, 'wb') as f:
                pickle.dump(self.sequence_cache, f)
    
    def load_cache(self, cache_dir: Union[str, Path]):
        """加载缓存数据"""
        if not self.cache_data:
            return
        
        cache_dir = Path(cache_dir)
        
        # 加载图像缓存
        image_cache_path = cache_dir / f"{self.split}_image_cache.pkl"
        if image_cache_path.exists():
            with open(image_cache_path, 'rb') as f:
                self.image_cache = pickle.load(f)
        
        # 加载序列缓存
        sequence_cache_path = cache_dir / f"{self.split}_sequence_cache.pkl"
        if sequence_cache_path.exists():
            with open(sequence_cache_path, 'rb') as f:
                self.sequence_cache = pickle.load(f)
