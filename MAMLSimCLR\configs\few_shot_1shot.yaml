# 1-shot学习配置 - 极少样本学习场景

# 数据相关配置
data:
  data_root: "../data/ISAC_LWH_RGB"
  img_size: 84
  img_channels: 3
  dataset_type: "ISAC"
  classification_type: "binary"

# SimCLR预训练模型配置
simclr:
  pretrained_path: null
  backbone: "resnet18"
  feature_dim: 512
  freeze_backbone: true   # ✅ 冻结encoder

# MAML配置 - 1-shot设置
maml:
  n_way: 2
  k_shot: 1             # 1-shot学习
  k_query: 15
  task_num: 8           # 增加任务数以补偿少样本
  update_lr: 0.02       # 稍大的内循环学习率
  meta_lr: 0.001
  update_step: 3        # 减少更新步数避免过拟合
  update_step_test: 5

# 训练配置
training:
  epochs: 150
  batch_size: 32
  num_workers: 4
  device: "cuda"
  seed: 42
  early_stopping_patience: 20
  gradient_clip_norm: 1.0

# 优化器配置
optimizer:
  type: "adam"
  lr_scheduler:
    type: "cosine"
    T_max: 150
    eta_min: 0.000001

# 保存和日志配置
logging:
  save_dir: "./checkpoints_1shot"
  log_dir: "./logs_1shot"
  save_freq: 15
  log_freq: 100
  experiment_name: "few_shot_1shot"

# 评估配置
evaluation:
  eval_freq: 5
  test_episodes: 150    # 更多测试以获得稳定结果
  metrics: ["accuracy", "precision", "recall", "f1_score"]

# 数据增强配置（适度增强）
augmentation:
  horizontal_flip: true
  vertical_flip: false
  rotation: 8
  color_jitter:
    brightness: 0.15
    contrast: 0.15
    saturation: 0.15
    hue: 0.08
  gaussian_blur:
    kernel_size: 3
    sigma: [0.1, 1.5]
  normalize:
    mean: [0.485, 0.456, 0.406]
    std: [0.229, 0.224, 0.225]
