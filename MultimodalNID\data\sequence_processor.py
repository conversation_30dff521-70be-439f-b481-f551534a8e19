# -*- coding: utf-8 -*-
"""
序列数据处理器
"""

import numpy as np
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union
from collections import defaultdict
import time

from .base_processor import BaseProcessor
from config.sequence_config import SequenceConfig


class SequenceProcessor(BaseProcessor):
    """序列数据处理器，将PCAP文件转换为时序特征"""
    
    def __init__(self, config: SequenceConfig, logger: Optional[Any] = None):
        """
        初始化序列处理器
        
        Args:
            config: 序列配置对象
            logger: 日志记录器
        """
        super().__init__(config, logger)
        self.protocol_mapping = self._create_protocol_mapping()
        
    def _create_protocol_mapping(self) -> Dict[str, int]:
        """创建协议类型映射"""
        return {
            'TCP': 0,
            'UDP': 1, 
            'ICMP': 2,
            'IP': 3,
            'OTHER': 4
        }
    
    def process_pcap(self, pcap_path: Union[str, Path]) -> Dict[str, np.ndarray]:
        """
        处理PCAP文件，生成序列特征
        
        Args:
            pcap_path: PCAP文件路径
            
        Returns:
            包含序列特征的字典
        """
        if not self.validate_pcap(pcap_path):
            return {}
        
        try:
            # 提取数据包
            packets = self.extract_packets(pcap_path)
            if not packets:
                self.logger.warning(f"PCAP文件无有效数据包: {pcap_path}")
                return {}
            
            # 提取包级特征
            packet_features = self._extract_packet_features(packets)
            
            # 提取流级特征
            flow_features = self._extract_flow_features(packets)
            
            # 提取统计特征
            statistical_features = self._extract_statistical_features(packets)
            
            # 构建时序数据
            sequence_data = self._build_sequence_data(
                packet_features, flow_features, statistical_features
            )
            
            return {
                'sequence': sequence_data,
                'packet_features': packet_features,
                'flow_features': flow_features,
                'statistical_features': statistical_features
            }
            
        except Exception as e:
            self.logger.error(f"处理PCAP文件时发生错误: {e}")
            return {}
    
    def _extract_packet_features(self, packets: List[Any]) -> np.ndarray:
        """
        提取包级特征
        
        Args:
            packets: 数据包列表
            
        Returns:
            包级特征数组 (num_packets, num_features)
        """
        features = []
        prev_timestamp = None
        
        for packet in packets:
            packet_info = self.get_packet_info(packet)
            
            # 基础特征
            feature_vector = []
            
            if 'packet_size' in self.config.packet_features:
                feature_vector.append(packet_info['length'])
            
            if 'protocol_type' in self.config.packet_features:
                protocol_id = self.protocol_mapping.get(packet_info['protocol'], 4)
                feature_vector.append(protocol_id)
            
            if 'tcp_flags' in self.config.packet_features:
                tcp_flags = self._extract_tcp_flags(packet)
                feature_vector.append(tcp_flags)
            
            if 'inter_arrival_time' in self.config.packet_features:
                if prev_timestamp is not None:
                    iat = packet_info['timestamp'] - prev_timestamp
                else:
                    iat = 0.0
                feature_vector.append(iat)
                prev_timestamp = packet_info['timestamp']
            
            if 'payload_size' in self.config.packet_features:
                payload_size = self._get_payload_size(packet)
                feature_vector.append(payload_size)
            
            if 'header_length' in self.config.packet_features:
                header_length = self._get_header_length(packet)
                feature_vector.append(header_length)
            
            features.append(feature_vector)
        
        return np.array(features, dtype=np.float32)
    
    def _extract_flow_features(self, packets: List[Any]) -> np.ndarray:
        """
        提取流级特征
        
        Args:
            packets: 数据包列表
            
        Returns:
            流级特征数组
        """
        if not packets:
            return np.zeros(len(self.config.flow_features), dtype=np.float32)
        
        # 计算流统计信息
        timestamps = [self.get_packet_info(p)['timestamp'] for p in packets]
        packet_sizes = [self.get_packet_info(p)['length'] for p in packets]
        
        features = []
        
        if 'flow_duration' in self.config.flow_features:
            duration = max(timestamps) - min(timestamps) if len(timestamps) > 1 else 0
            features.append(duration)
        
        if 'total_packets' in self.config.flow_features:
            features.append(len(packets))
        
        if 'total_bytes' in self.config.flow_features:
            features.append(sum(packet_sizes))
        
        if 'avg_packet_size' in self.config.flow_features:
            avg_size = np.mean(packet_sizes) if packet_sizes else 0
            features.append(avg_size)
        
        if 'packet_rate' in self.config.flow_features:
            duration = max(timestamps) - min(timestamps) if len(timestamps) > 1 else 1
            packet_rate = len(packets) / max(duration, 1e-6)
            features.append(packet_rate)
        
        if 'byte_rate' in self.config.flow_features:
            duration = max(timestamps) - min(timestamps) if len(timestamps) > 1 else 1
            byte_rate = sum(packet_sizes) / max(duration, 1e-6)
            features.append(byte_rate)
        
        return np.array(features, dtype=np.float32)
    
    def _extract_statistical_features(self, packets: List[Any]) -> np.ndarray:
        """
        提取统计特征
        
        Args:
            packets: 数据包列表
            
        Returns:
            统计特征数组
        """
        if not packets:
            return np.zeros(len(self.config.statistical_features), dtype=np.float32)
        
        # 计算包大小和时间间隔统计
        packet_sizes = [self.get_packet_info(p)['length'] for p in packets]
        timestamps = [self.get_packet_info(p)['timestamp'] for p in packets]
        
        # 计算时间间隔
        inter_arrival_times = []
        for i in range(1, len(timestamps)):
            inter_arrival_times.append(timestamps[i] - timestamps[i-1])
        
        features = []
        
        # 包大小统计
        if 'packet_size_mean' in self.config.statistical_features:
            features.append(np.mean(packet_sizes))
        
        if 'packet_size_std' in self.config.statistical_features:
            features.append(np.std(packet_sizes))
        
        if 'packet_size_min' in self.config.statistical_features:
            features.append(np.min(packet_sizes))
        
        if 'packet_size_max' in self.config.statistical_features:
            features.append(np.max(packet_sizes))
        
        # 时间间隔统计
        if inter_arrival_times:
            if 'iat_mean' in self.config.statistical_features:
                features.append(np.mean(inter_arrival_times))
            
            if 'iat_std' in self.config.statistical_features:
                features.append(np.std(inter_arrival_times))
            
            if 'iat_min' in self.config.statistical_features:
                features.append(np.min(inter_arrival_times))
            
            if 'iat_max' in self.config.statistical_features:
                features.append(np.max(inter_arrival_times))
        else:
            # 如果没有时间间隔数据，填充零值
            iat_features = ['iat_mean', 'iat_std', 'iat_min', 'iat_max']
            for feat in iat_features:
                if feat in self.config.statistical_features:
                    features.append(0.0)
        
        return np.array(features, dtype=np.float32)
    
    def _build_sequence_data(self, packet_features: np.ndarray,
                           flow_features: np.ndarray,
                           statistical_features: np.ndarray) -> np.ndarray:
        """
        构建序列数据
        
        Args:
            packet_features: 包级特征
            flow_features: 流级特征  
            statistical_features: 统计特征
            
        Returns:
            序列数据数组 (seq_len, feature_dim)
        """
        # 处理序列长度
        if len(packet_features) > self.config.max_sequence_length:
            # 截断序列
            if self.config.truncate_strategy == 'tail':
                packet_features = packet_features[-self.config.max_sequence_length:]
            elif self.config.truncate_strategy == 'head':
                packet_features = packet_features[:self.config.max_sequence_length]
            elif self.config.truncate_strategy == 'random':
                start_idx = np.random.randint(0, len(packet_features) - self.config.max_sequence_length + 1)
                packet_features = packet_features[start_idx:start_idx + self.config.max_sequence_length]
        
        # 构建完整特征向量
        sequence_data = []
        
        for i, packet_feat in enumerate(packet_features):
            # 组合不同类型的特征
            combined_features = []
            
            if self.config.extract_packet_features:
                combined_features.extend(packet_feat)
            
            if self.config.extract_flow_features:
                combined_features.extend(flow_features)
            
            if self.config.extract_statistical_features:
                combined_features.extend(statistical_features)
            
            sequence_data.append(combined_features)
        
        sequence_array = np.array(sequence_data, dtype=np.float32)
        
        # 填充或截断到目标长度
        if len(sequence_array) < self.config.max_sequence_length:
            # 填充
            padding_length = self.config.max_sequence_length - len(sequence_array)
            padding = np.full((padding_length, sequence_array.shape[1]), 
                            self.config.padding_value, dtype=np.float32)
            sequence_array = np.vstack([sequence_array, padding])
        
        return sequence_array
    
    def _extract_tcp_flags(self, packet: Any) -> int:
        """提取TCP标志位"""
        if packet.haslayer('TCP'):
            tcp_layer = packet['TCP']
            return int(tcp_layer.flags) if hasattr(tcp_layer, 'flags') else 0
        return 0
    
    def _get_payload_size(self, packet: Any) -> int:
        """获取载荷大小"""
        if packet.haslayer('Raw'):
            return len(packet['Raw'].load)
        return 0
    
    def _get_header_length(self, packet: Any) -> int:
        """获取头部长度"""
        total_length = len(packet)
        payload_length = self._get_payload_size(packet)
        return total_length - payload_length
