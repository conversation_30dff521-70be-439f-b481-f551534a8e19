# -*- coding: utf-8 -*-
"""
基础数据处理器
"""

import logging
import numpy as np
from pathlib import Path
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Tuple, Union
from scapy.all import PcapReader


class BaseProcessor(ABC):
    """基础数据处理器抽象类"""
    
    def __init__(self, config: Any, logger: Optional[logging.Logger] = None):
        """
        初始化基础处理器
        
        Args:
            config: 配置对象
            logger: 日志记录器
        """
        self.config = config
        self.logger = logger or self._setup_logger()
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(self.__class__.__name__)
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    @abstractmethod
    def process_pcap(self, pcap_path: Union[str, Path]) -> Any:
        """
        处理PCAP文件的抽象方法
        
        Args:
            pcap_path: PCAP文件路径
            
        Returns:
            处理后的数据
        """
        pass
    
    def validate_pcap(self, pcap_path: Union[str, Path]) -> bool:
        """
        验证PCAP文件的有效性
        
        Args:
            pcap_path: PCAP文件路径
            
        Returns:
            是否为有效的PCAP文件
        """
        try:
            pcap_path = Path(pcap_path)
            
            # 检查文件是否存在
            if not pcap_path.exists():
                self.logger.warning(f"PCAP文件不存在: {pcap_path}")
                return False
            
            # 检查文件大小
            if pcap_path.stat().st_size == 0:
                self.logger.warning(f"PCAP文件为空: {pcap_path}")
                return False
            
            # 尝试读取文件头
            with PcapReader(str(pcap_path)) as reader:
                try:
                    next(iter(reader))
                    return True
                except StopIteration:
                    self.logger.warning(f"PCAP文件无数据包: {pcap_path}")
                    return False
                except Exception as e:
                    self.logger.warning(f"PCAP文件格式错误: {pcap_path}, 错误: {e}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"验证PCAP文件时发生错误: {e}")
            return False
    
    def extract_packets(self, pcap_path: Union[str, Path], 
                       max_packets: Optional[int] = None) -> List[Any]:
        """
        从PCAP文件中提取数据包
        
        Args:
            pcap_path: PCAP文件路径
            max_packets: 最大数据包数量
            
        Returns:
            数据包列表
        """
        packets = []
        
        try:
            with PcapReader(str(pcap_path)) as reader:
                for i, packet in enumerate(reader):
                    if max_packets and i >= max_packets:
                        break
                    packets.append(packet)
                    
        except Exception as e:
            self.logger.error(f"提取数据包时发生错误: {e}")
            
        return packets
    
    def get_packet_info(self, packet: Any) -> Dict[str, Any]:
        """
        获取数据包基本信息
        
        Args:
            packet: 数据包对象
            
        Returns:
            数据包信息字典
        """
        info = {
            'timestamp': float(packet.time) if hasattr(packet, 'time') else 0.0,
            'length': len(packet),
            'protocol': self._get_protocol_type(packet),
            'src_ip': None,
            'dst_ip': None,
            'src_port': None,
            'dst_port': None
        }
        
        # 提取IP信息
        if packet.haslayer('IP'):
            ip_layer = packet['IP']
            info['src_ip'] = ip_layer.src
            info['dst_ip'] = ip_layer.dst
        
        # 提取端口信息
        if packet.haslayer('TCP'):
            tcp_layer = packet['TCP']
            info['src_port'] = tcp_layer.sport
            info['dst_port'] = tcp_layer.dport
        elif packet.haslayer('UDP'):
            udp_layer = packet['UDP']
            info['src_port'] = udp_layer.sport
            info['dst_port'] = udp_layer.dport
        
        return info
    
    def _get_protocol_type(self, packet: Any) -> str:
        """
        获取数据包协议类型
        
        Args:
            packet: 数据包对象
            
        Returns:
            协议类型字符串
        """
        if packet.haslayer('TCP'):
            return 'TCP'
        elif packet.haslayer('UDP'):
            return 'UDP'
        elif packet.haslayer('ICMP'):
            return 'ICMP'
        elif packet.haslayer('IP'):
            return 'IP'
        else:
            return 'OTHER'
    
    def normalize_features(self, features: np.ndarray, 
                          method: str = 'minmax') -> np.ndarray:
        """
        特征归一化
        
        Args:
            features: 特征数组
            method: 归一化方法 ('minmax', 'zscore')
            
        Returns:
            归一化后的特征数组
        """
        if method == 'minmax':
            min_vals = np.min(features, axis=0)
            max_vals = np.max(features, axis=0)
            # 避免除零
            range_vals = max_vals - min_vals
            range_vals[range_vals == 0] = 1
            return (features - min_vals) / range_vals
            
        elif method == 'zscore':
            mean_vals = np.mean(features, axis=0)
            std_vals = np.std(features, axis=0)
            # 避免除零
            std_vals[std_vals == 0] = 1
            return (features - mean_vals) / std_vals
            
        else:
            raise ValueError(f"不支持的归一化方法: {method}")
    
    def save_processed_data(self, data: Any, output_path: Union[str, Path]) -> bool:
        """
        保存处理后的数据
        
        Args:
            data: 要保存的数据
            output_path: 输出路径
            
        Returns:
            是否保存成功
        """
        try:
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            if isinstance(data, np.ndarray):
                np.save(output_path, data)
            else:
                # 对于其他类型的数据，可以使用pickle或其他序列化方法
                import pickle
                with open(output_path, 'wb') as f:
                    pickle.dump(data, f)
            
            self.logger.info(f"数据已保存到: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存数据时发生错误: {e}")
            return False
