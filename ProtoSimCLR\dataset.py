from torch.utils.data import Dataset
from torchvision import transforms
import os
from torchvision.datasets import ImageFolder
import torch
import numpy as np
from torch import nn
from PIL import Image

class GaussianBlur(object):
    """blur a single image on CPU"""
    def __init__(self, kernel_size):
        radias = kernel_size // 2
        kernel_size = radias * 2 + 1
        self.blur_h = nn.Conv2d(3, 3, kernel_size=(kernel_size, 1),
                                stride=1, padding=0, bias=False, groups=3)
        self.blur_v = nn.Conv2d(3, 3, kernel_size=(1, kernel_size),
                                stride=1, padding=0, bias=False, groups=3)
        self.k = kernel_size
        self.r = radias

        self.blur = nn.Sequential(
            nn.ReflectionPad2d(radias),
            self.blur_h,
            self.blur_v
        )

        self.pil_to_tensor = transforms.ToTensor()
        self.tensor_to_pil = transforms.ToPILImage()

    def __call__(self, img):
        img = self.pil_to_tensor(img).unsqueeze(0)

        sigma = np.random.uniform(0.1, 2.0)
        x = np.arange(-self.r, self.r + 1)
        x = np.exp(-np.power(x, 2) / (2 * sigma * sigma))
        x = x / x.sum()
        x = torch.from_numpy(x).view(1, -1).repeat(3, 1)

        self.blur_h.weight.data.copy_(x.view(3, 1, self.k, 1))
        self.blur_v.weight.data.copy_(x.view(3, 1, 1, self.k))

        with torch.no_grad():
            img = self.blur(img)
            img = img.squeeze()

        img = self.tensor_to_pil(img)

        return img

def get_simclr_pipeline_transform(size, s=1):
    """Return a set of data augmentation transformations as described in the SimCLR paper."""
    color_jitter = transforms.ColorJitter(0.8 * s, 0.8 * s, 0.8 * s, 0.2 * s)
    data_transforms = transforms.Compose([# transforms.RandomResizedCrop(size=size),
                                          transforms.RandomHorizontalFlip(),
                                          transforms.RandomApply([color_jitter], p=0.8),
                                          transforms.RandomGrayscale(p=0.2),
                                          GaussianBlur(kernel_size=int(0.1 * size)),
                                          transforms.ToTensor()])
    return data_transforms

def custom_transform(size):
    """Return a set of data augmentation transformations for custom datasets."""
    return transforms.Compose([
        transforms.Resize((size, size)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
class ContrastiveLearningViewGenerator(object):
    """Take two random crops of one image as the query and key."""

    def __init__(self, base_transform, n_views=2):
        self.base_transform = base_transform
        self.n_views = n_views

    def __call__(self, x):
        return [self.base_transform(x) for i in range(self.n_views)]

class DefaultDataset(Dataset):
    """
    Default数据集类，继承自torch.utils.data.Dataset
    """
    def __init__(self,**kwargs):
        """
        初始化数据集
        :param root_dir: 数据集根目录
        :param transform: 图像转换操作
        """
        root_dir = kwargs.get('root_dir', '')
        self.transform = kwargs.get('transform', None)
        if root_dir is None:
            raise ValueError("root_dir must be provided in kwargs")
        if not os.path.exists(root_dir):
            raise FileNotFoundError(f"Root directory {root_dir} does not exist")
        self.image_paths = []
        self.labels = []
        self.class_idx = {name : i for i, name in enumerate(sorted(os.listdir(root_dir)))}
        # 遍历根目录下的所有子目录和文件：只有根目录下一级子目录是类别名，类别目录下所有可能的文件都是图像文件
        for class_name in os.listdir(root_dir):
            class_dir = os.path.join(root_dir, class_name)
            if os.path.isdir(class_dir):
                # 递归遍历class_dir下所有文件
                for root, _, files in os.walk(class_dir):
                    for filename in files:
                        if filename.endswith('.jpg') or filename.endswith('.png'):
                            self.image_paths.append(os.path.join(root, filename))
                            self.labels.append(self.class_idx[class_name])

    def __len__(self):
        return len(self.image_paths)

    def __getitem__(self, idx):
        img_path = self.image_paths[idx]
        label = self.labels[idx]
        from PIL import Image
        image = Image.open(img_path).convert('RGB')
        if self.transform:
            image = self.transform(image)
        return image, label

class FewShotDataset(Dataset):
    """
    Few-shot学习数据集类，继承自torch.utils.data.Dataset
    """
    def __init__(self, **kwargs):
        """
        初始化FewShot数据集
        :param root: 数据集根目录
        :param split: 数据集划分 ('train' 或 'val')
        :param n_way: 类别数
        :param k_shot: 每个类别的样本数
        :param k_query: 查询样本数 (修正参数名)
        :param episodes_per_epoch: 每个epoch的episode数量
        :param transform: 图像转换操作
        """
        self.root = kwargs.get('root', '')
        self.n_way = kwargs.get('n_way', 5)
        self.k_shot = kwargs.get('k_shot', 3)
        self.k_query = kwargs.get('k_query', 15)  # 修正参数名
        self.episodes_num = kwargs.get('episodes_num', 100)
        self.episode_transform = custom_transform(32)
        if not os.path.exists(self.root):
            raise FileNotFoundError(f"Root directory {self.root} does not exist")

        # 初始化数据集
        self.dataset = DefaultDataset(root_dir=self.root, transform=self.episode_transform)
        self.split_classes = {}
        # 按类别划分数据 - 修复：直接使用image_paths和labels
        for img_path, label in zip(self.dataset.image_paths, self.dataset.labels):
            if label not in self.split_classes:
                self.split_classes[label] = []
            self.split_classes[label].append(img_path)

    def __len__(self):
        return self.episodes_num

    def __getitem__(self,idx):
        """
        获取一个episode的数据
        :param idx: episode索引
        :return: 支持集和查询集图像及其标签
        """
        import random
        import PIL
        # 随机选择n_way个类别
        selected_classes = random.sample(list(self.split_classes.keys()), self.n_way)

        support_x = []
        support_y = []
        query_x = []
        query_y = []

        for episodes_class,class_idx in enumerate(selected_classes):
            images = self.split_classes[class_idx]
            if len(images) < self.k_shot + self.k_query:
                # 数量不足时进行重采样（有放回采样）,会导致支持集和查询集样本重复
                samples = random.choices(images, k=self.k_shot + self.k_query)
            else:
                samples = random.sample(images, self.k_shot + self.k_query)
            # 分割支持集和查询集
            support_samples = samples[:self.k_shot]
            query_samples = samples[self.k_shot:]
            # 添加支持集样本
            for img_path in support_samples:
                image = PIL.Image.open(img_path).convert('RGB')
                if self.episode_transform:
                    image = self.episode_transform(image)
                support_x.append(image)
                support_y.append(episodes_class)
            # 添加查询集样本
            for img_path in query_samples:
                image = PIL.Image.open(img_path).convert('RGB')
                if self.episode_transform:
                    image = self.episode_transform(image)
                query_x.append(image)
                query_y.append(episodes_class)
        # 转换为Tensor
        support_x = torch.stack(support_x)
        support_y = torch.tensor(support_y, dtype=torch.long)
        query_x = torch.stack(query_x)
        query_y = torch.tensor(query_y, dtype=torch.long)
        return support_x, support_y, query_x, query_y

class ContrastiveDataset(Dataset):
    """
    对比学习数据集类，继承自torch.utils.data.Dataset
    """
    def __init__(self, **kwargs):
        """
        初始化对比学习数据集
        :param root: 数据集根目录
        :param transform: 图像转换操作
        """
        self.root = kwargs.get('root', '')
        self.transform = kwargs.get('transform', transforms.ToTensor())
        if not os.path.exists(self.root):
            raise FileNotFoundError(f"Root directory {self.root} does not exist")

        self.dataset = ImageFolder(root=self.root, transform=self.transform)

    def __len__(self):
        return len(self.dataset)

    def __getitem__(self, idx):
        return self.dataset[idx]  # 返回图像和标签元组



class ProtoSCLDataset(Dataset):
    """
    Few-shot学习数据集类，继承自torch.utils.data.Dataset
    """
    def __init__(self, **kwargs):
        """
        初始化FewShot数据集
        :param root: 数据集根目录
        :param split: 数据集划分 ('train' 或 'val')
        :param n_way: 类别数
        :param k_shot: 每个类别的样本数
        :param k_query: 查询样本数 (修正参数名)
        :param episodes_per_epoch: 每个epoch的episode数量
        :param transform: 图像转换操作
        """
        self.kind = kwargs.get('kind', 'fewshot')  # 数据集类型，默认为'fewshot'
        self.root = kwargs.get('root', '')
        self.n_way = kwargs.get('n_way', 5)
        self.k_shot = kwargs.get('k_shot', 1)
        self.k_query = kwargs.get('k_query', 15)  # 修正参数名
        self.cl_n_views = kwargs.get('cl_n_views', 2)  # 对比学习视图数量
        self.cl_batch_size = kwargs.get('cl_batch_size', 64)  # 对比学习批次大小
        self.episodes_num = kwargs.get('episodes_num', 100)  # 每个epoch的episode数量
        self.episode_transform = kwargs.get('transform', custom_transform(32))  # 默认转换为32x32大小的图像
        self.contrastive_transform = kwargs.get('contrastive_transform', ContrastiveLearningViewGenerator(get_simclr_pipeline_transform(32), n_views=self.cl_n_views))
        if not os.path.exists(self.root):
            raise FileNotFoundError(f"Root directory {self.root} does not exist")

        # 初始化数据集
        self.dataset = DefaultDataset(root_dir=self.root, transform=self.episode_transform)
        self.split_classes = {}
        self.all_image_paths = []
        # 按类别划分数据 - 修复：直接使用image_paths和labels
        for img_path, label in zip(self.dataset.image_paths, self.dataset.labels):
            if label not in self.split_classes:
                self.split_classes[label] = []
            self.split_classes[label].append(img_path)
            self.all_image_paths.append(img_path)

    def __len__(self):
        return self.episodes_num

    def __getitem__(self,idx):
        """
        获取一个episode的数据
        :param idx: episode索引
        :return: 支持集和查询集图像及其标签
        """
        import random
        import PIL
        # 随机选择n_way个类别
        selected_classes = random.sample(list(self.split_classes.keys()), self.n_way)
        support_x = []
        support_y = []
        query_x = []
        query_y = []

        for episodes_class,class_idx in enumerate(selected_classes):
            images = self.split_classes[class_idx]
            if len(images) < self.k_shot + self.k_query:
                # 数量不足时进行重采样（有放回采样）,会导致支持集和查询集样本重复
                samples = random.choices(images, k=self.k_shot + self.k_query)
            else:
                samples = random.sample(images, self.k_shot + self.k_query)
            # 分割支持集和查询集
            support_samples = samples[:self.k_shot]
            query_samples = samples[self.k_shot:]
            # 添加支持集样本
            for img_path in support_samples:
                image = PIL.Image.open(img_path).convert('RGB')
                if self.episode_transform:
                    image = self.episode_transform(image)
                support_x.append(image)
                support_y.append(episodes_class)
            # 添加查询集样本
            for img_path in query_samples:
                image = PIL.Image.open(img_path).convert('RGB')
                if self.episode_transform:
                    image = self.episode_transform(image)
                query_x.append(image)
                query_y.append(episodes_class)
        # 转换为Tensor
        support_x = torch.stack(support_x)
        support_y = torch.tensor(support_y, dtype=torch.long)
        query_x = torch.stack(query_x)
        query_y = torch.tensor(query_y, dtype=torch.long)
        episode = (support_x,support_y,query_x,query_y)
        if self.kind == 'fewshot':
            return episode
        # 对比学习数据
        views_batch = []
        cl_paths = random.sample(self.all_image_paths, self.cl_batch_size)
        for img_path in cl_paths:
            image = PIL.Image.open(img_path).convert('RGB')
            views = self.contrastive_transform(image)
            views_batch.append(views)
        views_list = [torch.stack(views) for views in zip(*views_batch)]

        return (episode, views_list)


if __name__ == "__main__":
    # Example usage
    data_path = '../data/ISAC_LWH_RGB/train'
    dataset = ProtoSCLDataset(root=data_path)
    dataloader = torch.utils.data.DataLoader(dataset, batch_size=1, shuffle=True)
    cnt = 0
    for item in dataloader:
        episode, views_list = item
        print("Episode data:", episode[0].shape, episode[1].shape, episode[2].shape, episode[3].shape)
        print("Views data:", len(views_list), views_list[0].shape, views_list[1].shape)
        if cnt == 1:
            break
        #print(item)
        cnt += 1




if __name__ == "__main__":
    # Example usage
    data_path = '../data/ISAC_LWH_RGB/train'
    dataset = ProtoSCLDataset(root=data_path)
    dataloader = torch.utils.data.DataLoader(dataset, batch_size=1, shuffle=True)
    cnt = 0
    for item in dataloader:
        episode, views_list = item
        print("Episode data:", episode[0].shape, episode[1].shape, episode[2].shape, episode[3].shape)
        print("Views data:", len(views_list), views_list[0].shape, views_list[1].shape)
        if cnt == 1:
            break
        #print(item)
        cnt += 1
