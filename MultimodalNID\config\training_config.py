# -*- coding: utf-8 -*-
"""
训练配置类
"""

from dataclasses import dataclass
from typing import Dict, Any, List, Optional
from .base_config import BaseConfig


@dataclass
class TrainingConfig(BaseConfig):
    """训练配置类"""
    
    # 基础训练配置
    batch_size: int = 32
    num_epochs: int = 100
    learning_rate: float = 1e-3
    weight_decay: float = 1e-4
    
    # 优化器配置
    optimizer: str = "adam"  # adam, sgd, adamw
    optimizer_params: Dict[str, Any] = None
    
    # 学习率调度配置
    scheduler: str = "cosine"  # cosine, step, exponential, plateau
    scheduler_params: Dict[str, Any] = None
    
    # 对比学习配置
    temperature: float = 0.07
    use_hard_negatives: bool = True
    negative_sampling_ratio: float = 1.0
    
    # 多模态融合配置
    fusion_strategy: str = "attention"  # concat, attention, gated
    fusion_dropout: float = 0.1
    cross_modal_weight: float = 1.0
    
    # 损失函数配置
    contrastive_loss_weight: float = 1.0
    alignment_loss_weight: float = 0.5
    reconstruction_loss_weight: float = 0.1
    
    # 正则化配置
    gradient_clip_norm: float = 1.0
    label_smoothing: float = 0.0
    mixup_alpha: float = 0.0
    
    # 验证和保存配置
    validation_frequency: int = 1
    save_frequency: int = 10
    early_stopping_patience: int = 20
    save_best_only: bool = True
    
    # 日志配置
    log_frequency: int = 100
    use_tensorboard: bool = True
    use_wandb: bool = False
    wandb_project: str = "multimodal-nid"
    
    # 评估配置
    evaluation_metrics: List[str] = None
    
    def __post_init__(self):
        super().__post_init__()
        
        # 设置默认优化器参数
        if self.optimizer_params is None:
            if self.optimizer == "adam":
                self.optimizer_params = {
                    "betas": (0.9, 0.999),
                    "eps": 1e-8
                }
            elif self.optimizer == "sgd":
                self.optimizer_params = {
                    "momentum": 0.9,
                    "nesterov": True
                }
            elif self.optimizer == "adamw":
                self.optimizer_params = {
                    "betas": (0.9, 0.999),
                    "eps": 1e-8
                }
        
        # 设置默认调度器参数
        if self.scheduler_params is None:
            if self.scheduler == "cosine":
                self.scheduler_params = {
                    "T_max": self.num_epochs,
                    "eta_min": 1e-6
                }
            elif self.scheduler == "step":
                self.scheduler_params = {
                    "step_size": 30,
                    "gamma": 0.1
                }
            elif self.scheduler == "exponential":
                self.scheduler_params = {
                    "gamma": 0.95
                }
            elif self.scheduler == "plateau":
                self.scheduler_params = {
                    "mode": "min",
                    "factor": 0.5,
                    "patience": 10,
                    "threshold": 1e-4
                }
        
        # 设置默认评估指标
        if self.evaluation_metrics is None:
            self.evaluation_metrics = [
                "accuracy",
                "precision",
                "recall", 
                "f1_score",
                "auc_roc",
                "contrastive_accuracy"
            ]
    
    def get_optimizer_config(self) -> Dict[str, Any]:
        """获取优化器配置"""
        config = {
            "type": self.optimizer,
            "lr": self.learning_rate,
            "weight_decay": self.weight_decay
        }
        config.update(self.optimizer_params)
        return config
    
    def get_scheduler_config(self) -> Dict[str, Any]:
        """获取学习率调度器配置"""
        config = {
            "type": self.scheduler
        }
        config.update(self.scheduler_params)
        return config
    
    def validate(self) -> bool:
        """验证训练配置参数"""
        super().validate()
        
        # 验证基础参数
        if self.batch_size <= 0:
            raise ValueError("批次大小必须为正整数")
        
        if self.num_epochs <= 0:
            raise ValueError("训练轮数必须为正整数")
        
        if self.learning_rate <= 0:
            raise ValueError("学习率必须为正数")
        
        if self.weight_decay < 0:
            raise ValueError("权重衰减必须为非负数")
        
        # 验证温度参数
        if self.temperature <= 0:
            raise ValueError("温度参数必须为正数")
        
        # 验证损失权重
        loss_weights = [
            self.contrastive_loss_weight,
            self.alignment_loss_weight,
            self.reconstruction_loss_weight
        ]
        if any(w < 0 for w in loss_weights):
            raise ValueError("损失权重必须为非负数")
        
        # 验证融合策略
        if self.fusion_strategy not in ["concat", "attention", "gated"]:
            raise ValueError("融合策略必须为 'concat', 'attention' 或 'gated'")
        
        # 验证优化器类型
        if self.optimizer not in ["adam", "sgd", "adamw"]:
            raise ValueError("优化器类型必须为 'adam', 'sgd' 或 'adamw'")
        
        # 验证调度器类型
        if self.scheduler not in ["cosine", "step", "exponential", "plateau"]:
            raise ValueError("调度器类型必须为 'cosine', 'step', 'exponential' 或 'plateau'")
        
        return True
