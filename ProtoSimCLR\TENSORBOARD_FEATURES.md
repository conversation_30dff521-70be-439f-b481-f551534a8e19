# ProtoSimCLR Tensorboard日志记录功能

## 🎯 功能概述

ProtoSimCLR现在具备完整的tensorboard日志记录功能，可以实时监控训练过程、可视化模型性能和分析实验结果。

## 📊 已实现的功能

### 1. 核心日志记录功能
- ✅ **标量指标记录**: 损失、准确率、学习率等
- ✅ **直方图记录**: 模型参数分布、梯度分布
- ✅ **模型结构图**: 自动记录模型架构
- ✅ **文本记录**: 配置信息、训练状态等
- ✅ **GPU内存监控**: 实时显示GPU内存使用情况
- ✅ **训练时间跟踪**: 记录训练耗时和进度

### 2. 训练过程监控
- ✅ **Episode级别指标**: 每个episode的loss和accuracy
- ✅ **Epoch级别指标**: 每个epoch的平均性能
- ✅ **学习率变化**: 自动跟踪优化器学习率
- ✅ **模型参数统计**: 定期记录参数和梯度分布
- ✅ **验证指标**: precision, recall, f1-score等详细指标

### 3. 配置管理
- ✅ **配置文件保存**: 自动保存实验配置到JSON文件
- ✅ **实验目录管理**: 按实验名称和时间戳组织日志
- ✅ **配置可视化**: 在tensorboard中显示配置信息

## 🔧 使用方法

### 1. 基本使用
```python
from utils import create_tensorboard_logger
from config_parser import ConfigParser

# 加载配置
config = ConfigParser('configs/multiclass.yaml')

# 创建logger
logger = create_tensorboard_logger(config)

# 记录指标
logger.log_scalar('Train/Loss', loss_value, step)
logger.log_scalar('Train/Accuracy', accuracy, step)

# 关闭logger
logger.close()
```

### 2. 训练集成
训练脚本已经完全集成了tensorboard日志记录：

```bash
# 运行训练（自动启用tensorboard日志记录）
python meta_train.py --config configs/multiclass.yaml

# 查看日志
tensorboard --logdir ./logs/multiclass_malware
```

### 3. 配置选项
在配置文件中可以设置日志相关参数：

```yaml
logging:
  save_dir: "./checkpoints"      # 模型保存目录
  log_dir: "./logs"             # tensorboard日志目录
  save_freq: 15                 # 模型保存频率（epoch）
  log_freq: 100                 # 日志记录频率（episode）
  experiment_name: "multiclass_malware"  # 实验名称
```

## 📈 可视化内容

### 1. Scalars标签页
- **训练指标**:
  - `Train/Episode_Loss`: 每个episode的损失
  - `Train/Episode_Accuracy`: 每个episode的准确率
  - `Train/Avg_Loss`: 平均损失
  - `Train/Avg_Accuracy`: 平均准确率

- **验证指标**:
  - `Validation/Loss`: 验证损失
  - `Validation/Accuracy`: 验证准确率
  - `Validation/Precision`: 精确率
  - `Validation/Recall`: 召回率
  - `Validation/F1_score`: F1分数

- **系统指标**:
  - `Learning_Rate/Group_0`: 学习率变化
  - `GPU_Memory/Allocated_GB`: GPU内存使用
  - `Time/Elapsed_Hours`: 训练时间

### 2. Histograms标签页
- **模型参数**: `Parameters/layer_name`
- **梯度分布**: `Gradients/layer_name`
- **自定义分布**: 损失分布、准确率分布等

### 3. Graphs标签页
- **模型结构图**: 完整的ProtoNet架构可视化
- **数据流图**: 显示前向传播路径

### 4. Text标签页
- **配置信息**: 完整的实验配置
- **训练状态**: 训练完成信息等

## 🚀 快速开始

### 1. 运行演示
```bash
# 测试tensorboard功能
python test_tensorboard.py

# 运行演示（生成示例日志）
python demo_tensorboard.py

# 启动tensorboard
tensorboard --logdir ./logs/multiclass_malware
```

### 2. 查看结果
1. 在浏览器中打开 http://localhost:6006
2. 探索不同的标签页：
   - **Scalars**: 查看训练曲线和指标变化
   - **Histograms**: 分析参数和梯度分布
   - **Graphs**: 查看模型结构
   - **Text**: 查看配置和文本信息

### 3. 实际训练
```bash
# 开始训练（会自动记录到tensorboard）
python meta_train.py --config configs/multiclass.yaml --device cuda

# 在另一个终端启动tensorboard
tensorboard --logdir ./logs
```

## 📁 文件结构

```
ProtoSimCLR/
├── utils.py                    # TensorboardLogger类和工具函数
├── meta_train.py              # 集成了tensorboard的训练脚本
├── test_tensorboard.py        # tensorboard功能测试
├── demo_tensorboard.py        # tensorboard演示脚本
├── configs/
│   ├── multiclass.yaml        # 包含logging配置
│   └── surpvised_finetune.yaml
└── logs/                      # tensorboard日志目录
    └── multiclass_malware/    # 实验日志
        ├── events.out.tfevents.*
        └── config.json
```

## 🔍 高级功能

### 1. 自定义指标记录
```python
# 记录自定义指标
logger.log_scalars({
    'Custom/Metric1': value1,
    'Custom/Metric2': value2,
    'Custom/Metric3': value3
}, step)
```

### 2. 模型参数监控
```python
# 记录模型参数统计
logger.log_model_parameters(model, epoch)
```

### 3. 实验对比
- 使用不同的experiment_name来对比多个实验
- 在tensorboard中可以同时查看多个实验的结果

## 💡 最佳实践

1. **合理设置log_freq**: 避免过于频繁的日志记录影响训练速度
2. **定期清理日志**: 删除不需要的实验日志以节省磁盘空间
3. **使用有意义的实验名称**: 便于后续分析和对比
4. **记录关键超参数**: 在配置中记录所有重要的实验设置
5. **监控GPU内存**: 及时发现内存泄漏问题

## 🎉 总结

ProtoSimCLR现在具备了完整的tensorboard日志记录功能，可以：
- 📊 实时监控训练过程
- 🔍 深入分析模型性能
- 📈 可视化学习曲线
- 🏗️ 查看模型结构
- ⚙️ 跟踪系统资源使用
- 📋 管理实验配置

这些功能将大大提升您的实验效率和结果分析能力！
