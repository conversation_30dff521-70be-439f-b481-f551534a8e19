# -*- coding: utf-8 -*-
"""
多模态数据加载器
"""

import torch
import numpy as np
from typing import Any, Dict, List, Optional, Tuple
from torch.utils.data import DataLoader, WeightedRandomSampler
from torchvision import transforms

from .multimodal_dataset import MultimodalDataset
from config.image_config import ImageConfig
from config.sequence_config import SequenceConfig
from config.training_config import TrainingConfig


class MultimodalDataLoader:
    """多模态数据加载器管理器"""
    
    def __init__(self, 
                 data_dir: str,
                 image_config: ImageConfig,
                 sequence_config: SequenceConfig,
                 training_config: TrainingConfig):
        """
        初始化数据加载器管理器
        
        Args:
            data_dir: 数据目录
            image_config: 图像配置
            sequence_config: 序列配置
            training_config: 训练配置
        """
        self.data_dir = data_dir
        self.image_config = image_config
        self.sequence_config = sequence_config
        self.training_config = training_config
        
        # 创建数据变换
        self.train_transform = self._create_train_transform()
        self.val_transform = self._create_val_transform()
        
        # 创建数据集
        self.train_dataset = None
        self.val_dataset = None
        self.test_dataset = None
        
        self._create_datasets()
    
    def _create_train_transform(self) -> Optional[Any]:
        """创建训练数据变换"""
        if not self.image_config.use_augmentation:
            return None
        
        # 图像增强变换
        image_transforms = []
        
        aug_params = self.image_config.augmentation_params
        
        if aug_params.get('horizontal_flip', False):
            image_transforms.append(transforms.RandomHorizontalFlip(p=0.5))
        
        if aug_params.get('rotation_range', 0) > 0:
            rotation_range = aug_params['rotation_range']
            image_transforms.append(
                transforms.RandomRotation(degrees=rotation_range)
            )
        
        if aug_params.get('brightness_range'):
            brightness_range = aug_params['brightness_range']
            image_transforms.append(
                transforms.ColorJitter(
                    brightness=(brightness_range[0], brightness_range[1])
                )
            )
        
        if aug_params.get('contrast_range'):
            contrast_range = aug_params['contrast_range']
            image_transforms.append(
                transforms.ColorJitter(
                    contrast=(contrast_range[0], contrast_range[1])
                )
            )
        
        # 组合变换
        if image_transforms:
            return MultimodalTransform(
                image_transform=transforms.Compose(image_transforms),
                sequence_transform=self._create_sequence_augmentation()
            )
        
        return None
    
    def _create_val_transform(self) -> Optional[Any]:
        """创建验证数据变换"""
        # 验证时不使用数据增强
        return None
    
    def _create_sequence_augmentation(self) -> Optional[Any]:
        """创建序列数据增强"""
        # 可以添加序列数据的增强方法，如噪声注入、时间扭曲等
        return SequenceAugmentation(
            noise_std=0.01,
            dropout_rate=0.1
        )
    
    def _create_datasets(self):
        """创建数据集"""
        # 训练集
        self.train_dataset = MultimodalDataset(
            data_dir=self.data_dir,
            image_config=self.image_config,
            sequence_config=self.sequence_config,
            split='train',
            transform=self.train_transform,
            cache_data=True
        )
        
        # 验证集
        self.val_dataset = MultimodalDataset(
            data_dir=self.data_dir,
            image_config=self.image_config,
            sequence_config=self.sequence_config,
            split='val',
            transform=self.val_transform,
            cache_data=True
        )
        
        # 测试集
        self.test_dataset = MultimodalDataset(
            data_dir=self.data_dir,
            image_config=self.image_config,
            sequence_config=self.sequence_config,
            split='test',
            transform=self.val_transform,
            cache_data=True
        )
    
    def get_train_loader(self, use_weighted_sampling: bool = True) -> DataLoader:
        """
        获取训练数据加载器
        
        Args:
            use_weighted_sampling: 是否使用加权采样平衡类别
            
        Returns:
            训练数据加载器
        """
        sampler = None
        shuffle = True
        
        if use_weighted_sampling:
            # 计算样本权重
            class_weights = self.train_dataset.get_class_weights()
            sample_weights = [class_weights[label] for label in self.train_dataset.labels]
            
            sampler = WeightedRandomSampler(
                weights=sample_weights,
                num_samples=len(sample_weights),
                replacement=True
            )
            shuffle = False
        
        return DataLoader(
            dataset=self.train_dataset,
            batch_size=self.training_config.batch_size,
            shuffle=shuffle,
            sampler=sampler,
            num_workers=self.training_config.num_workers,
            pin_memory=self.training_config.pin_memory,
            collate_fn=self._collate_fn
        )
    
    def get_val_loader(self) -> DataLoader:
        """获取验证数据加载器"""
        return DataLoader(
            dataset=self.val_dataset,
            batch_size=self.training_config.batch_size,
            shuffle=False,
            num_workers=self.training_config.num_workers,
            pin_memory=self.training_config.pin_memory,
            collate_fn=self._collate_fn
        )
    
    def get_test_loader(self) -> DataLoader:
        """获取测试数据加载器"""
        return DataLoader(
            dataset=self.test_dataset,
            batch_size=self.training_config.batch_size,
            shuffle=False,
            num_workers=self.training_config.num_workers,
            pin_memory=self.training_config.pin_memory,
            collate_fn=self._collate_fn
        )
    
    def _collate_fn(self, batch: List[Dict[str, torch.Tensor]]) -> Dict[str, torch.Tensor]:
        """
        批次数据整理函数
        
        Args:
            batch: 批次数据列表
            
        Returns:
            整理后的批次数据
        """
        # 分离不同类型的数据
        images = torch.stack([item['image'] for item in batch])
        sequences = torch.stack([item['sequence'] for item in batch])
        labels = torch.cat([item['label'] for item in batch])
        file_paths = [item['file_path'] for item in batch]
        
        return {
            'image': images,
            'sequence': sequences,
            'label': labels,
            'file_paths': file_paths
        }
    
    def get_dataset_statistics(self) -> Dict[str, Any]:
        """获取数据集统计信息"""
        stats = {
            'train': self.train_dataset.get_statistics(),
            'val': self.val_dataset.get_statistics(),
            'test': self.test_dataset.get_statistics()
        }
        
        return stats


class MultimodalTransform:
    """多模态数据变换"""
    
    def __init__(self, image_transform: Optional[Any] = None,
                 sequence_transform: Optional[Any] = None):
        """
        初始化多模态变换
        
        Args:
            image_transform: 图像变换
            sequence_transform: 序列变换
        """
        self.image_transform = image_transform
        self.sequence_transform = sequence_transform
    
    def __call__(self, image: np.ndarray, 
                 sequence: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        应用变换
        
        Args:
            image: 图像数据
            sequence: 序列数据
            
        Returns:
            变换后的图像和序列数据
        """
        # 应用图像变换
        if self.image_transform:
            # 转换为PIL图像格式进行变换
            if len(image.shape) == 3 and image.shape[0] == 3:
                # (C, H, W) -> (H, W, C)
                image = np.transpose(image, (1, 2, 0))
            
            # 确保数据类型正确
            if image.dtype != np.uint8:
                image = (image * 255).astype(np.uint8)
            
            # 应用变换
            image = self.image_transform(image)
            
            # 转换回numpy数组
            if hasattr(image, 'numpy'):
                image = image.numpy()
            
            # 转换回 (C, H, W) 格式
            if len(image.shape) == 3 and image.shape[-1] == 3:
                image = np.transpose(image, (2, 0, 1))
        
        # 应用序列变换
        if self.sequence_transform:
            sequence = self.sequence_transform(sequence)
        
        return image, sequence


class SequenceAugmentation:
    """序列数据增强"""
    
    def __init__(self, noise_std: float = 0.01, dropout_rate: float = 0.1):
        """
        初始化序列增强
        
        Args:
            noise_std: 噪声标准差
            dropout_rate: 随机丢弃率
        """
        self.noise_std = noise_std
        self.dropout_rate = dropout_rate
    
    def __call__(self, sequence: np.ndarray) -> np.ndarray:
        """
        应用序列增强
        
        Args:
            sequence: 输入序列
            
        Returns:
            增强后的序列
        """
        # 添加高斯噪声
        if self.noise_std > 0:
            noise = np.random.normal(0, self.noise_std, sequence.shape)
            sequence = sequence + noise
        
        # 随机丢弃
        if self.dropout_rate > 0:
            mask = np.random.random(sequence.shape) > self.dropout_rate
            sequence = sequence * mask
        
        return sequence
