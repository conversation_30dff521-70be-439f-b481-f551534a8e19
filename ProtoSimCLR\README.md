# ProtoSimCLR: Prototype-based SimCLR for Few-Shot Network Intrusion Detection

ProtoSimCLR是一个两阶段的半监督框架，用于恶意流量检测：
- **阶段1**: 使用原型对比学习在网络流量数据上进行预训练
- **阶段2**: 使用元学习进行新网络攻击类型的少样本学习

## 🏗️ 项目结构

```
ProtoSimCLR/
├── configs/                    # 配置文件目录
│   └── multiclass.yaml        # 多分类配置文件
├── models/                     # 模型定义
│   ├── resnet_simclr.py       # SimCLR ResNet backbone
│   └── meta_train.py          # ProtoNet模型
├── pretrained/                 # 预训练模型目录
│   └── checkpoint_9500.pth.tar
├── config_parser.py           # 配置文件解析器
├── dataset.py                 # 数据集类定义
├── trainer.py                 # 训练器类
├── meta_train.py              # 元学习训练脚本
├── finetune_train.py          # 微调训练脚本
├── train.py                   # 统一训练入口
├── test_setup.py              # 设置测试脚本
├── utils.py                   # 工具函数
└── README.md                  # 本文件
```

## 🚀 快速开始

### 1. 环境准备

确保安装以下依赖：
```bash
pip install torch torchvision tqdm tensorboard pyyaml pillow
```

### 2. 数据准备

数据集应按以下结构组织：
```
data/
├── train/
│   ├── normal/
│   │   ├── image1.jpg
│   │   └── image2.jpg
│   └── malicious_type1/
│       ├── image1.jpg
│       └── image2.jpg
└── val/
    ├── normal/
    └── malicious_type1/
```

### 3. 配置设置

编辑 `configs/multiclass.yaml` 文件，设置正确的数据路径和参数：

```yaml
data:
  train_dataset: "path/to/your/train/data"
  val_dataset: "path/to/your/val/data"
  img_size: 32
  img_channels: 3
  dataset_type: "ISAC"

simclr:
  pretrained_path: ./pretrained/checkpoint_9500.pth.tar
  backbone: "resnet18"
  feature_dim: 128
  freeze_backbone: true

protonet:
  n_way: 5              # 类别数
  k_shot: 3            # 支持样本数
  k_query: 12          # 查询样本数
  task_num: 4          # 每批任务数
  meta_lr: 0.001       # 元学习率
```

### 4. 测试设置

运行测试脚本确保一切正常：
```bash
cd ProtoSimCLR
python test_setup.py
```

### 5. 开始训练

#### 元学习训练（推荐）
```bash
python train.py --config configs/multiclass.yaml --mode meta
```

#### 微调训练
```bash
python train.py --config configs/multiclass.yaml --mode finetune
```

#### 直接运行特定训练脚本
```bash
# 元学习训练
python meta_train.py --config configs/multiclass.yaml

# 微调训练
python finetune_train.py --config configs/multiclass.yaml
```

## 📊 监控训练

使用TensorBoard监控训练过程：
```bash
tensorboard --logdir logs/
```

## 🔧 配置说明

### 核心配置项

- **data**: 数据相关配置
  - `train_dataset`: 训练数据路径
  - `val_dataset`: 验证数据路径
  - `img_size`: 图像尺寸
  - `dataset_type`: 数据集类型

- **simclr**: SimCLR预训练模型配置
  - `pretrained_path`: 预训练模型路径
  - `backbone`: 骨干网络类型
  - `freeze_backbone`: 是否冻结骨干网络

- **protonet**: 原型网络配置
  - `n_way`: N-way分类的类别数
  - `k_shot`: K-shot学习的支持样本数
  - `k_query`: 查询样本数
  - `temperature`: 温度参数

- **training**: 训练配置
  - `epochs`: 训练轮数
  - `batch_size`: 批大小
  - `device`: 设备类型

## 🎯 架构特点

### 1. 模块化设计
- 使用配置文件驱动训练
- 支持多种数据集类型
- 灵活的模型配置

### 2. 冻结Encoder架构
- SimCLR encoder权重冻结
- 只训练原型网络部分
- 保持预训练特征表示

### 3. Few-Shot学习
- 支持N-way K-shot学习
- 原型网络进行类别表示
- 余弦相似度计算

### 4. 统一参数传递
- 使用**kwargs统一参数传递
- 避免复杂的参数列表
- 提高代码可维护性

## 📝 使用示例

### 自定义配置
```python
from config_parser import ConfigParser

# 加载配置
config = ConfigParser('configs/multiclass.yaml')

# 修改配置
config.update_config({
    'training': {'epochs': 200},
    'protonet': {'n_way': 10}
})

# 保存修改后的配置
config.save_config('configs/custom.yaml')
```

### 自定义训练
```python
from trainer import ProtoSimCLRTrainer
from config_parser import ConfigParser

# 加载配置
config = ConfigParser('configs/multiclass.yaml')

# 创建训练器
trainer = ProtoSimCLRTrainer(**config.config)

# 自定义训练循环
# ... 你的训练代码
```

## 🐛 故障排除

### 常见问题

1. **CUDA内存不足**
   - 减小batch_size
   - 减小img_size
   - 使用更小的模型

2. **数据加载错误**
   - 检查数据路径是否正确
   - 确保图像格式支持（jpg/png）
   - 检查目录结构

3. **预训练模型加载失败**
   - 检查预训练模型路径
   - 确保模型格式兼容

### 调试模式

设置环境变量启用调试：
```bash
export PYTHONPATH=$PYTHONPATH:$(pwd)
python -u train.py --config configs/multiclass.yaml --mode meta
```

## 📈 性能优化

1. **数据加载优化**
   - 增加num_workers
   - 使用pin_memory=True
   - 预处理数据到内存

2. **训练优化**
   - 使用混合精度训练
   - 梯度累积
   - 学习率调度

3. **模型优化**
   - 选择合适的backbone
   - 调整feature_dim
   - 优化温度参数

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

本项目采用MIT许可证。
