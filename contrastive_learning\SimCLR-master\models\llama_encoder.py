import torch
import torch.nn as nn
from transformers import LlamaConfig, LlamaPreTrainedModel, LlamaModel


class LlamaEncoderConfig(LlamaConfig):
    def __init__(self,
                 input_dim=1024,
                 conv_kernel_size=16,  # 卷积核大小 = 步长
                 conv_output_channels=256,  # 卷积输出通道数
                 out_dim=128,  # 添加输出维度参数
                 **kwargs):
        super().__init__(**kwargs)
        # 自定义参数
        self.conv_kernel_size = conv_kernel_size
        self.conv_output_channels = conv_output_channels
        self.out_dim = out_dim  # 输出维度
        self.hidden_size = conv_output_channels  # hidden_size应与卷积输出通道数一致
        # 计算卷积后序列长度
        self.output_sequence_length = input_dim // conv_kernel_size


class LlamaEncoder(LlamaPreTrainedModel):
    """基于LLaMA的字节序列编码器模型

    输入: [batch_size, input_dim] 的归一化字节流
    输出: [batch_size, out_dim] 的特征向量
    """

    def __init__(self, config):
        super().__init__(config)
        self.config = config

        # 输入卷积层 (无重叠降维)
        self.conv = nn.Conv1d(
            in_channels=1,  # 单通道输入
            out_channels=config.conv_output_channels,
            kernel_size=config.conv_kernel_size,
            stride=config.conv_kernel_size,  # 步长 = 卷积核大小
            padding=0
        )
        self.bn = nn.BatchNorm1d(config.conv_output_channels)
        # 使用自定义初始化创建LLaMA模型
        self.llama = LlamaModel(self._create_llama_config())

        # 全局池化层 (将序列维度压缩为1)
        self.global_pool = nn.AdaptiveAvgPool1d(1)

        # 最终投影层 (将特征维度映射到目标输出维度)
        self.final_projection = nn.Linear(config.conv_output_channels, config.out_dim)

        # 初始化权重
        self.post_init()

    def _create_llama_config(self):
        """创建适配的LLaMA配置"""
        config = LlamaConfig(
            vocab_size=1,  # 不使用词汇表
            hidden_size=self.config.conv_output_channels,  # 与卷积输出通道数一致
            intermediate_size=getattr(self.config, 'intermediate_size', 1024),
            num_hidden_layers=getattr(self.config, 'num_hidden_layers', 6),
            num_attention_heads=getattr(self.config, 'num_attention_heads', 8),
            num_key_value_heads=getattr(self.config, 'num_key_value_heads', 4),
            max_position_embeddings=getattr(self.config, 'max_position_embeddings', 64),
            rms_norm_eps=getattr(self.config, 'rms_norm_eps', 1e-6),
            use_cache=False,  # 训练时不使用缓存
        )
        return config

    def forward(self, input_bytes, attention_mask=None):
        """
        前向传播
        :param input_bytes: 输入字节流 [batch_size, input_dim]
        :param attention_mask: 注意力掩码 [batch_size, output_sequence_length]
        :return: 特征向量 [batch_size, out_dim]
        """

        # 添加通道维度 [batch_size, input_dim] -> [batch_size, 1, input_dim]
        x = input_bytes.unsqueeze(1)

        # 应用一维卷积降维 [batch_size, 1, input_dim] -> [batch_size, conv_output_channels, output_sequence_length]
        conv_output = self.conv(x)
        conv_output
        # 转置维度适应LLaMA输入 [batch_size, conv_output_channels, output_sequence_length]
        # -> [batch_size, output_sequence_length, conv_output_channels]
        conv_output = conv_output.permute(0, 2, 1)

        # 如果没有提供注意力掩码，创建全1掩码
        if attention_mask is None:
            attention_mask = torch.ones(
                conv_output.shape[:2],
                dtype=torch.long,
                device=conv_output.device
            )

        # Llama处理
        llama_outputs = self.llama(
            inputs_embeds=conv_output,
            attention_mask=attention_mask
        )

        # 获取最后一层隐藏状态 [batch_size, output_sequence_length, conv_output_channels]
        sequence_output = llama_outputs.last_hidden_state

        # 转置回卷积输出格式 [batch_size, output_sequence_length, conv_output_channels]
        # -> [batch_size, conv_output_channels, output_sequence_length]
        sequence_output = sequence_output.permute(0, 2, 1)

        # 全局平均池化，将序列维度压缩为1 [batch_size, conv_output_channels, output_sequence_length]
        # -> [batch_size, conv_output_channels, 1]
        pooled_output = self.global_pool(sequence_output)

        # 移除序列维度 [batch_size, conv_output_channels, 1] -> [batch_size, conv_output_channels]
        pooled_output = pooled_output.squeeze(-1)

        # 最终投影到目标维度 [batch_size, conv_output_channels] -> [batch_size, out_dim]
        feature_vector = self.final_projection(pooled_output)

        return feature_vector


if __name__ == '__main__':
    # 创建配置
    config = LlamaEncoderConfig(
        input_dim=1024,
        conv_kernel_size=16,
        conv_output_channels=32,
        out_dim=128,
        num_hidden_layers=6,
        num_attention_heads=8,
        intermediate_size=256
    )

    # 创建一个LLaMA编码器
    encoder = LlamaEncoder(config)
    torch.save(encoder, 'llama_encoder.pth')
    # 创建一个示例字节流 (需要是浮点类型)
    input_bytes = torch.randint(0, 256, (32, 1024)).float()

    # 创建一个示例注意力掩码
    attention_mask = torch.ones(64,64)  # 1024/32=32

    print("=== 模型基本信息 ===")
    print(f"模型类型: {type(encoder).__name__}")
    print(f"输入形状: {input_bytes.shape}")
    print(f"注意力掩码形状: {attention_mask.shape}")

    # 前向传播
    output = encoder(input_bytes, attention_mask)
    print(f"输出形状: {output.shape}")

    print("\n=== 模型架构 ===")
    print(encoder)

    print("\n=== 模型参数统计 ===")
    total_params = 0
    trainable_params = 0

    for name, parameter in encoder.named_parameters():
        param_count = parameter.numel()
        total_params += param_count
        if parameter.requires_grad:
            trainable_params += param_count
        print(f"{name}: {param_count:,} 参数")

    print(f"\n总参数量: {total_params:,}")
    print(f"可训练参数量: {trainable_params:,}")
    print(f"不可训练参数量: {total_params - trainable_params:,}")

    print("\n=== 各模块参数量 ===")
    # 卷积层参数
    conv_params = sum(p.numel() for p in encoder.conv.parameters())
    print(f"卷积层参数量: {conv_params:,}")

    # LLaMA模型参数
    llama_params = sum(p.numel() for p in encoder.llama.parameters())
    print(f"LLaMA模型参数量: {llama_params:,}")

    # 最终投影层参数
    final_proj_params = sum(p.numel() for p in encoder.final_projection.parameters())
    print(f"最终投影层参数量: {final_proj_params:,}")

    # 全局池化层参数
    global_pool_params = sum(p.numel() for p in encoder.global_pool.parameters())
    print(f"全局池化层参数量: {global_pool_params:,}")

    print("\n=== 内存估算 ===")
    # 估算模型内存占用 (假设float32，每个参数4字节)
    memory_mb = total_params * 4 / (1024 * 1024)
    print(f"模型参数内存占用: {memory_mb:.2f} MB")

    # 前向传播时的激活内存估算
    input_memory_mb = input_bytes.numel() * 4 / (1024 * 1024)
    output_memory_mb = output.numel() * 4 / (1024 * 1024)
    print(f"输入数据内存占用: {input_memory_mb:.2f} MB")
    print(f"输出数据内存占用: {output_memory_mb:.2f} MB")

    print("\n=== LLaMA配置详情 ===")
    llama_config = encoder._create_llama_config()
    print(f"隐藏层维度: {llama_config.hidden_size}")
    print(f"注意力头数: {llama_config.num_attention_heads}")
    print(f"键值头数: {llama_config.num_key_value_heads}")
    print(f"Transformer层数: {llama_config.num_hidden_layers}")
    print(f"FFN中间维度: {llama_config.intermediate_size}")
    print(f"最大位置编码: {llama_config.max_position_embeddings}")

