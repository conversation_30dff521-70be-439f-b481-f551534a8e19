# <PERSON> (<EMAIL>)
#
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this file, You
# can obtain one at http://mozilla.org/MPL/2.0/.
# ==============================================================================

foreach($f in gci 1_Pcap *.pcap -Recurse)
{
    0_Tool\SplitCap_2-1\SplitCap -d -p 50000 -b 50000 -r $f.FullName -o 2_Session\AllLayers\$($f.BaseName)
    gci 2_Session\AllLayers\$($f.BaseName) | ?{$_.Length -eq 0} | del

}

0_Tool\finddupe -del 2_Session\AllLayers