# -*- coding: utf-8 -*-
"""
使用示例
演示如何使用双分支多模态对比学习系统
"""

import torch
from pathlib import Path

# 导入配置类
from config import ImageConfig, SequenceConfig, TrainingConfig

# 导入数据处理模块
from data import ImageProcessor, SequenceProcessor, MultimodalDataset, MultimodalDataLoader

# 导入模型
from models import ContrastiveModel

# 导入训练模块
from training import Trainer

# 导入工具
from utils import setup_logger, FileManager


def example_data_processing():
    """示例：数据处理"""
    print("=== 数据处理示例 ===")
    
    # 创建配置
    image_config = ImageConfig()
    sequence_config = SequenceConfig()
    
    # 创建处理器
    image_processor = ImageProcessor(image_config)
    sequence_processor = SequenceProcessor(sequence_config)
    
    # 假设有一个PCAP文件
    pcap_file = "example.pcap"
    
    if Path(pcap_file).exists():
        # 处理图像数据
        print("处理图像数据...")
        image_data = image_processor.process_pcap(pcap_file)
        if image_data is not None:
            print(f"图像数据形状: {image_data.shape}")
            
            # 保存图像
            image_processor.save_image(image_data, "output_image.png")
            print("图像已保存到 output_image.png")
        
        # 处理序列数据
        print("处理序列数据...")
        sequence_data = sequence_processor.process_pcap(pcap_file)
        if sequence_data:
            print(f"序列数据形状: {sequence_data['sequence'].shape}")
            
            # 保存序列数据
            sequence_processor.save_processed_data(sequence_data, "output_sequence.pkl")
            print("序列数据已保存到 output_sequence.pkl")
    else:
        print(f"PCAP文件 {pcap_file} 不存在，跳过数据处理示例")


def example_model_creation():
    """示例：模型创建"""
    print("\n=== 模型创建示例 ===")
    
    # 创建配置
    image_config = ImageConfig(
        encoder_type="resnet18",
        feature_dim=512
    )
    
    sequence_config = SequenceConfig(
        encoder_type="transformer",
        output_feature_dim=512
    )
    
    training_config = TrainingConfig(
        fusion_strategy="attention",
        temperature=0.07
    )
    
    # 创建模型
    model = ContrastiveModel(
        image_config=image_config,
        sequence_config=sequence_config,
        training_config=training_config
    )
    
    print(f"模型创建成功")
    print(f"总参数数: {sum(p.numel() for p in model.parameters()):,}")
    print(f"可训练参数数: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")
    
    # 测试前向传播
    batch_size = 4
    image_input = torch.randn(batch_size, 3, 32, 32)
    sequence_input = torch.randn(batch_size, 100, 64)
    
    with torch.no_grad():
        outputs = model(image_input, sequence_input)
        
    print(f"图像特征形状: {outputs['image_features'].shape}")
    print(f"序列特征形状: {outputs['sequence_features'].shape}")
    print(f"融合特征形状: {outputs['fused_features'].shape}")
    print(f"分类输出形状: {outputs['logits'].shape}")


def example_training_setup():
    """示例：训练设置"""
    print("\n=== 训练设置示例 ===")
    
    # 创建配置
    image_config = ImageConfig()
    sequence_config = SequenceConfig()
    training_config = TrainingConfig(
        batch_size=16,
        num_epochs=10,
        learning_rate=1e-3
    )
    
    # 创建模型
    model = ContrastiveModel(
        image_config=image_config,
        sequence_config=sequence_config,
        training_config=training_config
    )
    
    print("模型和配置创建完成")
    print(f"批次大小: {training_config.batch_size}")
    print(f"训练轮数: {training_config.num_epochs}")
    print(f"学习率: {training_config.learning_rate}")
    print(f"融合策略: {training_config.fusion_strategy}")
    print(f"温度参数: {training_config.temperature}")


def example_config_management():
    """示例：配置管理"""
    print("\n=== 配置管理示例 ===")
    
    # 创建配置
    image_config = ImageConfig(
        image_size=(64, 64),
        encoder_type="resnet50",
        feature_dim=1024
    )
    
    # 保存配置
    config_dict = image_config.to_dict()
    FileManager.save_yaml(config_dict, "example_image_config.yaml")
    print("配置已保存到 example_image_config.yaml")
    
    # 加载配置
    loaded_config_dict = FileManager.load_yaml("example_image_config.yaml")
    if loaded_config_dict:
        loaded_config = ImageConfig.from_dict(loaded_config_dict)
        print("配置加载成功")
        print(f"图像尺寸: {loaded_config.image_size}")
        print(f"编码器类型: {loaded_config.encoder_type}")
        print(f"特征维度: {loaded_config.feature_dim}")


def example_inference():
    """示例：模型推理"""
    print("\n=== 模型推理示例 ===")
    
    # 创建配置和模型
    image_config = ImageConfig()
    sequence_config = SequenceConfig()
    training_config = TrainingConfig()
    
    model = ContrastiveModel(
        image_config=image_config,
        sequence_config=sequence_config,
        training_config=training_config
    )
    
    # 设置为评估模式
    model.eval()
    
    # 创建示例输入
    image_input = torch.randn(1, 3, 32, 32)
    sequence_input = torch.randn(1, 100, 64)
    
    # 推理
    with torch.no_grad():
        # 获取特征嵌入
        embeddings = model.get_embeddings(image_input, sequence_input)
        print(f"图像嵌入形状: {embeddings['image_features'].shape}")
        print(f"序列嵌入形状: {embeddings['sequence_features'].shape}")
        print(f"融合嵌入形状: {embeddings['fused_features'].shape}")
        
        # 获取预测概率
        probabilities = model.predict(image_input, sequence_input)
        print(f"预测概率: {probabilities}")
        
        # 预测类别
        predicted_class = torch.argmax(probabilities, dim=1)
        print(f"预测类别: {predicted_class.item()}")


def main():
    """主函数"""
    print("双分支多模态对比学习网络入侵检测系统 - 使用示例")
    print("=" * 60)
    
    # 设置日志
    logger = setup_logger("Example")
    logger.info("开始运行示例")
    
    try:
        # 运行各个示例
        example_data_processing()
        example_model_creation()
        example_training_setup()
        example_config_management()
        example_inference()
        
        print("\n" + "=" * 60)
        print("所有示例运行完成！")
        
    except Exception as e:
        logger.error(f"运行示例时发生错误: {e}")
        raise


if __name__ == "__main__":
    main()
