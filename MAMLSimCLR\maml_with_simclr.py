"""
基于SimCLR预训练特征的MAML实现
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np

class SimCLRMAML(nn.Module):
    """
    结合SimCLR预训练特征的MAML元学习器
    """
    def __init__(self, **kwargs):
        super(SimCLRMAML, self).__init__()

        # 从kwargs中提取参数
        self.model = kwargs.get('model')
        self.config = kwargs.get('config')

        if self.model is None or self.config is None:
            raise ValueError("model and config are required")

        # MAML超参数
        self.update_lr = kwargs.get('update_lr', self.config.maml.update_lr)
        self.meta_lr = kwargs.get('meta_lr', self.config.maml.meta_lr)
        self.n_way = kwargs.get('n_way', self.config.maml.n_way)
        self.k_shot = kwargs.get('k_shot', self.config.maml.k_shot)
        self.k_query = kwargs.get('k_query', self.config.maml.k_query)
        self.task_num = kwargs.get('task_num', self.config.maml.task_num)
        self.update_step = kwargs.get('update_step', self.config.maml.update_step)
        self.update_step_test = kwargs.get('update_step_test', self.config.maml.update_step_test)

        # 优化器 - 只优化可训练的参数（decoder）
        trainable_params = [p for p in self.model.parameters() if p.requires_grad]
        optimizer_type = kwargs.get('optimizer_type', 'adam')

        if optimizer_type.lower() == 'adam':
            self.meta_optimizer = optim.Adam(trainable_params, lr=self.meta_lr)
        elif optimizer_type.lower() == 'sgd':
            self.meta_optimizer = optim.SGD(trainable_params, lr=self.meta_lr)
        else:
            raise ValueError(f"Unsupported optimizer type: {optimizer_type}")

        print(f"✅ Meta-optimizer ({optimizer_type}) created for {len(trainable_params)} trainable parameter groups")
        print(f"✅ Total trainable parameters: {sum(p.numel() for p in trainable_params):,}")

        # 设备
        self.device = kwargs.get('device', self.config.training.device)
        self.model.to(self.device)

    def forward(self, x_support, y_support, x_query, y_query):
        """
        MAML前向传播和元更新

        Args:
            x_support: 支持集输入 [task_num, k_shot*n_way, C, H, W]
            y_support: 支持集标签 [task_num, k_shot*n_way]
            x_query: 查询集输入 [task_num, k_query*n_way, C, H, W]
            y_query: 查询集标签 [task_num, k_query*n_way]

        Returns:
            accuracies: 各更新步骤的准确率
        """
        task_num = x_support.size(0)
        query_size = x_query.size(1)

        # 记录损失和准确率
        losses_query = [0 for _ in range(self.update_step + 1)]
        corrects = [0 for _ in range(self.update_step + 1)]

        for i in range(task_num):
            # 获取当前任务的数据
            x_spt = x_support[i].to(self.device)
            y_spt = y_support[i].to(self.device)
            x_qry = x_query[i].to(self.device)
            y_qry = y_query[i].to(self.device)

            # 1. 初始预测（使用原始参数）
            logits_q = self.model(x_qry)
            loss_q = F.cross_entropy(logits_q, y_qry)
            losses_query[0] += loss_q

            with torch.no_grad():
                pred_q = F.softmax(logits_q, dim=1).argmax(dim=1)
                correct = torch.eq(pred_q, y_qry).sum().item()
                corrects[0] += correct

            # 2. 获取decoder的快速权重（只有decoder参数参与MAML更新）
            fast_weights = list(self.model.get_decoder_params())

            # 3. 内循环更新
            for k in range(self.update_step):
                # 在支持集上计算损失
                logits_spt = self.model(x_spt)
                loss_spt = F.cross_entropy(logits_spt, y_spt)

                # 计算梯度（只对decoder参数）
                grad = torch.autograd.grad(loss_spt, fast_weights, create_graph=True, allow_unused=True)

                # 更新快速权重，处理None梯度
                fast_weights = [p - self.update_lr * g if g is not None else p
                               for p, g in zip(fast_weights, grad)]

                # 在查询集上评估（使用更新后的decoder参数）
                # 注意：不能使用torch.no_grad()，因为需要计算梯度用于元更新
                with torch.no_grad():
                    features = self.model.feature_extractor(x_qry)  # encoder部分不需要梯度

                # decoder部分需要梯度用于元更新
                logits_q = self.model.decoder(features, fast_weights)
                loss_q = F.cross_entropy(logits_q, y_qry)
                losses_query[k + 1] += loss_q

                # 计算准确率（这部分不需要梯度）
                with torch.no_grad():
                    pred_q = F.softmax(logits_q, dim=1).argmax(dim=1)
                    correct = torch.eq(pred_q, y_qry).sum().item()
                    corrects[k + 1] += correct

        # 4. 元更新 - 使用最后一步的查询损失进行元更新
        meta_loss = losses_query[-1] / task_num

        self.meta_optimizer.zero_grad()
        meta_loss.backward()

        # 梯度裁剪（只对可训练参数）
        trainable_params = [p for p in self.model.parameters() if p.requires_grad]
        torch.nn.utils.clip_grad_norm_(trainable_params, max_norm=self.config.training.gradient_clip_norm)

        self.meta_optimizer.step()

        # 计算准确率
        accuracies = np.array(corrects) / (query_size * task_num)

        # 转换损失为标量列表（用于记录）
        losses = [loss.item() if hasattr(loss, 'item') else loss for loss in losses_query]

        return accuracies, losses

    def finetune(self, x_support, y_support, x_query, y_query):
        """
        在新任务上进行微调测试

        Args:
            x_support: 支持集输入
            y_support: 支持集标签
            x_query: 查询集输入
            y_query: 查询集标签

        Returns:
            accuracy: 微调后的准确率
        """
        self.model.eval()

        x_spt = x_support.to(self.device)
        y_spt = y_support.to(self.device)
        x_qry = x_query.to(self.device)
        y_qry = y_query.to(self.device)

        # 获取decoder的快速权重
        fast_weights = list(self.model.get_decoder_params())

        # 内循环更新（测试时使用更多步骤）
        for k in range(self.update_step_test):
            logits_spt = self.model(x_spt)
            loss_spt = F.cross_entropy(logits_spt, y_spt)

            grad = torch.autograd.grad(loss_spt, fast_weights, allow_unused=True)
            fast_weights = [p - self.update_lr * g if g is not None else p
                           for p, g in zip(fast_weights, grad)]

        # 在查询集上测试
        with torch.no_grad():
            features = self.model.feature_extractor(x_qry)
            logits_q = self.model.decoder(features, fast_weights)
            pred_q = F.softmax(logits_q, dim=1).argmax(dim=1)
            accuracy = torch.eq(pred_q, y_qry).float().mean().item()

        return accuracy

    def save_checkpoint(self, path, epoch, best_acc):
        """
        保存检查点
        """
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.meta_optimizer.state_dict(),
            'best_acc': best_acc,
            'config': self.config
        }
        torch.save(checkpoint, path)
        print(f"Checkpoint saved to {path}")

    def load_checkpoint(self, path):
        """
        加载检查点
        """
        checkpoint = torch.load(path, map_location=self.device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.meta_optimizer.load_state_dict(checkpoint['optimizer_state_dict'])

        epoch = checkpoint['epoch']
        best_acc = checkpoint['best_acc']

        print(f"Checkpoint loaded from {path}, epoch: {epoch}, best_acc: {best_acc:.4f}")
        return epoch, best_acc


class EarlyStopping:
    """
    早停机制
    """
    def __init__(self, **kwargs):
        self.patience = kwargs.get('patience', 10)
        self.min_delta = kwargs.get('min_delta', 0.001)
        self.counter = 0
        self.best_score = None
        self.early_stop = False

    def __call__(self, val_acc):
        if self.best_score is None:
            self.best_score = val_acc
        elif val_acc < self.best_score + self.min_delta:
            self.counter += 1
            if self.counter >= self.patience:
                self.early_stop = True
        else:
            self.best_score = val_acc
            self.counter = 0

        return self.early_stop


def create_maml_learner(**kwargs):
    """
    创建MAML学习器

    Args:
        **kwargs: 包含所有必要参数的字典
            - config: 配置对象
            - num_classes: 实际的类别数量（可选）
            - optimizer_type: 优化器类型（默认'adam'）
            - device: 设备（可选）

    Returns:
        SimCLRMAML: MAML学习器
    """
    from simclr_pretrained_model import create_simclr_maml_model

    config = kwargs.get('config')
    if config is None:
        raise ValueError("config is required")

    # 创建模型
    model = create_simclr_maml_model(**kwargs)

    # 创建MAML学习器
    maml_learner = SimCLRMAML(**kwargs, model=model)

    return maml_learner
