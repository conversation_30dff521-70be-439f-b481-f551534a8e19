CUDA_VISIBLE_DEVICES: '2'
# 数据相关配置
data:
  train_dataset: "../data/ISAC_USTC_UJS/Train"
  val_dataset: "../data/CTU_Processed/CTU_IMG/Test"
  img_size: 32
  img_channels: 3
  dataset_type: "FewShotDataset"  # 使用FewShotDataset
  num_classes: 10  # 根据实际类别数调整

# SimCLR预训练模型配置
simclr:
  pretrained_path: "../contrastive_learning/SimCLR-master/runs/Jul13_22-30-50_amax/checkpoint_2000.pth.tar"
  base_model: "resnet18"  # 添加base_model参数
  out_dim: 128           # 添加out_dim参数
  batch_size: 64  # SimCLR预训练时的batch size

# ProtoNet配置 - 多分类设置
ProtoSCL:
  n_way: 10              # 5-way分类（根据实际类别数调整）
  k_shot: 10            # 3-shot学习
  k_query: 12           # 查询集样本数
  episodes_num: 500           # dataset中每个epoch的episode数量
  cl_batch_size: 64  # 每个batch的样本数
  cl_n_views: 2  # 每个样本的视图数
  temperature: 10.0     # 温度参数
  meta_lr: 0.001        # 元学习率
  device: "cuda"

# 训练配置
training:
  epochs: 5000
  num_workers: 4
  device: "cuda"
  seed: 42
  early_stopping_patience: 20
  gradient_clip_norm: 1.0

# 优化器配置
optimizer:
  type: "adam"
  lr: 0.001  # 微调训练时的学习率
  momentum: 0.9  # SGD优化器的动量
  weight_decay: 1e-4  # 权重衰减
  lr_scheduler:
    type: "cosine"
    T_max: 120
    eta_min: 0.000001

# 保存和日志配置
logging:
  save_dir: "./checkpoints/2_stage_PSCL"
  log_dir: "./logs"
  save_freq: 100
  log_freq: 100
  experiment_name: "ProtoSCL_experiment"

# 评估配置
evaluation:
  eval_freq: 20
  test_episodes: 100
  metrics: ["accuracy", "precision", "recall", "f1_score"]


