import torch
import torch.nn as nn
import torch.optim as optim
import torchvision
import torchvision.transforms as transforms
from torch.utils.data import DataLoader
from tqdm import tqdm
import matplotlib.pyplot as plt
import numpy as np
from torchvision import datasets
from torch.utils.data import Dataset
import os


class DefaultDataset(Dataset):
    """
    Default数据集类，继承自torch.utils.data.Dataset
    能够处理ISAC类型的数据
    """
    def __init__(self,**kwargs):
        """
        初始化数据集
        :param root_dir: 数据集根目录
        :param transform: 图像转换操作
        """
        root_dir = kwargs.get('root', '')

        self.transform = kwargs.get('transform', None)
        if root_dir is None:
            raise ValueError("root_dir must be provided in kwargs")
        if not os.path.exists(root_dir):
            raise FileNotFoundError(f"Root directory {root_dir} does not exist")
        self.image_paths = []
        self.labels = []
        self.class_idx = {name : i for i, name in enumerate(sorted(os.listdir(root_dir)))}
        # 遍历根目录下的所有子目录和文件：只有根目录下一级子目录是类别名，类别目录下所有可能的文件都是图像文件
        for class_name in os.listdir(root_dir):
            class_dir = os.path.join(root_dir, class_name)
            if os.path.isdir(class_dir):
                # 递归遍历class_dir下所有文件
                for root, _, files in os.walk(class_dir):
                    for filename in files:
                        if filename.endswith('.jpg') or filename.endswith('.png'):
                            self.image_paths.append(os.path.join(root, filename))
                            self.labels.append(self.class_idx[class_name])

    def __len__(self):
        return len(self.image_paths)

    def __getitem__(self, idx):
        img_path = self.image_paths[idx]
        label = self.labels[idx]
        from PIL import Image
        image = Image.open(img_path).convert('RGB')
        if self.transform:
            image = self.transform(image)
        return image, label


class ValDataset(Dataset):
    """
    Default数据集类，继承自torch.utils.data.Dataset
    能够处理ISAC类型的数据
    """
    def __init__(self,**kwargs):
        """
        初始化数据集
        :param root_dir: 数据集根目录
        :param transform: 图像转换操作
        """
        root_dir = kwargs.get('root', '')

        self.transform = kwargs.get('transform', None)
        if root_dir is None:
            raise ValueError("root_dir must be provided in kwargs")
        if not os.path.exists(root_dir):
            raise FileNotFoundError(f"Root directory {root_dir} does not exist")
        self.image_paths = []
        self.labels = []
        self.class_idx = {name : i for i, name in enumerate(sorted(os.listdir(root_dir)))}
        # 遍历根目录下的所有子目录和文件：只有根目录下一级子目录是类别名，类别目录下所有可能的文件都是图像文件
        for class_name in os.listdir(root_dir):
            class_dir = os.path.join(root_dir, class_name)
            if os.path.isdir(class_dir):
                # 递归遍历class_dir下所有文件
                for root, _, files in os.walk(class_dir):
                    cnt = 0
                    for filename in files:
                        cnt += 1
                        if cnt > 10:
                            break
                        if filename.endswith('.jpg') or filename.endswith('.png'):
                            self.image_paths.append(os.path.join(root, filename))
                            self.labels.append(self.class_idx[class_name])

    def __len__(self):
        return len(self.image_paths)

    def __getitem__(self, idx):
        img_path = self.image_paths[idx]
        label = self.labels[idx]
        from PIL import Image
        image = Image.open(img_path).convert('RGB')
        if self.transform:
            image = self.transform(image)
        return image, label

# ViT Patch Embedding
class PatchEmbedding(nn.Module):
    def __init__(self, img_size=32, patch_size=8, in_chans=3, embed_dim=256):
        super().__init__()
        self.img_size = img_size
        self.patch_size = patch_size
        self.n_patches = (img_size // patch_size) ** 2
        self.proj = nn.Conv2d(in_chans, embed_dim, kernel_size=patch_size, stride=patch_size)

    def forward(self, x):
        x = self.proj(x)  # (B, embed_dim, H/patch, W/patch)
        x = x.flatten(2)  # (B, embed_dim, N)
        x = x.transpose(1, 2)  # (B, N, embed_dim)
        return x

# 简单的Transformer Encoder Block
class TransformerEncoderBlock(nn.Module):
    def __init__(self, embed_dim, num_heads, mlp_ratio=4.0, dropout=0.1):
        super().__init__()
        self.norm1 = nn.LayerNorm(embed_dim)
        self.attn = nn.MultiheadAttention(embed_dim, num_heads, dropout=dropout, batch_first=True)
        self.norm2 = nn.LayerNorm(embed_dim)
        self.mlp = nn.Sequential(
            nn.Linear(embed_dim, int(embed_dim * mlp_ratio)),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(int(embed_dim * mlp_ratio), embed_dim),
            nn.Dropout(dropout),
        )

    def forward(self, x):
        # x = x + self.attn(self.norm1(x), self.norm1(x), self.norm1(x))[0]
        # x = x + self.mlp(self.norm2(x))
        attn_input = self.norm1(x)
        attn_output, _ = self.attn(
            attn_input, attn_input, attn_input
        )
        x = x + attn_output

        # MLP层 (使用残差连接)
        mlp_input = self.norm2(x)
        mlp_output = self.mlp(mlp_input)
        x = x + mlp_output
        return x

# ViT 主体
class ViT(nn.Module):
    def __init__(self, img_size=32, patch_size=8, in_chans=3, num_classes=10, embed_dim=256, depth=6, num_heads=8, mlp_ratio=4.0, dropout=0.1):
        super().__init__()
        self.patch_embed = PatchEmbedding(img_size, patch_size, in_chans, embed_dim)
        num_patches = self.patch_embed.n_patches
        self.cls_token = nn.Parameter(torch.zeros(1, 1, embed_dim))
        self.pos_embed = nn.Parameter(torch.zeros(1, num_patches + 1, embed_dim))
        self.pos_drop = nn.Dropout(p=dropout)
        self.blocks = nn.Sequential(*[
            TransformerEncoderBlock(embed_dim, num_heads, mlp_ratio, dropout)
            for _ in range(depth)
        ])
        self.norm = nn.LayerNorm(embed_dim)
        self.head = nn.Linear(embed_dim, num_classes)
        nn.init.trunc_normal_(self.pos_embed, std=0.02)
        nn.init.trunc_normal_(self.cls_token, std=0.02)

    def forward(self, x):
        B = x.shape[0]
        x = self.patch_embed(x)
        cls_tokens = self.cls_token.expand(B, -1, -1)
        x = torch.cat((cls_tokens, x), dim=1)
        x = x + self.pos_embed
        x = self.pos_drop(x)
        x = self.blocks(x)
        x = self.norm(x)
        cls_token_final = x[:, 0]
        out = self.head(cls_token_final)
        return out


# 训练函数
def train(model, dataloader, optimizer, criterion, epoch):
    model.train()
    total_loss, total_correct, total_samples = 0, 0, 0
    progress_bar = tqdm(dataloader, desc=f'Epoch {epoch + 1} [Training]')

    for images, labels in progress_bar:
        images, labels = images.to(device), labels.to(device)

        # 前向传播
        logits = model(images)

        # 计算损失
        loss = criterion(logits, labels)

        # 反向传播
        optimizer.zero_grad()
        loss.backward()

        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)

        optimizer.step()

        # 统计指标
        _, predicted = torch.max(logits, 1)
        total_loss += loss.item() * images.size(0)
        total_correct += (predicted == labels).sum().item()
        total_samples += labels.size(0)

        progress_bar.set_postfix({
            'Loss': loss.item(),
            'Acc': total_correct / total_samples
        })

    epoch_loss = total_loss / total_samples
    epoch_acc = total_correct / total_samples
    return epoch_loss, epoch_acc


# 测试函数
def evaluate(model, dataloader, criterion):
    model.eval()
    total_loss, total_correct, total_samples = 0, 0, 0

    with torch.no_grad():
        for images, labels in tqdm(dataloader, desc='Evaluating'):
            images, labels = images.to(device), labels.to(device)

            logits = model(images)
            loss = criterion(logits, labels)
            _, predicted = torch.max(logits, 1)

            total_loss += loss.item() * images.size(0)
            total_correct += (predicted == labels).sum().item()
            total_samples += labels.size(0)

    epoch_loss = total_loss / total_samples
    epoch_acc = total_correct / total_samples
    return epoch_loss, epoch_acc

# 展示一些预测结果
def show_predictions(model, dataloader, num_images=6):
    model.eval()
    classes = ('plane', 'car', 'bird', 'cat', 'deer',
               'dog', 'frog', 'horse', 'ship', 'truck')

    fig, axes = plt.subplots(2, 3, figsize=(10, 6))
    axes = axes.flatten()

    for i, (images, labels) in enumerate(dataloader):
        if i >= 1:  # 取第一个batch
            break

        images, labels = images.to(device), labels.to(device)
        logits = model(images)
        _, preds = torch.max(logits, 1)

        images = images.cpu().numpy().transpose(0, 2, 3, 1)
        images = images * 0.5 + 0.5  # 反归一化

        for j in range(min(num_images, len(images))):
            ax = axes[j]
            ax.imshow(images[j])
            ax.set_title(f"True: {classes[labels[j]]}\nPred: {classes[preds[j]]}")
            ax.axis('off')

    plt.tight_layout()
    plt.savefig('vit_random_predictions.png')
    plt.show()


if __name__ == '__main__':
    # 创建随机初始化的ViT模型
    num_classes = 10  # 根据你的数据集类别数调整
    num_epochs = 1000
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    from torchvision.models import resnet18
    #model = ViT(img_size=32, patch_size=8, in_chans=3, num_classes=num_classes, embed_dim=768, depth=12, num_heads=12)
    model = resnet18(pretrained=False, num_classes=num_classes)
    model = model.to(device)
    # static = torch.load('vit_cifar10_best.pth', map_location=device)
    # model.load_state_dict(static, strict=False)
    print(f"模型参数量: {sum(p.numel() for p in model.parameters()) / 1e6:.2f}M")

    # 优化器和学习率调度器
    optimizer = optim.AdamW(model.parameters(), lr=3e-4, weight_decay=0.05)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=num_epochs, eta_min=1e-5)
    criterion = nn.CrossEntropyLoss()

    # 设备配置
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    # 数据预处理 - 添加数据增强
    transform_train = transforms.Compose([
        transforms.Resize((32, 32)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
    ])

    transform_test = transforms.Compose([
        transforms.Resize((32, 32)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
    ])

    # 加载数据集
    train_dataset = ValDataset(root='../ProcessPCAP/final/ISAC218_processed/train', transform=transform_train)
    test_dataset = DefaultDataset(root='../ProcessPCAP/final/ISAC218_processed/val', transform=transform_test)
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True, num_workers=4, pin_memory=True)
    test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False, num_workers=4, pin_memory=True)

    # 训练和测试逻辑

    train_losses, train_accs = [], []
    test_losses, test_accs = [], []

    best_acc = 0.0

    for epoch in range(num_epochs):
        train_loss, train_acc = train(model, train_loader, optimizer, criterion, epoch)
        test_loss, test_acc = evaluate(model, test_loader, criterion)

        # 更新学习率
        scheduler.step()

        train_losses.append(train_loss)
        train_accs.append(train_acc)
        test_losses.append(test_loss)
        test_accs.append(test_acc)

        # 保存最佳模型
        if test_acc > best_acc:
            best_acc = test_acc
            torch.save(model.state_dict(), 'resnet18_cifar10_best.pth')
            print(f"保存最佳模型，准确率: {best_acc:.4f}")

        print(f"Epoch {epoch + 1}/{num_epochs} | "
              f"Train Loss: {train_loss:.4f} Acc: {train_acc:.4f} | "
              f"Test Loss: {test_loss:.4f} Acc: {test_acc:.4f} | "
              f"LR: {scheduler.get_last_lr()[0]:.2e}")

    # 可视化训练结果
    plt.figure(figsize=(12, 5))
    plt.subplot(1, 2, 1)
    plt.plot(range(1, num_epochs + 1), train_losses, 'o-', label='Train')
    plt.plot(range(1, num_epochs + 1), test_losses, 'o-', label='Test')
    plt.xlabel('Epochs')
    plt.ylabel('Loss')
    plt.legend()
    plt.title('Loss Curve')
    plt.grid(True)

    plt.subplot(1, 2, 2)
    plt.plot(range(1, num_epochs + 1), train_accs, 'o-', label='Train')
    plt.plot(range(1, num_epochs + 1), test_accs, 'o-', label='Test')
    plt.xlabel('Epochs')
    plt.ylabel('Accuracy')
    plt.legend()
    plt.title('Accuracy Curve')
    plt.grid(True)

    plt.tight_layout()
    plt.savefig('vit_random_results.png')
    plt.show()