# Sequence Branch Train 内存问题分析

## 问题概述
在使用基于 Llama 的序列数据对比学习训练时，只能设置非常小的 batch size，存在严重的内存限制问题。

## 根本原因分析

### 1. LlamaEncoder 模型复杂度过高

**核心问题：** LlamaEncoder 使用了完整的 LlamaModel，这是一个大型语言模型架构，包含：

- **Transformer 层数：** 6 层 (num_hidden_layers=6)
- **注意力头数：** 8 个 (num_attention_heads=8) 
- **隐藏层维度：** 32 (hidden_size=32，与卷积输出通道数一致)
- **FFN 中间维度：** 256 (intermediate_size=256)
- **序列长度：** 32 (1024/32=32，由卷积降维产生)

**内存消耗估算：**
- 模型参数：约几百万参数
- 注意力机制：O(n²) 复杂度，其中 n=32
- 每个样本的激活内存：大量中间激活值

### 2. 对比学习的内存放大效应

**SimCLR 训练特点：**
- 每个样本生成 2 个视图 (n_views=2)
- 实际 batch size = batch_size × n_views
- 相似度矩阵计算：(2×batch_size)² 的矩阵

**内存消耗：**
```
实际处理样本数 = batch_size × 2
相似度矩阵大小 = (batch_size × 2)²
```

### 3. 序列数据的内存开销

**数据特征：**
- 输入序列长度：1024 字节
- 数据类型：float32 (4字节/元素)
- 每个样本内存：1024 × 4 = 4KB

**批处理内存：**
```
单批次输入内存 = batch_size × 2 × 1024 × 4 bytes
例如 batch_size=32: 32 × 2 × 1024 × 4 = 256KB (仅输入数据)
```

### 4. 梯度和优化器状态

**额外内存需求：**
- 梯度存储：与模型参数同等大小
- Adam 优化器：需要存储动量和二阶矩估计 (2倍参数大小)
- 总计：约 4倍模型参数大小的额外内存

## 具体内存瓶颈

### 1. LlamaModel 的注意力机制
```python
# 在 llama_encoder.py 第 96-99 行
llama_outputs = self.llama(
    inputs_embeds=conv_output,  # [batch_size×2, 32, 32]
    attention_mask=attention_mask
)
```

**问题：**
- 自注意力计算需要 O(序列长度²) 的内存
- 多头注意力进一步放大内存需求
- 6 层 Transformer 累积大量中间激活

### 2. 相似度矩阵计算
```python
# 在 simclr.py 第 34 行
similarity_matrix = torch.matmul(features, features.T)
```

**问题：**
- 矩阵大小：(batch_size×2) × (batch_size×2)
- 例如 batch_size=256: 512×512 = 262,144 个元素
- 内存占用：262,144 × 4 bytes ≈ 1MB (仅相似度矩阵)

### 3. 反向传播的激活存储
- 需要保存所有中间激活用于梯度计算
- LlamaModel 的深层网络产生大量激活值
- 激活检查点未启用，内存使用未优化

## 内存使用估算

假设 batch_size=32：

1. **输入数据：** 32×2×1024×4 = 256KB
2. **模型参数：** ~几MB (LlamaModel)
3. **激活值：** ~几十MB (6层Transformer + 注意力)
4. **梯度：** ~几MB (与参数同大小)
5. **优化器状态：** ~几MB×2 (Adam)
6. **相似度矩阵：** 64×64×4 = 16KB

**总计：** 可能超过 100MB+ 每个 batch

## 为什么只能用小 batch size

1. **GPU 内存限制：** 典型 GPU (如 RTX 3080) 有 10-12GB 显存
2. **内存碎片：** 实际可用内存小于理论值
3. **PyTorch 开销：** 框架本身的内存开销
4. **安全边际：** 需要预留内存防止 OOM

**结果：** 只能使用很小的 batch_size (如 4-16) 才能避免内存溢出

## 建议的解决方案

### 1. 模型架构优化
- 减少 Transformer 层数 (6→2-3层)
- 减少注意力头数 (8→4头)
- 减少 FFN 中间维度 (256→128)

### 2. 训练策略优化
- 启用梯度累积
- 使用混合精度训练 (fp16)
- 启用激活检查点

### 3. 数据处理优化
- 减少序列长度 (1024→512)
- 使用更高效的数据加载

### 4. 硬件升级
- 使用更大显存的 GPU
- 考虑多 GPU 训练
