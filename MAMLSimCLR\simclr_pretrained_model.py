"""
SimCLR预训练模型加载器
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models
import os


class ResNetSimCLR(nn.Module):
    """
    修复版本的ResNetSimCLR，解决torchvision警告
    """
    def __init__(self, **kwargs):
        super(ResNetSimCLR, self).__init__()

        # 从kwargs中提取参数，设置默认值
        base_model = kwargs.get('base_model', 'resnet18')
        out_dim = kwargs.get('out_dim', 512)
        # 使用新的weights参数而不是pretrained
        if base_model == "resnet18":
            backbone = models.resnet18(weights=None,num_classes=out_dim)
        elif base_model == "resnet50":
            backbone = models.resnet50(weights=None,num_classes=out_dim)
        else:
            raise ValueError(f"Invalid backbone architecture: {base_model}. Choose resnet18 or resnet50")
        # 这里不需要修改SimCLR的尾部结构，避免在encoder中假如未训练权重
        self.backbone = backbone
        # 获取最后一层的输入特征维度
        dim_mlp = self.backbone.fc.in_features
        # add mlp projection head
        self.backbone.fc = nn.Sequential(
            nn.Linear(dim_mlp, dim_mlp),
            nn.ReLU(),
            self.backbone.fc
        )

    def forward(self, x):
        return self.backbone(x)


class SimCLRFeatureExtractor(nn.Module):
    """
    基于SimCLR预训练模型的特征提取器
    """
    def __init__(self, **kwargs):
        super(SimCLRFeatureExtractor, self).__init__()

        # 从kwargs中提取参数，设置默认值
        self.backbone_name = kwargs.get('backbone', 'resnet18')
        self.feature_dim = kwargs.get('feature_dim', 512)
        self.freeze = kwargs.get('freeze', False)
        self.pretrained_path = kwargs.get('pretrained_path', None)

        # 创建SimCLR模型
        self.simclr_model = ResNetSimCLR(
            base_model=self.backbone_name,
            out_dim=self.feature_dim
        )

        # 加载预训练权重
        if self.pretrained_path:
            if os.path.exists(self.pretrained_path):
                self.load_pretrained_weights(self.pretrained_path)
                print(f"Loaded pretrained SimCLR model from {self.pretrained_path}")
            else:
                print("Warning: No pretrained weights loaded, using random initialization")

        # 移除投影头，只保留特征提取部分
        self.feature_extractor = nn.Sequential(*list(self.simclr_model.backbone.children())[:-1])

        # 获取特征维度
        self.output_dim = self._get_feature_dim()

        # 是否冻结参数
        if self.freeze:
            self.freeze_parameters()

    def load_pretrained_weights(self, pretrained_path):
        """
        加载预训练权重
        """
        try:
            checkpoint = torch.load(pretrained_path, map_location='cpu')

            # 处理不同的保存格式
            if 'state_dict' in checkpoint:
                state_dict = checkpoint['state_dict']
            elif 'model_state_dict' in checkpoint:
                state_dict = checkpoint['model_state_dict']
            else:
                state_dict = checkpoint
            # 去除module.前缀
            new_state_dict = {k.replace('module.', ''): v for k, v in state_dict.items()}
            # 移除不匹配的键
            model_dict = self.simclr_model.state_dict()
            filtered_dict = {}

            for k, v in new_state_dict.items():
                if k in model_dict and model_dict[k].shape == v.shape:
                    filtered_dict[k] = v
                else:
                    print(f"Skipping parameter {k} due to shape mismatch or absence")


            model_dict.update(filtered_dict)
            self.simclr_model.load_state_dict(model_dict)

        except Exception as e:
            print(f"Error loading pretrained weights: {e}")
            print("Using random initialization instead")

    def _get_feature_dim(self):
        """
        获取特征维度
        """
        with torch.no_grad():
            dummy_input = torch.randn(1, 3, 84, 84)
            features = self.feature_extractor(dummy_input)
            return features.view(features.size(0), -1).size(1)

    def freeze_parameters(self):
        """
        冻结特征提取器参数
        """
        for param in self.feature_extractor.parameters():
            param.requires_grad = False
        print("Feature extractor parameters frozen")

    def unfreeze_parameters(self):
        """
        解冻特征提取器参数
        """
        for param in self.feature_extractor.parameters():
            param.requires_grad = True
        print("Feature extractor parameters unfrozen")

    def forward(self, x):
        """
        前向传播
        """
        features = self.feature_extractor(x)
        # 展平特征
        features = features.view(features.size(0), -1)
        return features


class SimCLRMAMLNetwork(nn.Module):
    """
    结合SimCLR特征提取器（冻结）和CNN解码器（可训练）的网络
    """
    def __init__(self, **kwargs):
        super(SimCLRMAMLNetwork, self).__init__()

        # 从kwargs中提取参数，设置默认值
        self.feature_extractor = kwargs.get('feature_extractor')
        self.num_classes = kwargs.get('num_classes', 2)
        self.hidden_dim = kwargs.get('hidden_dim', 256)
        self.freeze = kwargs.get('freeze', True)
        if self.feature_extractor is None:
            raise ValueError("feature_extractor is required")


        print(f"✅ SimCLR encoder frozen. Feature dimension: {self.feature_extractor.output_dim}")
        print(f"✅ Encoder freeze status: {not any(p.requires_grad for p in self.feature_extractor.parameters())}")

        # MLP解码器（可训练）
        self.decoder = MLPDecoder(
            input_dim=self.feature_extractor.output_dim,
            num_classes=self.num_classes,
            hidden_dim=self.hidden_dim
        )

        trainable_params = sum(p.numel() for p in self.decoder.parameters() if p.requires_grad)
        print(f"✅ MLP decoder created with {trainable_params:,} trainable parameters")

    def forward(self, x, params=None):
        """
        前向传播

        Args:
            x: 输入图像
            params: MAML快速权重参数（可选）
        """
        # 使用冻结的SimCLR encoder提取特征

        with torch.no_grad():
            self.feature_extractor.eval()  # 确保始终处于eval模式
            features = self.feature_extractor(x)

        # 使用可训练的CNN decoder进行分类
        return self.decoder(features, params)

    def get_decoder_params(self):
        """
        获取解码器参数（用于MAML更新）
        """
        return list(self.decoder.parameters())

    def get_trainable_params(self):
        """
        获取所有可训练参数
        """
        return self.get_decoder_params()

    def freeze_encoder(self):
        """
        确保encoder被冻结
        """
        self.feature_extractor.eval()
        for param in self.feature_extractor.parameters():
            param.requires_grad = False

    def train(self, mode=True):
        """
        重写train方法，确保encoder始终处于eval模式
        """
        super().train(mode)
        # 确保encoder始终处于eval模式
        self.feature_extractor.eval()
        return self

    # 为了向后兼容，保留原来的方法名
    def get_classifier_params(self):
        """
        获取分类器参数（向后兼容）
        """
        return self.get_decoder_params()


def create_simclr_maml_model(**kwargs):
    """
    创建SimCLR+MAML集成模型

    Args:
        **kwargs: 包含所有必要参数的字典
            - config: 配置对象
            - num_classes: 实际的类别数量（可选）
            - hidden_dim: 隐藏层维度（默认256）

    Returns:
        SimCLRMAMLNetwork: 集成模型
    """
    config = kwargs.get('config')
    if config is None:
        raise ValueError("config is required")

    num_classes = kwargs.get('num_classes')
    hidden_dim = kwargs.get('hidden_dim', 256)

    # 创建特征提取器
    feature_extractor = SimCLRFeatureExtractor(
        backbone=config.simclr.backbone,
        feature_dim=config.simclr.feature_dim,
        pretrained_path=config.simclr.pretrained_path,
        freeze=config.simclr.freeze_backbone
    )

    # 确定类别数量
    if num_classes is None:
        num_classes = config.maml.n_way

    # 创建完整网络
    model = SimCLRMAMLNetwork(
        feature_extractor=feature_extractor,
        num_classes=num_classes,
        hidden_dim=hidden_dim
    )

    return model


class MLPDecoder(nn.Module):
    """
    MLP解码器网络，用于元学习训练
    使用纯线性层，便于实现MAML的函数式前向传播
    """
    def __init__(self, **kwargs):
        super(MLPDecoder, self).__init__()

        # 从kwargs中提取参数，设置默认值
        self.input_dim = kwargs.get('input_dim')
        self.num_classes = kwargs.get('num_classes')
        self.hidden_dim = kwargs.get('hidden_dim', 256)

        if self.input_dim is None or self.num_classes is None:
            raise ValueError("input_dim and num_classes are required")

        # MLP解码器网络
        self.layers = nn.Sequential(
            nn.Linear(self.input_dim, self.hidden_dim),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5),
            nn.Linear(self.hidden_dim, self.hidden_dim // 2),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5),
            nn.Linear(self.hidden_dim // 2, self.num_classes)
        )

        # 初始化权重
        self._initialize_weights()

    def _initialize_weights(self):
        """初始化网络权重"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                nn.init.constant_(m.bias, 0)

    def forward(self, features, params=None):
        """前向传播"""
        if params is not None:
            return self.functional_forward(features, params)

        return self.layers(features)

    def functional_forward(self, features, params):
        """使用给定参数进行函数式前向传播"""
        x = features

        # 第一个线性层
        weight = params[0]
        bias = params[1]
        x = F.linear(x, weight, bias)
        x = F.relu(x)
        x = F.dropout(x, p=0.5, training=self.training)

        # 第二个线性层
        weight = params[2]
        bias = params[3]
        x = F.linear(x, weight, bias)
        x = F.relu(x)
        x = F.dropout(x, p=0.5, training=self.training)

        # 输出层
        weight = params[4]
        bias = params[5]
        x = F.linear(x, weight, bias)

        return x

    def get_trainable_params(self):
        """获取可训练参数"""
        return list(self.parameters())
