checkpoint_dir: ./checkpoints
data_root: ./data
dataset_name: network_traffic
device: cuda
dropout_rate: 0.1
encoder_type: transformer
experiment_name: multimodal_nid
extract_flow_features: true
extract_packet_features: true
extract_statistical_features: true
feature_dim: 64
flow_features:
- flow_duration
- total_packets
- total_bytes
- avg_packet_size
- packet_rate
- byte_rate
log_dir: ./logs
log_format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
log_level: INFO
max_sequence_length: 100
num_workers: 4
output_feature_dim: 512
output_root: ./output
overlap_ratio: 0.5
packet_features:
- packet_size
- protocol_type
- tcp_flags
- inter_arrival_time
- payload_size
- header_length
padding_value: 0.0
pin_memory: true
project_root: .
rnn_config:
  bidirectional: true
  dropout: 0.1
  hidden_size: 256
  num_layers: 2
seed: 42
statistical_features:
- packet_size_mean
- packet_size_std
- packet_size_min
- packet_size_max
- iat_mean
- iat_std
- iat_min
- iat_max
tags: []
tcn_config:
  activation: relu
  dropout: 0.1
  kernel_size: 3
  num_channels:
  - 64
  - 128
  - 256
test_split: 0.15
time_window_size: 10.0
train_split: 0.7
transformer_config:
  activation: relu
  dropout: 0.1
  feedforward_dim: 2048
  hidden_dim: 512
  num_heads: 8
  num_layers: 6
truncate_strategy: tail
use_attention: true
val_split: 0.15
