#!/usr/bin/env python3
"""
调试NaN问题的脚本
"""

import torch
import torch.nn.functional as F
import sys
import os

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.llama_encoder import LlamaEncoder, LlamaEncoderConfig

def check_tensor_health(tensor, name):
    """检查tensor的健康状态"""
    if tensor is None:
        print(f"❌ {name}: None")
        return False
    
    nan_count = torch.isnan(tensor).sum().item()
    inf_count = torch.isinf(tensor).sum().item()
    
    print(f"📊 {name}:")
    print(f"   Shape: {tensor.shape}")
    print(f"   Min: {tensor.min().item():.6f}")
    print(f"   Max: {tensor.max().item():.6f}")
    print(f"   Mean: {tensor.mean().item():.6f}")
    print(f"   Std: {tensor.std().item():.6f}")
    print(f"   NaN count: {nan_count}")
    print(f"   Inf count: {inf_count}")
    
    if nan_count > 0 or inf_count > 0:
        print(f"❌ {name} contains NaN or Inf!")
        return False
    else:
        print(f"✅ {name} is healthy")
        return True

def simulate_info_nce_loss(features, batch_size, temperature=0.07):
    """模拟info_nce_loss计算过程，逐步检查"""
    print("\n=== 模拟 Info NCE Loss 计算 ===")
    
    # Step 1: 创建labels
    print("\n1. 创建labels...")
    labels = torch.cat([torch.arange(batch_size) for i in range(2)], dim=0)
    labels = (labels.unsqueeze(0) == labels.unsqueeze(1)).float()
    labels = labels.to(features.device)
    check_tensor_health(labels, "labels")
    
    # Step 2: 特征归一化
    print("\n2. 特征归一化...")
    features_norm = F.normalize(features, dim=1)
    if not check_tensor_health(features_norm, "normalized_features"):
        return None, None
    
    # Step 3: 计算相似度矩阵
    print("\n3. 计算相似度矩阵...")
    similarity_matrix = torch.matmul(features_norm, features_norm.T)
    if not check_tensor_health(similarity_matrix, "similarity_matrix"):
        return None, None
    
    # Step 4: 移除对角线
    print("\n4. 移除对角线...")
    mask = torch.eye(labels.shape[0], dtype=torch.bool).to(features.device)
    labels_masked = labels[~mask].view(labels.shape[0], -1)
    similarity_masked = similarity_matrix[~mask].view(similarity_matrix.shape[0], -1)
    check_tensor_health(similarity_masked, "similarity_masked")
    
    # Step 5: 选择正负样本
    print("\n5. 选择正负样本...")
    try:
        positives = similarity_masked[labels_masked.bool()].view(labels_masked.shape[0], -1)
        negatives = similarity_masked[~labels_masked.bool()].view(similarity_masked.shape[0], -1)
        check_tensor_health(positives, "positives")
        check_tensor_health(negatives, "negatives")
    except Exception as e:
        print(f"❌ 正负样本选择出错: {e}")
        return None, None
    
    # Step 6: 组合logits
    print("\n6. 组合logits...")
    logits = torch.cat([positives, negatives], dim=1)
    if not check_tensor_health(logits, "logits_before_temperature"):
        return None, None
    
    # Step 7: 温度缩放
    print(f"\n7. 温度缩放 (temperature={temperature})...")
    logits_scaled = logits / temperature
    if not check_tensor_health(logits_scaled, "logits_after_temperature"):
        return None, None
    
    # Step 8: 创建目标标签
    print("\n8. 创建目标标签...")
    target_labels = torch.zeros(logits_scaled.shape[0], dtype=torch.long).to(features.device)
    
    return logits_scaled, target_labels

def test_model_forward(batch_size=4):
    """测试模型前向传播"""
    print(f"\n=== 测试模型前向传播 (batch_size={batch_size}) ===")
    
    # 创建模型 - 使用优化配置避免显存问题
    config = LlamaEncoderConfig(
        out_dim=128,
        num_hidden_layers=2,    # 减少层数
        num_attention_heads=4,  # 减少注意力头
        intermediate_size=64,   # 减少FFN维度
    )
    model = LlamaEncoder(config)
    
    if torch.cuda.is_available():
        model = model.cuda()
        device = 'cuda'
    else:
        device = 'cpu'
    
    model.train()
    
    # 创建输入数据
    input_data = torch.randn(batch_size * 2, 1024, device=device)
    check_tensor_health(input_data, "input_data")
    
    # 前向传播
    print("\n前向传播...")
    try:
        features = model(input_data)
        if not check_tensor_health(features, "model_output_features"):
            return None
    except Exception as e:
        print(f"❌ 模型前向传播出错: {e}")
        return None
    
    return features

def test_different_configurations():
    """测试不同配置下的数值稳定性"""
    print("\n=== 测试不同配置 ===")
    
    batch_size = 4
    features = test_model_forward(batch_size)
    
    if features is None:
        print("❌ 无法获取模型输出，跳过后续测试")
        return
    
    # 测试不同温度参数
    temperatures = [0.01, 0.07, 0.1, 0.5, 1.0]
    
    for temp in temperatures:
        print(f"\n--- 测试温度参数: {temp} ---")
        logits, labels = simulate_info_nce_loss(features, batch_size, temp)
        
        if logits is not None:
            # 计算损失
            criterion = torch.nn.CrossEntropyLoss()
            try:
                loss = criterion(logits, labels)
                print(f"✅ 温度={temp}, 损失={loss.item():.6f}")
            except Exception as e:
                print(f"❌ 温度={temp}, 损失计算出错: {e}")
        else:
            print(f"❌ 温度={temp}, logits计算失败")

def main():
    print("NaN问题调试脚本")
    print("=" * 50)
    
    if torch.cuda.is_available():
        print(f"GPU设备: {torch.cuda.get_device_name()}")
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024 / 1024:.0f} MB")
    else:
        print("使用CPU进行测试")
    
    # 设置随机种子
    torch.manual_seed(42)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(42)
    
    # 运行测试
    test_different_configurations()
    
    print("\n=== 建议 ===")
    print("1. 如果小温度参数(0.01, 0.07)出现NaN，增大温度参数")
    print("2. 如果模型输出就有问题，检查模型初始化")
    print("3. 如果相似度矩阵有问题，可能是特征归一化问题")
    print("4. 建议使用温度参数 >= 0.1")

if __name__ == "__main__":
    main()
