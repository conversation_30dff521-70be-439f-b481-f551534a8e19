# This file may be used to create an environment using:
# $ conda create --name <env> --file <this file>
# platform: linux-64
_libgcc_mutex=0.1=main
absl-py=0.9.0=pypi_0
blas=1.0=mkl
bzip2=1.0.8=h516909a_2
ca-certificates=2019.11.28=hecc5488_0
cachetools=4.0.0=pypi_0
cairo=1.14.12=h80bd089_1005
certifi=2019.11.28=py37hc8dfbb8_1
chardet=3.0.4=pypi_0
cudatoolkit=10.1.243=h6bb024c_0
ffmpeg=4.0.2=ha0c5888_2
fontconfig=2.13.1=he4413a7_1000
freeglut=3.0.0=hf484d3e_1005
freetype=2.9.1=h8a8886c_1
gettext=********=hc5be6a0_1002
glib=2.56.2=had28632_1001
gmp=6.1.2=hf484d3e_1000
gnutls=3.5.19=h2a4e5f8_1
google-auth=1.11.3=pypi_0
google-auth-oauthlib=0.4.1=pypi_0
graphite2=1.3.13=hf484d3e_1000
grpcio=1.27.2=pypi_0
harfbuzz=1.9.0=he243708_1001
hdf5=1.10.2=hc401514_3
icu=58.2=hf484d3e_1000
idna=2.9=pypi_0
intel-openmp=2020.0=166
jasper=2.0.14=h07fcdf6_1
jpeg=9b=h024ee3a_2
ld_impl_linux-64=2.33.1=h53a641e_7
libedit=3.1.20181209=hc058e9b_0
libffi=3.2.1=hd88cf55_4
libgcc-ng=9.1.0=hdf63c60_0
libgfortran=3.0.0=1
libgfortran-ng=7.3.0=hdf63c60_0
libglu=9.0.0=hf484d3e_1000
libiconv=1.15=h516909a_1005
libopencv=3.4.2=hb342d67_1
libpng=1.6.37=hbc83047_0
libstdcxx-ng=9.1.0=hdf63c60_0
libtiff=4.1.0=h2733197_0
libuuid=2.32.1=h14c3975_1000
libxcb=1.13=h14c3975_1002
libxml2=2.9.9=h13577e0_2
markdown=3.2.1=pypi_0
mkl=2020.0=166
mkl-service=2.3.0=py37he904b0f_0
mkl_fft=1.0.15=py37ha843d7b_0
mkl_random=1.1.0=py37hd6b4f25_0
ncurses=6.2=he6710b0_0
nettle=3.3=0
ninja=1.9.0=py37hfd86e86_0
numpy=1.18.1=py37h4f9e942_0
numpy-base=1.18.1=py37hde5b4d6_1
oauthlib=3.1.0=pypi_0
olefile=0.46=py37_0
opencv=3.4.2=py37h6fd60c2_1
openh264=1.8.0=hdbcaa40_1000
openssl=1.1.1d=h516909a_0
pcre=8.44=he1b5a44_0
pillow=7.0.0=py37hb39fc2d_0
pip=20.0.2=py37_1
pixman=0.34.0=h14c3975_1003
protobuf=3.11.3=pypi_0
pthread-stubs=0.4=h14c3975_1001
py-opencv=3.4.2=py37hb342d67_1
pyasn1=0.4.8=pypi_0
pyasn1-modules=0.2.8=pypi_0
python=3.7.6=h0371630_2
python_abi=3.7=1_cp37m
pytorch=1.4.0=py3.7_cuda10.1.243_cudnn7.6.3_0
pyyaml=5.3=pypi_0
readline=7.0=h7b6447c_5
requests=2.23.0=pypi_0
requests-oauthlib=1.3.0=pypi_0
rsa=4.0=pypi_0
setuptools=46.0.0=py37_0
six=1.14.0=py37_0
sqlite=3.31.1=h7b6447c_0
tensorboard=2.1.1=pypi_0
tk=8.6.8=hbc83047_0
torchvision=0.5.0=py37_cu101
urllib3=1.25.8=pypi_0
werkzeug=1.0.0=pypi_0
wheel=0.34.2=py37_0
x264=1!152.20180806=h14c3975_0
xorg-fixesproto=5.0=h14c3975_1002
xorg-inputproto=2.3.2=h14c3975_1002
xorg-kbproto=1.0.7=h14c3975_1002
xorg-libice=1.0.10=h516909a_0
xorg-libsm=1.2.3=h84519dc_1000
xorg-libx11=1.6.9=h516909a_0
xorg-libxau=1.0.9=h14c3975_0
xorg-libxdmcp=1.1.3=h516909a_0
xorg-libxext=1.3.4=h516909a_0
xorg-libxfixes=5.0.3=h516909a_1004
xorg-libxi=1.7.10=h516909a_0
xorg-libxrender=0.9.10=h516909a_1002
xorg-renderproto=0.11.1=h14c3975_1002
xorg-xextproto=7.3.0=h14c3975_1002
xorg-xproto=7.0.31=h14c3975_1007
xz=5.2.4=h14c3975_4
zlib=1.2.11=h7b6447c_3
zstd=1.3.7=h0b5b093_0
