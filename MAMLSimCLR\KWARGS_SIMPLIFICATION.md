# MAMLSimCLR **kwargs 简化总结

## 🎯 简化目标

将MAMLSimCLR目录下的所有脚本统一使用**kwargs来传参，提高代码的灵活性和可维护性。

## 📁 简化的文件

### 1. simclr_pretrained_model.py
**简化的类和函数**:
- `ResNetSimCLR(**kwargs)`
- `SimCLRFeatureExtractor(**kwargs)`
- `SimCLRMAMLNetwork(**kwargs)`
- `MLPDecoder(**kwargs)`
- `create_simclr_maml_model(**kwargs)`

**使用示例**:
```python
# 之前
model = create_simclr_maml_model(config, num_classes=5)

# 现在
model = create_simclr_maml_model(
    config=config,
    num_classes=5,
    hidden_dim=512
)
```

### 2. maml_with_simclr.py
**简化的类和函数**:
- `SimCLRMAML(**kwargs)`
- `EarlyStopping(**kwargs)`
- `create_maml_learner(**kwargs)`

**使用示例**:
```python
# 之前
maml_learner = SimCLRMAML(model, config)

# 现在
maml_learner = SimCLRMAML(
    model=model,
    config=config,
    optimizer_type='adam',
    update_lr=0.01
)
```

### 3. network_traffic_dataset.py
**简化的类和函数**:
- `ISACDataset(**kwargs)`
- `DefaultDataset(**kwargs)`
- `create_few_shot_dataloader(**kwargs)`

**使用示例**:
```python
# 之前
dataset = ISACDataset(data_root, split='train', transform=None, classification_type='binary')

# 现在
dataset = ISACDataset(
    data_root=data_root,
    split='train',
    transform=None,
    classification_type='binary',
    seed=42
)
```

### 4. train_integration.py
**简化的函数**:
- `train_epoch(**kwargs)`
- `validate_epoch(**kwargs)`
- `setup_experiment(**kwargs)`

**使用示例**:
```python
# 之前
train_epoch(maml_learner, dataloader, epoch, writer, config)

# 现在
train_epoch(
    maml_learner=maml_learner,
    dataloader=dataloader,
    epoch=epoch,
    writer=writer,
    config=config
)
```

### 5. config.py
**简化的类和函数**:
- `Config(**kwargs)`
- `load_config(**kwargs)`

**使用示例**:
```python
# 之前
config = load_config('config.yaml')

# 现在
config = load_config(
    config_path='config.yaml',
    epochs=100,
    device='cuda'
)
```

## ✅ 简化的优势

### 1. 参数传递更灵活
```python
# 可以轻松覆盖配置文件中的参数
model = create_simclr_maml_model(
    config=config,
    num_classes=10,      # 覆盖配置中的类别数
    hidden_dim=1024      # 覆盖配置中的隐藏层维度
)
```

### 2. 代码更易扩展
```python
# 添加新参数不需要修改函数签名
maml_learner = create_maml_learner(
    config=config,
    model=model,
    optimizer_type='sgd',    # 新参数
    weight_decay=1e-4        # 新参数
)
```

### 3. 默认值处理更优雅
```python
def __init__(self, **kwargs):
    self.learning_rate = kwargs.get('learning_rate', 0.001)
    self.batch_size = kwargs.get('batch_size', 32)
    self.device = kwargs.get('device', 'cuda')
```

### 4. 参数验证更统一
```python
def __init__(self, **kwargs):
    required_params = ['config', 'model']
    for param in required_params:
        if kwargs.get(param) is None:
            raise ValueError(f"{param} is required")
```

## 🚀 使用指南

### 1. 基本使用模式
```python
# 1. 加载配置
config = load_config(config_path='configs/default.yaml')

# 2. 创建模型
model = create_simclr_maml_model(config=config)

# 3. 创建学习器
maml_learner = create_maml_learner(config=config, model=model)

# 4. 创建数据加载器
train_loader = create_few_shot_dataloader(config=config, split='train')

# 5. 训练
train_epoch(
    maml_learner=maml_learner,
    dataloader=train_loader,
    epoch=0,
    writer=writer,
    config=config
)
```

### 2. 参数覆盖模式
```python
# 覆盖配置文件中的参数
config = load_config(
    config_path='configs/default.yaml',
    epochs=200,           # 覆盖训练轮数
    device='cpu'          # 覆盖设备
)

# 覆盖模型参数
model = create_simclr_maml_model(
    config=config,
    num_classes=5,        # 覆盖类别数
    hidden_dim=512        # 覆盖隐藏层维度
)
```

### 3. 完全自定义模式
```python
# 不使用配置文件，完全通过kwargs传参
dataset = ISACDataset(
    data_root='/path/to/data',
    split='train',
    classification_type='multiclass',
    seed=42
)
```

## 📝 迁移指南

### 从旧版本迁移到新版本

**旧代码**:
```python
model = create_simclr_maml_model(config, num_classes)
maml_learner = SimCLRMAML(model, config)
dataset = ISACDataset(data_root, 'train', transform, 'binary', 42)
```

**新代码**:
```python
model = create_simclr_maml_model(config=config, num_classes=num_classes)
maml_learner = SimCLRMAML(model=model, config=config)
dataset = ISACDataset(
    data_root=data_root,
    split='train',
    transform=transform,
    classification_type='binary',
    seed=42
)
```

## 🎉 总结

通过使用**kwargs统一传参，MAMLSimCLR框架变得：

1. **更灵活**: 可以轻松覆盖任何参数
2. **更易维护**: 添加新参数不需要修改函数签名
3. **更一致**: 所有函数都使用相同的参数传递模式
4. **更易用**: 参数名称明确，减少错误

现在您可以更方便地使用和扩展MAMLSimCLR框架了！

## 📖 使用示例

查看 `simplified_usage_example.py` 文件获取完整的使用示例。
