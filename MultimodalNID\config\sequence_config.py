# -*- coding: utf-8 -*-
"""
序列分支配置类
"""

from dataclasses import dataclass
from typing import List, Optional, Dict, Any
from .base_config import BaseConfig


@dataclass
class SequenceConfig(BaseConfig):
    """序列分支配置类"""
    
    # 序列数据配置
    max_sequence_length: int = 100
    feature_dim: int = 64
    padding_value: float = 0.0
    truncate_strategy: str = "tail"  # head, tail, random
    
    # 特征提取配置
    extract_packet_features: bool = True
    extract_flow_features: bool = True
    extract_statistical_features: bool = True
    
    # 包级特征配置
    packet_features: List[str] = None
    
    # 流级特征配置
    flow_features: List[str] = None
    
    # 统计特征配置
    statistical_features: List[str] = None
    
    # 时间窗口配置
    time_window_size: float = 10.0  # 秒
    overlap_ratio: float = 0.5
    
    # 编码器配置
    encoder_type: str = "transformer"  # transformer, lstm, gru, tcn
    
    # Transformer配置
    transformer_config: Dict[str, Any] = None
    
    # LSTM/GRU配置
    rnn_config: Dict[str, Any] = None
    
    # TCN配置
    tcn_config: Dict[str, Any] = None
    
    # 输出配置
    output_feature_dim: int = 512
    use_attention: bool = True
    dropout_rate: float = 0.1
    
    def __post_init__(self):
        super().__post_init__()
        
        # 设置默认特征列表
        if self.packet_features is None:
            self.packet_features = [
                "packet_size",
                "protocol_type", 
                "tcp_flags",
                "inter_arrival_time",
                "payload_size",
                "header_length"
            ]
        
        if self.flow_features is None:
            self.flow_features = [
                "flow_duration",
                "total_packets",
                "total_bytes",
                "avg_packet_size",
                "packet_rate",
                "byte_rate"
            ]
        
        if self.statistical_features is None:
            self.statistical_features = [
                "packet_size_mean",
                "packet_size_std",
                "packet_size_min",
                "packet_size_max",
                "iat_mean",
                "iat_std",
                "iat_min", 
                "iat_max"
            ]
        
        # 设置默认编码器配置
        if self.transformer_config is None:
            self.transformer_config = {
                "num_layers": 6,
                "num_heads": 8,
                "hidden_dim": 512,
                "feedforward_dim": 2048,
                "dropout": 0.1,
                "activation": "relu"
            }
        
        if self.rnn_config is None:
            self.rnn_config = {
                "hidden_size": 256,
                "num_layers": 2,
                "bidirectional": True,
                "dropout": 0.1
            }
        
        if self.tcn_config is None:
            self.tcn_config = {
                "num_channels": [64, 128, 256],
                "kernel_size": 3,
                "dropout": 0.1,
                "activation": "relu"
            }
    
    def get_total_feature_dim(self) -> int:
        """计算总特征维度"""
        total_dim = 0
        
        if self.extract_packet_features:
            total_dim += len(self.packet_features)
        
        if self.extract_flow_features:
            total_dim += len(self.flow_features)
        
        if self.extract_statistical_features:
            total_dim += len(self.statistical_features)
        
        return total_dim
    
    def validate(self) -> bool:
        """验证序列配置参数"""
        super().validate()
        
        # 验证序列长度
        if self.max_sequence_length <= 0:
            raise ValueError("最大序列长度必须为正整数")
        
        # 验证特征维度
        if self.feature_dim <= 0:
            raise ValueError("特征维度必须为正整数")
        
        # 验证截断策略
        if self.truncate_strategy not in ["head", "tail", "random"]:
            raise ValueError("截断策略必须为 'head', 'tail' 或 'random'")
        
        # 验证编码器类型
        supported_encoders = ["transformer", "lstm", "gru", "tcn"]
        if self.encoder_type not in supported_encoders:
            raise ValueError(f"不支持的编码器类型: {self.encoder_type}")
        
        # 验证时间窗口
        if self.time_window_size <= 0:
            raise ValueError("时间窗口大小必须为正数")
        
        if not 0 <= self.overlap_ratio < 1:
            raise ValueError("重叠比例必须在[0, 1)范围内")
        
        # 验证dropout率
        if not 0 <= self.dropout_rate <= 1:
            raise ValueError("Dropout率必须在[0, 1]范围内")
        
        return True
