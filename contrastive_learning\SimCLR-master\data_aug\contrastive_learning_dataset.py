from torchvision.transforms import transforms
from data_aug.gaussian_blur import G<PERSON><PERSON><PERSON>lur
from torchvision import transforms, datasets
from data_aug.view_generator import ContrastiveLearningViewGenerator
from exceptions.exceptions import InvalidDatasetSelection
from torch.utils.data import Dataset
import numpy as np
import os
import pickle
import torch
class SequenceDataset(Dataset):
    def __init__(self, root_folder, transform=None, sequence_length=1024):
        self.root_folder = root_folder
        self.transform = transform
        self.sequence_length = sequence_length
        self.seq_path = []
        self.labels = []
        self.class_idx = {name : i for i, name in enumerate(sorted(os.listdir(root_folder)))}
        # 遍历文件夹中的所有PKL文件
        for class_name in os.listdir(root_folder):
            class_dir = os.path.join(root_folder, class_name)
            if os.path.isdir(class_dir):
                # 递归遍历class_dir下所有文件
                for root, _, files in os.walk(class_dir):
                    for filename in files:
                        if filename.endswith('.pkl'):
                            self.seq_path.append(os.path.join(root, filename))
                            self.labels.append(self.class_idx[class_name])

    def __len__(self):
        return len(self.seq_path)

    def __getitem__(self, idx):
        file_path = self.seq_path[idx]

        # 读取PKL文件
        with open(file_path, 'rb') as f:
            data = pickle.load(f)

        # 处理字节流数据
        if isinstance(data, bytes):
            # 将字节转换为数值数组
            byte_array = np.frombuffer(data, dtype=np.uint8)
        elif isinstance(data, np.ndarray) and data.dtype == np.uint8:
            # 如果已经是字节数组
            byte_array = data
        else:
            # 尝试转换为字节数组
            byte_array = np.array(data, dtype=np.uint8)
        # 确保序列长度一致
        if len(byte_array) != self.sequence_length:
            raise ValueError(f"Sequence length mismatch: Expected {self.sequence_length}, got {len(byte_array)}")
        # 转换为tensor并归一化到[0,1]范围
        sequence = torch.from_numpy(byte_array).float() / 255.0
        # 应用变换
        if self.transform:
            sequence = self.transform(sequence)
        label = self.labels[idx]
        return sequence, label


class SequenceTransform:
    """针对字节流序列数据的增强变换类"""

    def __init__(self, noise_level=0.01, dropout_prob=0.1, scale_range=(0.9, 1.1)):
        self.noise_level = noise_level
        self.dropout_prob = dropout_prob
        self.scale_range = scale_range

    def add_noise(self, sequence):
        """添加均匀噪声"""
        noise = torch.rand_like(sequence) * self.noise_level * 2 - self.noise_level
        noisy_sequence = sequence + noise
        return torch.clamp(noisy_sequence, 0, 1)  # 保持在[0,1]范围内

    def random_dropout(self, sequence):
        """随机将一些字节设为0"""
        if self.dropout_prob > 0:
            mask = torch.rand_like(sequence) > self.dropout_prob
            return sequence * mask.float()
        return sequence

    def random_scale(self, sequence):
        """随机缩放"""
        scale = torch.FloatTensor(1).uniform_(self.scale_range[0], self.scale_range[1])
        scaled_sequence = sequence * scale
        return torch.clamp(scaled_sequence, 0, 1)  # 保持在[0,1]范围内

    def random_offset(self, sequence):
        """随机偏移"""
        offset = torch.FloatTensor(1).uniform_(-0.1, 0.1)
        offset_sequence = sequence + offset
        return torch.clamp(offset_sequence, 0, 1)  # 保持在[0,1]范围内

    def random_subsequence(self, sequence):
        """随机提取子序列并填充到原长度"""
        length = sequence.size(0)
        if length > 256:  # 只有当序列足够长时才进行子序列采样
            sub_len = max(length // 2, 10)
            start_idx = torch.randint(0, length - sub_len + 1, (1,)).item()
            subsequence = sequence[start_idx:start_idx + sub_len]

            # 创建新序列并随机放置子序列
            new_sequence = torch.zeros_like(sequence)
            place_idx = torch.randint(0, length - sub_len + 1, (1,)).item()
            new_sequence[place_idx:place_idx + sub_len] = subsequence
            return new_sequence
        return sequence

    def __call__(self, sequence):
        """应用随机增强变换"""
        # 随机应用各种增强
        transforms = [
            (self.add_noise, 0.5),
            (self.random_dropout, 0.3),
            (self.random_scale, 0.3),
            (self.random_offset, 0.3),
            (self.random_subsequence, 0.2)
        ]

        for transform_func, prob in transforms:
            if torch.rand(1) < prob:
                sequence = transform_func(sequence)

        return sequence

class ContrastiveLearningDataset:
    def __init__(self, root_folder):
        self.root_folder = root_folder

    @staticmethod
    def get_simclr_pipeline_transform(size, s=1):
        """Return a set of data augmentation transformations as described in the SimCLR paper."""
        color_jitter = transforms.ColorJitter(0.8 * s, 0.8 * s, 0.8 * s, 0.2 * s)
        data_transforms = transforms.Compose([#transforms.RandomResizedCrop(size=size),
                                              transforms.RandomHorizontalFlip(),
                                              transforms.RandomApply([color_jitter], p=0.8),
                                              transforms.RandomGrayscale(p=0.2),
                                              GaussianBlur(kernel_size=int(0.1 * size)),
                                              transforms.ToTensor()])
        return data_transforms


    def get_dataset(self, name, n_views):
        valid_datasets = {'cifar10': lambda: datasets.CIFAR10(self.root_folder, train=True,
                                                              transform=ContrastiveLearningViewGenerator(
                                                                  self.get_simclr_pipeline_transform(32),
                                                                  n_views),
                                                              download=True),

                          'stl10': lambda: datasets.STL10(self.root_folder, split='unlabeled',
                                                          transform=ContrastiveLearningViewGenerator(
                                                              self.get_simclr_pipeline_transform(96),
                                                              n_views),
                                                          download=True),

                          'custom': lambda: datasets.ImageFolder(root=self.root_folder,
                                                                    transform=ContrastiveLearningViewGenerator(
                                                                    self.get_simclr_pipeline_transform(32), n_views)),

                          'sequence': lambda: SequenceDataset(self.root_folder,
                                                              transform=ContrastiveLearningViewGenerator(
                                                                  SequenceTransform(),
                                                                  n_views),
                                                              sequence_length=1024)
    }

        try:
            dataset_fn = valid_datasets[name]
        except KeyError:
            raise InvalidDatasetSelection()
        else:
            return dataset_fn()