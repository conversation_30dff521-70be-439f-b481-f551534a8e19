import os
import numpy as np
from scipy import linalg
import torch
import torchvision.transforms as TF
from torch.utils.data import Dataset, DataLoader
from PIL import Image
import torch.nn as nn
from torchvision import models
# 配置参数
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
batch_size = 32
image_size = 32  # Inception v3输入尺寸


class ResNetSimCLR(nn.Module):
    def __init__(self, base_model, out_dim=128):
        super(ResNetSimCLR, self).__init__()
        self.resnet_dict = {
            "resnet18": models.resnet18(pretrained=False, num_classes=out_dim),
            "resnet50": models.resnet50(pretrained=False, num_classes=out_dim)
        }
        self.backbone = self._get_basemodel(base_model)
    def _get_basemodel(self, model_name):
        model = self.resnet_dict.get(model_name)
        if model is None:
            raise ValueError(f"Invalid backbone: {model_name}. Choose from {list(self.resnet_dict.keys())}")
        return model
    def freeze_backbone(self):
        for param in self.backbone.parameters():
            param.requires_grad = False
        for param in self.classifier.parameters():
            param.requires_grad = True
        print("Backbone frozen")

    def unfreeze_backbone(self):
        for param in self.backbone.parameters():
            param.requires_grad = True
        for param in self.classifier.parameters():
            param.requires_grad = True
        print("Backbone unfrozen")

    def forward(self, x):
        x = self.backbone(x)
        return x


def load_pretrained_weights(model, pretrained_path):
    if pretrained_path is None:
        print("No pretrained weights found, training from scratch.")
        return model
    checkpoint = torch.load(pretrained_path, map_location='cpu')

    # 处理不同格式的checkpoint
    if 'model_state_dict' in checkpoint:
        state_dict = checkpoint['model_state_dict']
    elif 'state_dict' in checkpoint:
        state_dict = checkpoint['state_dict']
    elif 'model' in checkpoint:
        state_dict = checkpoint['model']
    else:
        state_dict = checkpoint

    # 移除可能存在的module.前缀 (如果是DataParallel保存的)
    state_dict = {k.replace('encoder.', ''): v for k, v in state_dict.items()}
    state_dict = {k.replace('module.', ''): v for k, v in state_dict.items()}
    # 不加载分类头的权重
    filtered_state_dict = {k: v for k, v in state_dict.items() if not k.startswith('classifier')}
    # 严格加载匹配的参数
    msg = model.load_state_dict(filtered_state_dict, strict=False)
    print(f'Missing keys: {msg.missing_keys}')
    print(f'Unexpected keys: {msg.unexpected_keys}')
    return model
# 准备图像转换
transform = TF.Compose([
    TF.Resize(image_size),
    TF.ToTensor(),
])

# 自定义图像数据集类
class ImageDataset(Dataset):
    def __init__(self, img_dir):
        self.img_dir = img_dir
        self.img_paths = [os.path.join(img_dir, f) for f in os.listdir(img_dir)
                          if os.path.isfile(os.path.join(img_dir, f)) and f.lower().endswith(('png', 'jpg', 'jpeg'))]
    def __len__(self):
        return len(self.img_paths)
    def __getitem__(self, idx):
        img_path = self.img_paths[idx]
        img = Image.open(img_path).convert('RGB')
        return transform(img)

# 加载预训练Inception v3模型
model = ResNetSimCLR("resnet18")
model = load_pretrained_weights(model, pretrained_path='./FID_pretrained.pth.tar')
model.eval()
model.to(device)

# 禁用梯度计算
@torch.no_grad()
def get_activations(data_loader, model):
    """计算所有图像的特征向量"""
    activations = []
    for batch in data_loader:
        batch = batch.to(device)

        # Inception v3的辅助输出
        pred = model(batch)
        if isinstance(pred, tuple):
            pred = pred[0]  # 使用主要输出

        # 展平并存储
        activations.append(pred.cpu())

    return torch.cat(activations, dim=0)


# 计算特征统计量
def calculate_statistics(activations):
    """计算特征矩阵的均值和协方差"""
    mu = activations.mean(dim=0)
    sigma = activations.T.cov()
    return mu.numpy(), sigma.numpy()


# 计算FID
def calculate_fid(mu1, sigma1, mu2, sigma2):
    """计算两个分布间的Fréchet距离"""
    # 差异平方和
    diff = mu1 - mu2
    cov_mean = linalg.sqrtm(sigma1.dot(sigma2))

    # 检查虚部是否小到可以忽略
    if np.iscomplexobj(cov_mean):
        cov_mean = cov_mean.real

    # 计算FID分数
    fid = diff.dot(diff) + np.trace(sigma1 + sigma2 - 2 * cov_mean)
    return fid


# 主函数
def main():
    # 设置目录路径
    real_dir = './Htbot'  # 替换为真实图像目录
    gen_dir = './gen_img'  # 替换为生成图像目录

    print('加载数据集...')
    real_dataset = ImageDataset(real_dir)
    gen_dataset = ImageDataset(gen_dir)

    # 确保数据集不为空
    assert len(real_dataset) > 0, "真实图像目录为空或格式错误"
    assert len(gen_dataset) > 0, "生成图像目录为空或格式错误"

    print(f"真实图像数量: {len(real_dataset)}")
    print(f"生成图像数量: {len(gen_dataset)}")

    # 创建数据加载器
    real_loader = DataLoader(real_dataset, batch_size=batch_size, shuffle=False, num_workers=4)
    gen_loader = DataLoader(gen_dataset, batch_size=batch_size, shuffle=False, num_workers=4)

    print('提取真实图像特征...')
    real_activations = get_activations(real_loader, model)
    mu_real, sigma_real = calculate_statistics(real_activations)

    print('提取生成图像特征...')
    gen_activations = get_activations(gen_loader, model)
    mu_gen, sigma_gen = calculate_statistics(gen_activations)

    print('计算FID...')
    fid_value = calculate_fid(mu_real, sigma_real, mu_gen, sigma_gen)

    print('\n' + '=' * 50)
    print(f"FID分数: {fid_value:.2f}")
    print('=' * 50)

    # 解释结果
    print("\nFID解释(数值越小表示质量越好):")
    print("- FID < 10: 非常高质量的生成结果")
    print("- 10 < FID < 30: 良好质量")
    print("- 30 < FID < 50: 中等质量")
    print("- FID > 50: 可察觉的质量问题")
    print("- FID > 100: 明显的质量问题")


if __name__ == '__main__':
    main()