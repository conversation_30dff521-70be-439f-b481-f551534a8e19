# -*- coding: utf-8 -*-
"""
损失函数模块
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Tuple


class ContrastiveLoss(nn.Module):
    """对比学习损失函数"""
    
    def __init__(self, temperature: float = 0.07, use_hard_negatives: bool = True):
        """
        初始化对比损失
        
        Args:
            temperature: 温度参数
            use_hard_negatives: 是否使用困难负样本
        """
        super(ContrastiveLoss, self).__init__()
        self.temperature = temperature
        self.use_hard_negatives = use_hard_negatives
    
    def forward(self, 
                image_features: torch.Tensor,
                sequence_features: torch.Tensor,
                labels: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        计算对比损失
        
        Args:
            image_features: 图像特征 (batch_size, feature_dim)
            sequence_features: 序列特征 (batch_size, feature_dim)
            labels: 标签 (batch_size,)
            
        Returns:
            对比损失
        """
        batch_size = image_features.size(0)
        device = image_features.device
        
        # 归一化特征
        image_features = F.normalize(image_features, p=2, dim=1)
        sequence_features = F.normalize(sequence_features, p=2, dim=1)
        
        # 计算跨模态相似度矩阵
        similarity_matrix = torch.matmul(image_features, sequence_features.T) / self.temperature
        
        # 创建正样本掩码（对角线为正样本）
        positive_mask = torch.eye(batch_size, device=device, dtype=torch.bool)
        
        # 如果提供了标签，可以使用标签信息创建更复杂的正负样本对
        if labels is not None and self.use_hard_negatives:
            # 同类样本也可以作为正样本
            label_matrix = labels.unsqueeze(0) == labels.unsqueeze(1)
            positive_mask = positive_mask | label_matrix
        
        # 计算InfoNCE损失
        # 图像到序列
        logits_img_to_seq = similarity_matrix
        targets_img_to_seq = torch.arange(batch_size, device=device)
        loss_img_to_seq = F.cross_entropy(logits_img_to_seq, targets_img_to_seq)
        
        # 序列到图像
        logits_seq_to_img = similarity_matrix.T
        targets_seq_to_img = torch.arange(batch_size, device=device)
        loss_seq_to_img = F.cross_entropy(logits_seq_to_img, targets_seq_to_img)
        
        # 总损失
        contrastive_loss = (loss_img_to_seq + loss_seq_to_img) / 2
        
        return contrastive_loss
    
    def compute_accuracy(self,
                        image_features: torch.Tensor,
                        sequence_features: torch.Tensor) -> float:
        """
        计算对比学习准确率
        
        Args:
            image_features: 图像特征
            sequence_features: 序列特征
            
        Returns:
            准确率
        """
        batch_size = image_features.size(0)
        
        # 归一化特征
        image_features = F.normalize(image_features, p=2, dim=1)
        sequence_features = F.normalize(sequence_features, p=2, dim=1)
        
        # 计算相似度矩阵
        similarity_matrix = torch.matmul(image_features, sequence_features.T) / self.temperature
        
        # 预测（最相似的序列特征）
        predictions = torch.argmax(similarity_matrix, dim=1)
        targets = torch.arange(batch_size, device=image_features.device)
        
        # 计算准确率
        accuracy = (predictions == targets).float().mean().item()
        
        return accuracy


class AlignmentLoss(nn.Module):
    """模态对齐损失函数"""
    
    def __init__(self, loss_type: str = 'mse'):
        """
        初始化对齐损失
        
        Args:
            loss_type: 损失类型 ('mse', 'cosine', 'kl')
        """
        super(AlignmentLoss, self).__init__()
        self.loss_type = loss_type
    
    def forward(self,
                image_features: torch.Tensor,
                sequence_features: torch.Tensor) -> torch.Tensor:
        """
        计算对齐损失
        
        Args:
            image_features: 图像特征
            sequence_features: 序列特征
            
        Returns:
            对齐损失
        """
        if self.loss_type == 'mse':
            # MSE损失
            loss = F.mse_loss(image_features, sequence_features)
            
        elif self.loss_type == 'cosine':
            # 余弦相似度损失
            cosine_sim = F.cosine_similarity(image_features, sequence_features, dim=1)
            loss = 1 - cosine_sim.mean()
            
        elif self.loss_type == 'kl':
            # KL散度损失
            image_prob = F.softmax(image_features, dim=1)
            sequence_prob = F.softmax(sequence_features, dim=1)
            loss = F.kl_div(image_prob.log(), sequence_prob, reduction='batchmean')
            
        else:
            raise ValueError(f"不支持的损失类型: {self.loss_type}")
        
        return loss


class FocalLoss(nn.Module):
    """Focal损失函数，用于处理类别不平衡"""
    
    def __init__(self, alpha: float = 1.0, gamma: float = 2.0, reduction: str = 'mean'):
        """
        初始化Focal损失
        
        Args:
            alpha: 平衡因子
            gamma: 聚焦参数
            reduction: 减少方式
        """
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
    
    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        计算Focal损失
        
        Args:
            inputs: 预测logits
            targets: 真实标签
            
        Returns:
            Focal损失
        """
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss


class TripletLoss(nn.Module):
    """三元组损失函数"""
    
    def __init__(self, margin: float = 1.0, p: int = 2):
        """
        初始化三元组损失
        
        Args:
            margin: 边界值
            p: 距离的p范数
        """
        super(TripletLoss, self).__init__()
        self.margin = margin
        self.p = p
    
    def forward(self,
                anchor: torch.Tensor,
                positive: torch.Tensor,
                negative: torch.Tensor) -> torch.Tensor:
        """
        计算三元组损失
        
        Args:
            anchor: 锚点特征
            positive: 正样本特征
            negative: 负样本特征
            
        Returns:
            三元组损失
        """
        # 计算距离
        pos_dist = F.pairwise_distance(anchor, positive, p=self.p)
        neg_dist = F.pairwise_distance(anchor, negative, p=self.p)
        
        # 计算损失
        loss = F.relu(pos_dist - neg_dist + self.margin)
        
        return loss.mean()


class SupConLoss(nn.Module):
    """监督对比学习损失函数"""
    
    def __init__(self, temperature: float = 0.07, contrast_mode: str = 'all'):
        """
        初始化监督对比损失
        
        Args:
            temperature: 温度参数
            contrast_mode: 对比模式 ('all', 'one')
        """
        super(SupConLoss, self).__init__()
        self.temperature = temperature
        self.contrast_mode = contrast_mode
    
    def forward(self, features: torch.Tensor, labels: torch.Tensor) -> torch.Tensor:
        """
        计算监督对比损失
        
        Args:
            features: 特征张量 (batch_size, feature_dim)
            labels: 标签张量 (batch_size,)
            
        Returns:
            监督对比损失
        """
        device = features.device
        batch_size = features.shape[0]
        
        # 归一化特征
        features = F.normalize(features, p=2, dim=1)
        
        # 计算相似度矩阵
        similarity_matrix = torch.matmul(features, features.T) / self.temperature
        
        # 创建标签掩码
        labels = labels.contiguous().view(-1, 1)
        mask = torch.eq(labels, labels.T).float().to(device)
        
        # 移除对角线（自己与自己的相似度）
        logits_mask = torch.scatter(
            torch.ones_like(mask),
            1,
            torch.arange(batch_size).view(-1, 1).to(device),
            0
        )
        mask = mask * logits_mask
        
        # 计算exp和log-sum-exp
        exp_logits = torch.exp(similarity_matrix) * logits_mask
        log_prob = similarity_matrix - torch.log(exp_logits.sum(1, keepdim=True))
        
        # 计算平均log-likelihood
        mean_log_prob_pos = (mask * log_prob).sum(1) / mask.sum(1)
        
        # 损失
        loss = -mean_log_prob_pos.mean()
        
        return loss
