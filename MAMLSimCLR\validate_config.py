"""
配置验证脚本
用于验证YAML配置文件的正确性
"""

import os
import sys
import argparse
from config import Config

def validate_config(config_path):
    """
    验证配置文件
    """
    print(f"Validating configuration file: {config_path}")
    print("="*60)
    
    try:
        # 加载配置
        config = Config(config_path=config_path)
        print("✓ Configuration loaded successfully")
        
        # 验证必要的配置项
        errors = []
        warnings = []
        
        # 检查数据路径
        if not os.path.exists(config.data.data_root):
            errors.append(f"Data root directory does not exist: {config.data.data_root}")
        else:
            print(f"✓ Data root directory exists: {config.data.data_root}")
        
        # 检查预训练模型路径
        if config.simclr.pretrained_path is None:
            warnings.append("Pretrained model path is not specified")
        elif not os.path.exists(config.simclr.pretrained_path):
            warnings.append(f"Pretrained model file does not exist: {config.simclr.pretrained_path}")
        else:
            print(f"✓ Pretrained model exists: {config.simclr.pretrained_path}")
        
        # 检查参数合理性
        if config.maml.k_shot <= 0:
            errors.append(f"k_shot must be positive, got: {config.maml.k_shot}")
        
        if config.maml.k_query <= 0:
            errors.append(f"k_query must be positive, got: {config.maml.k_query}")
        
        if config.maml.n_way <= 1:
            errors.append(f"n_way must be greater than 1, got: {config.maml.n_way}")
        
        if config.training.epochs <= 0:
            errors.append(f"epochs must be positive, got: {config.training.epochs}")
        
        if config.maml.update_lr <= 0:
            errors.append(f"update_lr must be positive, got: {config.maml.update_lr}")
        
        if config.maml.meta_lr <= 0:
            errors.append(f"meta_lr must be positive, got: {config.maml.meta_lr}")
        
        # 检查设备
        if config.training.device not in ['cpu', 'cuda']:
            warnings.append(f"Unusual device specified: {config.training.device}")
        
        # 检查骨干网络
        if config.simclr.backbone not in ['resnet18', 'resnet50']:
            errors.append(f"Unsupported backbone: {config.simclr.backbone}")
        
        # 打印验证结果
        print("\nValidation Results:")
        print("-" * 40)
        
        if errors:
            print("❌ ERRORS:")
            for error in errors:
                print(f"   - {error}")
        
        if warnings:
            print("⚠️  WARNINGS:")
            for warning in warnings:
                print(f"   - {warning}")
        
        if not errors and not warnings:
            print("✅ All checks passed!")
        elif not errors:
            print("✅ Configuration is valid (with warnings)")
        else:
            print("❌ Configuration has errors")
        
        # 打印配置摘要
        print("\nConfiguration Summary:")
        print("-" * 40)
        print(f"Experiment: {config.logging.experiment_name or 'auto-generated'}")
        print(f"Data root: {config.data.data_root}")
        print(f"Backbone: {config.simclr.backbone}")
        print(f"Few-shot setting: {config.maml.n_way}-way {config.maml.k_shot}-shot")
        print(f"Training epochs: {config.training.epochs}")
        print(f"Device: {config.training.device}")
        print(f"Meta learning rate: {config.maml.meta_lr}")
        print(f"Update learning rate: {config.maml.update_lr}")
        
        return len(errors) == 0
        
    except Exception as e:
        print(f"❌ Error loading configuration: {e}")
        return False


def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='Validate YAML configuration file')
    parser.add_argument('config_path', type=str, help='Path to YAML configuration file')
    parser.add_argument('--verbose', action='store_true', help='Verbose output')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.config_path):
        print(f"❌ Configuration file not found: {args.config_path}")
        return 1
    
    # 验证配置
    is_valid = validate_config(args.config_path)
    
    if args.verbose:
        # 打印完整配置
        print("\nFull Configuration:")
        print("="*60)
        config = Config(config_path=args.config_path)
        config.print_config()
    
    return 0 if is_valid else 1


if __name__ == '__main__':
    sys.exit(main())
