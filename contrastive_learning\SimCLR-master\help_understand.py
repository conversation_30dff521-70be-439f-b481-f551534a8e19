import logging
import os
import sys

import torch
import torch.nn.functional as F
import torchvision.models as models
from torch import nn


class GaussianBlur(object):
    """blur a single image on CPU"""
    def __init__(self, kernel_size):
        radias = kernel_size // 2
        kernel_size = radias * 2 + 1
        self.blur_h = nn.Conv2d(3, 3, kernel_size=(kernel_size, 1),
                                stride=1, padding=0, bias=False, groups=3)
        self.blur_v = nn.Conv2d(3, 3, kernel_size=(1, kernel_size),
                                stride=1, padding=0, bias=False, groups=3)
        self.k = kernel_size
        self.r = radias

        self.blur = nn.Sequential(
            nn.ReflectionPad2d(radias),
            self.blur_h,
            self.blur_v
        )

        self.pil_to_tensor = transforms.ToTensor()
        self.tensor_to_pil = transforms.ToPILImage()

    def __call__(self, img):
        img = self.pil_to_tensor(img).unsqueeze(0)

        sigma = np.random.uniform(0.1, 2.0)
        x = np.arange(-self.r, self.r + 1)
        x = np.exp(-np.power(x, 2) / (2 * sigma * sigma))
        x = x / x.sum()
        x = torch.from_numpy(x).view(1, -1).repeat(3, 1)

        self.blur_h.weight.data.copy_(x.view(3, 1, self.k, 1))
        self.blur_v.weight.data.copy_(x.view(3, 1, 1, self.k))

        with torch.no_grad():
            img = self.blur(img)
            img = img.squeeze()

        img = self.tensor_to_pil(img)

        return img



class ResNetSimCLR(nn.Module):

    def __init__(self, base_model, out_dim):
        super(ResNetSimCLR, self).__init__()
        self.resnet_dict = {"resnet18": models.resnet18(pretrained=False, num_classes=out_dim),
                            "resnet50": models.resnet50(pretrained=False, num_classes=out_dim)}

        self.backbone = self._get_basemodel(base_model)
        dim_mlp = self.backbone.fc.in_features

        # add mlp projection head
        self.backbone.fc = nn.Sequential(nn.Linear(dim_mlp, dim_mlp), nn.ReLU(), self.backbone.fc)
    def _get_basemodel(self, model_name):
        try:
            model = self.resnet_dict[model_name]
        except KeyError:
            raise ValueError
        else:
            return model

    def forward(self, x):
        return self.backbone(x)


def info_nce_loss(features,batch_size, n_views):

    labels = torch.cat([torch.arange(batch_size) for i in range(n_views)], dim=0)
    print("InfoNCE labels shape:", labels)
    labels = (labels.unsqueeze(0) == labels.unsqueeze(1)).float()
    print("matrix Labels",labels)
    print("InfoNCE labels after unsqueeze shape:", labels.shape)
    labels = labels.to('cpu')

    features = F.normalize(features, dim=1)
    print("Ori_features",features.shape)
    similarity_matrix = torch.matmul(features, features.T)
    print("Similarity matrix shape:", similarity_matrix)
    # assert similarity_matrix.shape == (
    #     self.args.n_views * self.args.batch_size, self.args.n_views * self.args.batch_size)
    # assert similarity_matrix.shape == labels.shape

    # discard the main diagonal from both: labels and similarities matrix
    mask = torch.eye(labels.shape[0], dtype=torch.bool).to('cpu')
    print( "mask",mask)
    labels = labels[~mask].view(labels.shape[0], -1)
    print("label",labels)
    similarity_matrix = similarity_matrix[~mask].view(similarity_matrix.shape[0], -1)
    # assert similarity_matrix.shape == labels.shape
    print("similarity_matrix",similarity_matrix)
    # select and combine multiple positives
    positives = similarity_matrix[labels.bool()].view(labels.shape[0], -1)

    # select only the negatives the negatives
    negatives = similarity_matrix[~labels.bool()].view(similarity_matrix.shape[0], -1)

    logits = torch.cat([positives, negatives], dim=1)
    labels = torch.zeros(logits.shape[0], dtype=torch.long).to('cpu')

    logits = logits / 0.1
    return logits, labels

import torchvision.transforms as transforms
def get_simclr_pipeline_transform(size, s=1):
    """Return a set of data augmentation transformations as described in the SimCLR paper."""
    color_jitter = transforms.ColorJitter(0.8 * s, 0.8 * s, 0.8 * s, 0.2 * s)
    data_transforms = transforms.Compose([transforms.RandomResizedCrop(size=size),
                                          transforms.RandomHorizontalFlip(),
                                          transforms.RandomApply([color_jitter], p=0.8),
                                          transforms.RandomGrayscale(p=0.2),
                                          GaussianBlur(kernel_size=int(0.1 * size)),
                                          transforms.ToTensor()])
    return data_transforms


class ContrastiveLearningViewGenerator(object):
    """Take two random crops of one image as the query and key."""

    def __init__(self, base_transform, n_views=2):
        self.base_transform = base_transform
        self.n_views = n_views

    def __call__(self, x):
        return [self.base_transform(x) for i in range(self.n_views)]


if __name__ == "__main__":
    # Example usage
    import numpy as np
    from PIL import Image
    arr = np.random.randint(0, 256, (32, 32, 3), dtype=np.uint8)
    ori = Image.fromarray(arr)
    n_views = 2
    transformer = ContrastiveLearningViewGenerator(get_simclr_pipeline_transform(32),2)
    trans_feature = transformer(ori)
    batch_size = 4

    data = [torch.randn(batch_size, 3, 8, 8),torch.randn(batch_size, 3, 8, 8)]
    #print("Transformed feature shape:", trans_feature)
    features = torch.cat(data, dim=0)  # Concatenate the two views
    model = ResNetSimCLR(base_model='resnet18', out_dim=128)

    features = model(features)  # Forward pass through the model
    print("Features shape:", features.shape)
    logits, labels = info_nce_loss(features, batch_size, n_views)
    print("Logits shape:", logits.shape)
    print("Labels shape:", labels.shape)
