"""
配置文件解析器
"""

import yaml
import os
from typing import Dict, Any


class ConfigParser:
    """
    配置文件解析器类
    """
    def __init__(self, config_path: str):
        """
        初始化配置解析器
        :param config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        :return: 配置字典
        """
        if not os.path.exists(self.config_path):
            raise FileNotFoundError(f"Config file not found: {self.config_path}")

        with open(self.config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

        return config

    def get(self, key: str, default=None):
        """
        获取配置值，支持嵌套键（如 'data.train_dataset'）
        :param key: 配置键
        :param default: 默认值
        :return: 配置值
        """
        keys = key.split('.')
        value = self.config

        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default

    def get_cuda_visible_devices(self) -> str:
        """
        获取CUDA_VISIBLE_DEVICES配置
        :return: CUDA_VISIBLE_DEVICES字符串
        """
        return self.config.get('CUDA_VISIBLE_DEVICES', '')
    def get_data_config(self) -> Dict[str, Any]:
        """获取数据相关配置"""
        return self.config.get('data', {})

    def get_simclr_config(self) -> Dict[str, Any]:
        """获取SimCLR相关配置"""
        return self.config.get('simclr', {})

    def get_protonet_config(self) -> Dict[str, Any]:
        """获取ProtoNet相关配置"""
        return self.config.get('protonet', {})

    def get_training_config(self) -> Dict[str, Any]:
        """获取训练相关配置"""
        return self.config.get('training', {})

    def get_optimizer_config(self) -> Dict[str, Any]:
        """获取优化器相关配置"""
        return self.config.get('optimizer', {})

    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志相关配置"""
        return self.config.get('logging', {})

    def get_evaluation_config(self) -> Dict[str, Any]:
        """获取评估相关配置"""
        return self.config.get('evaluation', {})
    def get_ProtoSCL_config(self) -> Dict[str, Any]:
        """获取ProtoSCL相关配置"""
        return self.config.get('ProtoSCL', {})
    def to_kwargs(self, section: str = None) -> Dict[str, Any]:
        """
        将配置转换为kwargs格式
        :param section: 指定配置节，如果为None则返回所有配置
        :return: kwargs字典
        """
        if section is None:
            return self.config
        return self.config.get(section, {})

    def update_config(self, updates: Dict[str, Any]):
        """
        更新配置
        :param updates: 更新的配置字典
        """
        def deep_update(base_dict, update_dict):
            for key, value in update_dict.items():
                if isinstance(value, dict) and key in base_dict and isinstance(base_dict[key], dict):
                    deep_update(base_dict[key], value)
                else:
                    base_dict[key] = value

        deep_update(self.config, updates)

    def save_config(self, save_path: str):
        """
        保存配置到文件
        :param save_path: 保存路径
        """
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        with open(save_path, 'w', encoding='utf-8') as f:
            yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)

    def print_config(self):
        """打印配置信息"""
        print("=" * 50)
        print("Configuration:")
        print("=" * 50)
        print(yaml.dump(self.config, default_flow_style=False, allow_unicode=True))
        print("=" * 50)


def create_transforms(**kwargs):
    """
    根据配置创建数据变换
    :param kwargs: 数据增强配置参数
    :return: 训练和验证的transform
    """
    import torchvision.transforms as transforms

    # 获取配置参数
    img_size = kwargs.get('img_size', 32)
    normalize_config = kwargs.get('normalize', {})
    mean = normalize_config.get('mean', [0.485, 0.456, 0.406])
    std = normalize_config.get('std', [0.229, 0.224, 0.225])

    # 训练时的数据增强
    train_transforms = [
        transforms.Resize((img_size, img_size)),
    ]

    # 添加数据增强
    if kwargs.get('horizontal_flip', False):
        train_transforms.append(transforms.RandomHorizontalFlip())

    if kwargs.get('vertical_flip', False):
        train_transforms.append(transforms.RandomVerticalFlip())

    rotation = kwargs.get('rotation', 0)
    if rotation > 0:
        train_transforms.append(transforms.RandomRotation(rotation))

    color_jitter_config = kwargs.get('color_jitter', {})
    if color_jitter_config:
        train_transforms.append(transforms.ColorJitter(
            brightness=color_jitter_config.get('brightness', 0),
            contrast=color_jitter_config.get('contrast', 0),
            saturation=color_jitter_config.get('saturation', 0),
            hue=color_jitter_config.get('hue', 0)
        ))

    # 添加基本变换
    train_transforms.extend([
        transforms.ToTensor(),
        transforms.Normalize(mean=mean, std=std)
    ])

    # 验证时的变换（不包含随机增强）
    val_transforms = [
        transforms.Resize((img_size, img_size)),
        transforms.ToTensor(),
        transforms.Normalize(mean=mean, std=std)
    ]

    return transforms.Compose(train_transforms), transforms.Compose(val_transforms)


def get_dataset_class(dataset_type: str):
    """
    根据配置获取数据集类
    :param dataset_type: 数据集类型
    :return: 数据集类
    """
    from dataset import DefaultDataset, FewShotDataset

    dataset_mapping = {
        'default': DefaultDataset,
        'DefaultDataset': DefaultDataset,
        'FewShot': FewShotDataset,
        'FewShotDataset': FewShotDataset,
    }
    if dataset_type not in dataset_mapping:
        raise ValueError(f"Unknown dataset type: {dataset_type}. Available: {list(dataset_mapping.keys())}")

    return dataset_mapping[dataset_type]
