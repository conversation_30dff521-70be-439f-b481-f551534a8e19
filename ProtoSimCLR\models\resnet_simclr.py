import torch.nn as nn
import torchvision.models as models
import torch

class ResNetSimCLR(nn.Module):

    def __init__(self, **kwargs):
        base_model = kwargs.get("base_model", "resnet18")
        out_dim = kwargs.get("out_dim", 128)
        self.pretrained_path = kwargs.get("pretrained_path", False)
        super(ResNetSimCLR, self).__init__()
        self.resnet_dict = {"resnet18": models.resnet18(pretrained=False, num_classes=out_dim),
                            "resnet50": models.resnet50(pretrained=False, num_classes=out_dim)}

        self.backbone = self._get_basemodel(base_model)
        dim_mlp = self.backbone.fc.in_features
        # add mlp projection head
        self.backbone.fc = nn.Sequential(nn.Linear(dim_mlp, dim_mlp), nn.ReLU(), self.backbone.fc)
        if self.pretrained_path:
            self.load_pretrained_weights(self.pretrained_path)
    def _get_basemodel(self, model_name):
        try:
            model = self.resnet_dict[model_name]
        except KeyError:
            raise ValueError(
                "Invalid backbone architecture. Check the config file and pass one of: resnet18 or resnet50")
        else:
            return model

    def load_pretrained_weights(self, pretrained_path):
        """
        加载预训练权重
        """
        try:
            checkpoint = torch.load(pretrained_path, map_location='cpu')
            # 处理不同的保存格式
            if 'state_dict' in checkpoint:
                state_dict = checkpoint['state_dict']
            elif 'model_state_dict' in checkpoint:
                state_dict = checkpoint['model_state_dict']
            else:
                state_dict = checkpoint

            # 去除module.前缀
            new_state_dict = {k.replace('backbone.', ''): v for k, v in state_dict.items()}
            new_state_dict = {k.replace('module.', ''): v for k, v in new_state_dict.items()}
            new_state_dict = {k.replace('encoder.', ''): v for k, v in new_state_dict.items()}

            # 移除不匹配的键
            model_dict = self.backbone.state_dict()
            filtered_dict = {}

            for k, v in new_state_dict.items():
                if k in model_dict and model_dict[k].shape == v.shape:
                    filtered_dict[k] = v
                else:
                    print(f"Skipping parameter {k} due to shape mismatch or absence")

            model_dict.update(filtered_dict)
            self.backbone.load_state_dict(model_dict)
            print(f"Successfully loaded pretrained weights from {pretrained_path}")

        except Exception as e:
            print(f"Error loading pretrained weights: {e}")
            print("Using random initialization instead")
    def forward(self, x):
        return self.backbone(x)
