# SimCLR + MAML 集成框架

## 概述

本目录包含了将SimCLR预训练模型与MAML元学习框架集成的代码实现。该框架实现了双阶段训练：

1. **阶段一**：使用SimCLR进行对比学习预训练，学习通用的特征表示
2. **阶段二**：使用MAML进行Few-shot学习，快速适应新的恶意流量检测任务

## 新特性 🆕

- **YAML配置支持**：完全支持YAML配置文件，便于管理和复现实验
- **多数据集支持**：支持ISAC和DEFAULT两种数据集格式
- **多种预设配置**：提供默认、快速测试、高性能、1-shot等多种配置
- **批量训练**：支持使用多个配置文件进行批量实验
- **配置验证**：提供配置文件验证工具
- **实验管理**：自动创建实验目录和保存配置
- **智能检测**：自动检测数据集类型和类别数量

## 文件结构

```
main/
├── README.md                    # 本说明文件
├── config.py                    # 配置系统（支持YAML）
├── configs/                     # 配置文件目录
│   ├── default.yaml            # 默认配置 (ISAC数据集)
│   ├── quick_test.yaml         # 快速测试配置 (ISAC数据集)
│   ├── high_performance.yaml   # 高性能配置 (ISAC数据集)
│   ├── multiclass.yaml         # 多分类配置 (ISAC数据集)
│   ├── few_shot_1shot.yaml     # 1-shot学习配置 (ISAC数据集)
│   └── default_dataset.yaml    # DEFAULT数据集配置
├── simclr_pretrained_model.py   # SimCLR预训练模型加载器
├── maml_with_simclr.py         # 集成MAML实现
├── network_traffic_dataset.py   # 网络流量数据集处理
├── train_integration.py        # 集成训练脚本
├── evaluate.py                 # 评估脚本
├── utils.py                    # 工具函数
├── validate_config.py          # 配置验证脚本
├── batch_train.py              # 批量训练脚本
└── example_usage.py            # 使用示例
```

## 支持的数据集格式

### 1. ISAC数据集格式 (`dataset_type: "ISAC"`)
```
ISAC_LWH_RGB/
├── normal/
│   ├── Train/          # 正常流量训练数据
│   └── Test/           # 正常流量测试数据
└── abnormal/
    ├── Train/          # 恶意流量训练数据
    │   ├── DDoS/
    │   ├── Botnet/
    │   └── ...
    └── Test/           # 恶意流量测试数据
        ├── DDoS/
        ├── Botnet/
        └── ...
```

### 2. DEFAULT数据集格式 (`dataset_type: "DEFAULT"`)
```
your_dataset/
├── train/              # 训练数据
│   ├── class1/
│   ├── class2/
│   └── ...
└── test/               # 测试数据
    ├── class1/
    ├── class2/
    └── ...
```

## 使用方法

### 1. 检查数据集
```bash
# 自动检测数据集类型和结构
python inspect_dataset.py --data_root /path/to/your/dataset

# 指定数据集类型
python inspect_dataset.py --data_root /path/to/your/dataset --dataset_type DEFAULT
```

### 2. 准备预训练模型
首先需要使用SimCLR训练一个预训练模型：
```bash
cd ../contrastive_learning/SimCLR-master
python run.py --config configs/ISAC_RGB_UnSupervised_Learning.yaml
```

### 3. 使用YAML配置文件训练

#### ISAC数据集训练
```bash
# 使用默认配置 (ISAC数据集)
python train_integration.py --config configs/default.yaml --pretrained_path /path/to/simclr/model.pth

# 使用快速测试配置（适合调试）
python train_integration.py --config configs/quick_test.yaml --pretrained_path /path/to/simclr/model.pth

# 使用高性能配置
python train_integration.py --config configs/high_performance.yaml --pretrained_path /path/to/simclr/model.pth

# 使用多分类配置
python train_integration.py --config configs/multiclass.yaml --pretrained_path /path/to/simclr/model.pth

# 使用1-shot学习配置
python train_integration.py --config configs/few_shot_1shot.yaml --pretrained_path /path/to/simclr/model.pth
```

#### DEFAULT数据集训练
```bash
# 使用DEFAULT数据集配置
python train_integration.py --config configs/default_dataset.yaml --pretrained_path /path/to/simclr/model.pth

# 或者修改配置文件中的data_root路径
```

#### 命令行参数覆盖
```bash
# 可以用命令行参数覆盖YAML配置中的任何参数
python train_integration.py --config configs/default.yaml \
    --pretrained_path /path/to/simclr/model.pth \
    --epochs 50 \
    --experiment_name my_custom_experiment
```

### 3. 配置验证
```bash
# 验证配置文件的正确性
python validate_config.py configs/default.yaml

# 详细输出
python validate_config.py configs/default.yaml --verbose
```

### 4. 批量训练
```bash
# 使用多个配置文件进行批量实验
python batch_train.py \
    --configs configs/default.yaml configs/quick_test.yaml configs/high_performance.yaml \
    --pretrained_path /path/to/simclr/model.pth \
    --gpu_ids 0 1

# 即使某个实验失败也继续运行其他实验
python batch_train.py \
    --configs configs/*.yaml \
    --pretrained_path /path/to/simclr/model.pth \
    --continue_on_error
```

### 5. 评估模型
```bash
python evaluate.py --model_path ./experiments/my_experiment/checkpoints/best_model.pth
```

## 配置文件说明

### 预设配置

1. **default.yaml** - 默认配置
   - 适合大多数场景的平衡配置
   - ResNet18骨干网络，5-shot学习
   - 100个训练epoch

2. **quick_test.yaml** - 快速测试配置
   - 用于调试和快速验证
   - 冻结骨干网络，减少训练时间
   - 20个训练epoch，较小的批次大小

3. **high_performance.yaml** - 高性能配置
   - 追求最佳性能的配置
   - ResNet50骨干网络，10-shot学习
   - 200个训练epoch，更强的数据增强

4. **few_shot_1shot.yaml** - 1-shot学习配置
   - 极少样本学习场景
   - 1-shot设置，增加任务数量
   - 适度的数据增强

### 自定义配置

您可以复制任何预设配置文件并修改参数来创建自定义配置：

```yaml
# 数据配置
data:
  data_root: "../data/ISAC_LWH_RGB"
  img_size: 84

# MAML配置
maml:
  n_way: 2
  k_shot: 5
  k_query: 15
  update_lr: 0.01
  meta_lr: 0.001

# 训练配置
training:
  epochs: 100
  batch_size: 32
  device: "cuda"
```

## 技术特点

- **预训练特征复用**：充分利用SimCLR学习的通用特征表示
- **快速适应**：通过MAML实现对新型攻击的快速检测
- **端到端训练**：支持联合优化两个阶段
- **YAML配置管理**：灵活的配置系统，支持参数覆盖
- **实验管理**：自动创建实验目录，保存配置和结果
- **批量实验**：支持多配置批量训练

## 实验设置

- **数据集**：ISAC网络流量RGB图像数据
- **Few-shot设置**：支持1-shot到多shot的灵活配置
- **评估指标**：准确率、精确率、召回率、F1分数
- **可视化**：训练曲线、混淆矩阵等

## 故障排除

### 常见问题

1. **配置文件错误**
   ```bash
   # 使用配置验证工具检查
   python validate_config.py configs/your_config.yaml
   ```

2. **预训练模型路径错误**
   - 确保在配置文件中正确指定`simclr.pretrained_path`
   - 或使用命令行参数`--pretrained_path`

3. **数据路径错误**
   - 检查`data.data_root`是否指向正确的数据目录
   - 确保目录下有`normal/`和`abnormal/`子目录

4. **GPU内存不足**
   - 减少`training.batch_size`
   - 减少`maml.task_num`
   - 使用`simclr.freeze_backbone: true`

### 性能优化建议

1. **快速调试**：使用`configs/quick_test.yaml`
2. **最佳性能**：使用`configs/high_performance.yaml`
3. **内存优化**：冻结骨干网络，减少批次大小
4. **多GPU训练**：使用`batch_train.py`的`--gpu_ids`参数
