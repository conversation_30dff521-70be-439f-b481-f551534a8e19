"""
工具函数模块
"""

import os
import torch
import random
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
import torchvision.transforms as transforms
from torch.utils.tensorboard import SummaryWriter
import datetime
import json
from torch.utils.tensorboard import SummaryWriter
import datetime
import json


def set_seed(seed=42):
    """
    设置随机种子以确保实验可重复性
    """
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    print(f"Random seed set to {seed}")


def count_parameters(model):
    """
    计算模型参数数量
    """
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

    print(f"Total parameters: {total_params:,}")
    print(f"Trainable parameters: {trainable_params:,}")

    return total_params, trainable_params


def visualize_samples(dataset, num_samples=8, save_path=None):
    """
    可视化数据集样本
    """
    fig, axes = plt.subplots(2, 4, figsize=(12, 6))
    axes = axes.flatten()

    # 随机选择样本
    indices = random.sample(range(len(dataset)), num_samples)

    for i, idx in enumerate(indices):
        image, label = dataset[idx]

        # 如果是tensor，转换为PIL图像
        if isinstance(image, torch.Tensor):
            # 反归一化
            mean = torch.tensor([0.485, 0.456, 0.406])
            std = torch.tensor([0.229, 0.224, 0.225])
            image = image * std.view(3, 1, 1) + mean.view(3, 1, 1)
            image = torch.clamp(image, 0, 1)

            # 转换为PIL图像
            to_pil = transforms.ToPILImage()
            image = to_pil(image)

        axes[i].imshow(image)
        axes[i].set_title(f'Label: {label}')
        axes[i].axis('off')

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path)
        print(f"Sample visualization saved to {save_path}")
    else:
        plt.show()

    plt.close()


def create_learning_rate_scheduler(optimizer, config):
    """
    创建学习率调度器
    """
    if hasattr(config, 'scheduler_type'):
        if config.scheduler_type == 'step':
            scheduler = torch.optim.lr_scheduler.StepLR(
                optimizer,
                step_size=config.step_size,
                gamma=config.gamma
            )
        elif config.scheduler_type == 'cosine':
            scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
                optimizer,
                T_max=config.epochs
            )
        elif config.scheduler_type == 'plateau':
            scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
                optimizer,
                mode='max',
                factor=0.5,
                patience=10
            )
        else:
            scheduler = None
    else:
        scheduler = None

    return scheduler


def save_model_summary(model, config, save_path):
    """
    保存模型摘要信息
    """
    total_params, trainable_params = count_parameters(model)

    summary = {
        'Model Architecture': str(model),
        'Total Parameters': total_params,
        'Trainable Parameters': trainable_params,
        'Configuration': vars(config)
    }

    with open(save_path, 'w') as f:
        f.write("MODEL SUMMARY\n")
        f.write("="*50 + "\n\n")

        for key, value in summary.items():
            if key == 'Configuration':
                f.write(f"{key}:\n")
                for k, v in value.items():
                    f.write(f"  {k}: {v}\n")
            else:
                f.write(f"{key}: {value}\n")
            f.write("\n")

    print(f"Model summary saved to {save_path}")


def check_data_quality(data_root):
    """
    检查数据质量
    """
    print("Checking data quality...")

    issues = []
    total_files = 0

    for class_name in ['normal', 'abnormal']:
        class_dir = os.path.join(data_root, class_name)

        if not os.path.exists(class_dir):
            issues.append(f"Missing directory: {class_dir}")
            continue

        files = [f for f in os.listdir(class_dir)
                if f.lower().endswith(('.png', '.jpg', '.jpeg'))]

        print(f"{class_name}: {len(files)} files")
        total_files += len(files)

        # 检查文件完整性
        corrupted_files = []
        for filename in files[:10]:  # 检查前10个文件
            file_path = os.path.join(class_dir, filename)
            try:
                with Image.open(file_path) as img:
                    img.verify()
            except Exception as e:
                corrupted_files.append(filename)

        if corrupted_files:
            issues.append(f"Corrupted files in {class_name}: {corrupted_files}")

    print(f"Total files: {total_files}")

    if issues:
        print("Issues found:")
        for issue in issues:
            print(f"  - {issue}")
    else:
        print("No issues found!")

    return len(issues) == 0


def calculate_dataset_statistics(dataloader):
    """
    计算数据集统计信息
    """
    print("Calculating dataset statistics...")

    mean = torch.zeros(3)
    std = torch.zeros(3)
    total_samples = 0

    for support_x, _, query_x, _ in dataloader:
        # 合并支持集和查询集
        batch_samples = torch.cat([support_x, query_x], dim=1)
        batch_samples = batch_samples.view(-1, 3, 84, 84)

        batch_samples = batch_samples.view(batch_samples.size(0), batch_samples.size(1), -1)
        mean += batch_samples.mean(2).sum(0)
        std += batch_samples.std(2).sum(0)
        total_samples += batch_samples.size(0)

    mean /= total_samples
    std /= total_samples

    print(f"Dataset mean: {mean}")
    print(f"Dataset std: {std}")

    return mean, std


def log_gpu_memory():
    """
    记录GPU内存使用情况
    """
    if torch.cuda.is_available():
        allocated = torch.cuda.memory_allocated() / 1024**3  # GB
        cached = torch.cuda.memory_reserved() / 1024**3  # GB
        print(f"GPU Memory - Allocated: {allocated:.2f}GB, Cached: {cached:.2f}GB")
    else:
        print("CUDA not available")


def create_experiment_dir(base_dir, experiment_name=None):
    """
    创建实验目录
    """
    import datetime

    if experiment_name is None:
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        experiment_name = f"experiment_{timestamp}"

    exp_dir = os.path.join(base_dir, experiment_name)
    os.makedirs(exp_dir, exist_ok=True)

    # 创建子目录
    subdirs = ['checkpoints', 'logs', 'plots', 'results']
    for subdir in subdirs:
        os.makedirs(os.path.join(exp_dir, subdir), exist_ok=True)

    print(f"Experiment directory created: {exp_dir}")
    return exp_dir


def backup_code(source_dir, backup_dir):
    """
    备份代码文件
    """
    import shutil

    os.makedirs(backup_dir, exist_ok=True)

    # 复制Python文件
    for filename in os.listdir(source_dir):
        if filename.endswith('.py'):
            src_path = os.path.join(source_dir, filename)
            dst_path = os.path.join(backup_dir, filename)
            shutil.copy2(src_path, dst_path)

    print(f"Code backed up to {backup_dir}")


class AverageMeter:
    """
    计算和存储平均值和当前值
    """
    def __init__(self):
        self.reset()

    def reset(self):
        self.val = 0
        self.avg = 0
        self.sum = 0
        self.count = 0

    def update(self, val, n=1):
        self.val = val
        self.sum += val * n
        self.count += n
        self.avg = self.sum / self.count


class Timer:
    """
    计时器
    """
    def __init__(self):
        self.start_time = None
        self.end_time = None

    def start(self):
        import time
        self.start_time = time.time()

    def stop(self):
        import time
        self.end_time = time.time()
        return self.end_time - self.start_time

    def elapsed(self):
        import time
        if self.start_time is None:
            return 0
        return time.time() - self.start_time


class TensorboardLogger:
    """
    Tensorboard日志记录器
    """
    def __init__(self, log_dir, experiment_name=None, config=None):
        """
        初始化Tensorboard日志记录器
        :param log_dir: 日志目录
        :param experiment_name: 实验名称
        :param config: 配置信息
        """
        # 创建实验目录
        if experiment_name is None:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            experiment_name = f"experiment_{timestamp}"

        self.experiment_dir = os.path.join(log_dir, experiment_name)
        os.makedirs(self.experiment_dir, exist_ok=True)

        # 初始化tensorboard writer
        self.writer = SummaryWriter(log_dir=self.experiment_dir)

        # 保存配置信息
        if config is not None:
            self.save_config(config)

        print(f"📊 Tensorboard logs will be saved to: {self.experiment_dir}")
        print(f"🚀 To view logs, run: tensorboard --logdir {self.experiment_dir}")

    def save_config(self, config):
        """保存配置信息到文件"""
        config_path = os.path.join(self.experiment_dir, 'config.json')

        # 将配置转换为可序列化的格式
        if hasattr(config, 'config'):
            config_dict = config.config
        elif isinstance(config, dict):
            config_dict = config
        else:
            config_dict = vars(config)

        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False)

        # 也记录到tensorboard
        config_text = json.dumps(config_dict, indent=2, ensure_ascii=False)
        self.writer.add_text('Config', config_text, 0)

    def log_scalar(self, tag, value, step):
        """记录标量值"""
        self.writer.add_scalar(tag, value, step)

    def log_scalars(self, tag_dict, step):
        """记录多个标量值"""
        for tag, value in tag_dict.items():
            self.writer.add_scalar(tag, value, step)

    def log_histogram(self, tag, values, step):
        """记录直方图"""
        self.writer.add_histogram(tag, values, step)

    def log_image(self, tag, image, step):
        """记录图像"""
        self.writer.add_image(tag, image, step)

    def log_text(self, tag, text, step):
        """记录文本"""
        self.writer.add_text(tag, text, step)

    def log_model_graph(self, model, input_tensor):
        """记录模型结构图"""
        try:
            self.writer.add_graph(model, input_tensor)
        except Exception as e:
            print(f"Failed to log model graph: {e}")

    def log_learning_rate(self, optimizer, step):
        """记录学习率"""
        for i, param_group in enumerate(optimizer.param_groups):
            lr = param_group['lr']
            self.writer.add_scalar(f'Learning_Rate/Group_{i}', lr, step)

    def log_model_parameters(self, model, step):
        """记录模型参数的统计信息"""
        for name, param in model.named_parameters():
            if param.requires_grad:
                self.writer.add_histogram(f'Parameters/{name}', param.data, step)
                if param.grad is not None:
                    self.writer.add_histogram(f'Gradients/{name}', param.grad.data, step)

    def log_gpu_memory(self, step):
        """记录GPU内存使用情况"""
        if torch.cuda.is_available():
            allocated = torch.cuda.memory_allocated() / 1024**3  # GB
            cached = torch.cuda.memory_reserved() / 1024**3  # GB
            self.writer.add_scalar('GPU_Memory/Allocated_GB', allocated, step)
            self.writer.add_scalar('GPU_Memory/Cached_GB', cached, step)

    def close(self):
        """关闭writer"""
        self.writer.close()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


def create_tensorboard_logger(config_parser):
    """
    根据配置创建tensorboard日志记录器
    :param config_parser: 配置解析器
    :return: TensorboardLogger实例
    """
    logging_config = config_parser.get_logging_config()

    log_dir = logging_config.get('log_dir', './logs')
    experiment_name = logging_config.get('experiment_name', None)

    return TensorboardLogger(
        log_dir=log_dir,
        experiment_name=experiment_name,
        config=config_parser
    )


class TensorboardLogger:
    """
    Tensorboard日志记录器
    """
    def __init__(self, log_dir, experiment_name=None, config=None):
        """
        初始化Tensorboard日志记录器
        :param log_dir: 日志目录
        :param experiment_name: 实验名称
        :param config: 配置信息
        """
        # 创建实验目录
        if experiment_name is None:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            experiment_name = f"experiment_{timestamp}"

        self.experiment_dir = os.path.join(log_dir, experiment_name)
        os.makedirs(self.experiment_dir, exist_ok=True)

        # 初始化tensorboard writer
        self.writer = SummaryWriter(log_dir=self.experiment_dir)

        # 保存配置信息
        if config is not None:
            self.save_config(config)

        print(f"Tensorboard logs will be saved to: {self.experiment_dir}")
        print(f"To view logs, run: tensorboard --logdir {self.experiment_dir}")

    def save_config(self, config):
        """保存配置信息到文件"""
        config_path = os.path.join(self.experiment_dir, 'config.json')

        # 将配置转换为可序列化的格式
        if hasattr(config, 'config'):
            config_dict = config.config
        elif isinstance(config, dict):
            config_dict = config
        else:
            config_dict = vars(config)

        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False)

        # 也记录到tensorboard
        config_text = json.dumps(config_dict, indent=2, ensure_ascii=False)
        self.writer.add_text('Config', config_text, 0)

    def log_scalar(self, tag, value, step):
        """记录标量值"""
        self.writer.add_scalar(tag, value, step)

    def log_scalars(self, tag_dict, step):
        """记录多个标量值"""
        for tag, value in tag_dict.items():
            self.writer.add_scalar(tag, value, step)

    def log_histogram(self, tag, values, step):
        """记录直方图"""
        self.writer.add_histogram(tag, values, step)

    def log_image(self, tag, image, step):
        """记录图像"""
        self.writer.add_image(tag, image, step)

    def log_text(self, tag, text, step):
        """记录文本"""
        self.writer.add_text(tag, text, step)

    def log_model_graph(self, model, input_tensor):
        """记录模型结构图"""
        try:
            self.writer.add_graph(model, input_tensor)
        except Exception as e:
            print(f"Failed to log model graph: {e}")

    def log_learning_rate(self, optimizer, step):
        """记录学习率"""
        for i, param_group in enumerate(optimizer.param_groups):
            lr = param_group['lr']
            self.writer.add_scalar(f'Learning_Rate/Group_{i}', lr, step)

    def log_model_parameters(self, model, step):
        """记录模型参数的统计信息"""
        for name, param in model.named_parameters():
            if param.requires_grad:
                self.writer.add_histogram(f'Parameters/{name}', param.data, step)
                if param.grad is not None:
                    self.writer.add_histogram(f'Gradients/{name}', param.grad.data, step)

    def log_gpu_memory(self, step):
        """记录GPU内存使用情况"""
        if torch.cuda.is_available():
            allocated = torch.cuda.memory_allocated() / 1024**3  # GB
            cached = torch.cuda.memory_reserved() / 1024**3  # GB
            self.writer.add_scalar('GPU_Memory/Allocated_GB', allocated, step)
            self.writer.add_scalar('GPU_Memory/Cached_GB', cached, step)

    def close(self):
        """关闭writer"""
        self.writer.close()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


def create_tensorboard_logger(config_parser):
    """
    根据配置创建tensorboard日志记录器
    :param config_parser: 配置解析器
    :return: TensorboardLogger实例
    """
    logging_config = config_parser.get_logging_config()

    log_dir = logging_config.get('log_dir', './logs')
    experiment_name = logging_config.get('experiment_name', None)

    return TensorboardLogger(
        log_dir=log_dir,
        experiment_name=experiment_name,
        config=config_parser
    )
