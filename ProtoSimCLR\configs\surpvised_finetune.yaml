# 多分类配置 - 区分具体的恶意流量类型

# 数据相关配置
data:
  train_dataset: "../data/ISAC_LWH_RGB/train"
  val_dataset: "../data/ISAC_LWH_RGB/val"
  img_size: 32
  img_channels: 3
  dataset_type: "FewShotDataset"  # 使用FewShotDataset
  num_classes: 10  # 根据实际类别数调整

# SimCLR预训练模型配置
simclr:
  pretrained_path: ./pretrained/checkpoint_9500.pth.tar
  base_model: "resnet18"  # 统一参数名称
  out_dim: 128           # 统一参数名称

# ProtoNet配置 - 多分类设置
protonet:
  n_way: 5              # 5-way分类（根据实际类别数调整）
  k_shot: 3            # 3-shot学习
  k_query: 12           # 查询集样本数
  task_num: 4           # 每个batch的任务数
  temperature: 10.0     # 温度参数
  meta_lr: 0.001        # 元学习率
  episodes_per_epoch: 100  # 每个epoch的episode数量

# 训练配置
training:
  epochs: 120
  num_workers: 4
  device: "cuda"
  seed: 42
  early_stopping_patience: 20
  gradient_clip_norm: 1.0

# 优化器配置
optimizer:
  type: "adam"
  lr: 0.001  # 微调训练时的学习率
  momentum: 0.9  # SGD优化器的动量
  weight_decay: 1e-4  # 权重衰减
  lr_scheduler:
    type: "cosine"
    T_max: 120
    eta_min: 0.000001

# 保存和日志配置
logging:
  save_dir: "./checkpoints"
  log_dir: "./logs"
  save_freq: 15
  log_freq: 100
  experiment_name: "multiclass_malware"

# 评估配置
evaluation:
  eval_freq: 5
  test_episodes: 100
  metrics: ["accuracy", "precision", "recall", "f1_score"]

# 数据增强配置
augmentation:
  horizontal_flip: false
  vertical_flip: false
  rotation: 10
  color_jitter:
    brightness: 0.2
    contrast: 0.2
    saturation: 0.2
    hue: 0.1
  gaussian_blur:
    kernel_size: 3
    sigma: [0.1, 2.0]
  normalize:
    mean: [0.485, 0.456, 0.406]
    std: [0.229, 0.224, 0.225]
