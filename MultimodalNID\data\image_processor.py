# -*- coding: utf-8 -*-
"""
图像数据处理器
"""

import numpy as np
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union
from PIL import Image
from scapy.all import PcapReader, Raw, TCP, UDP, ICMP

from .base_processor import BaseProcessor
from config.image_config import ImageConfig


class ImageProcessor(BaseProcessor):
    """图像数据处理器，将PCAP文件转换为RGB图像"""
    
    def __init__(self, config: ImageConfig, logger: Optional[Any] = None):
        """
        初始化图像处理器
        
        Args:
            config: 图像配置对象
            logger: 日志记录器
        """
        super().__init__(config, logger)
        
    def process_pcap(self, pcap_path: Union[str, Path]) -> Optional[np.ndarray]:
        """
        处理PCAP文件，生成RGB图像
        
        Args:
            pcap_path: PCAP文件路径
            
        Returns:
            RGB图像数组 (H, W, C) 或 None
        """
        if not self.validate_pcap(pcap_path):
            return None
        
        try:
            # 提取三层特征数据
            al_data = self._extract_al_layer(pcap_path)
            l7_data = self._extract_l7_layer(pcap_path)
            l5_data = self._extract_l5_layer(pcap_path)
            
            # 转换为图像矩阵
            al_matrix = self._bytes_to_matrix(al_data)
            l7_matrix = self._bytes_to_matrix(l7_data)
            l5_matrix = self._bytes_to_matrix(l5_data)
            
            # 组合为RGB图像
            rgb_image = self._combine_to_rgb(al_matrix, l7_matrix, l5_matrix)
            
            return rgb_image
            
        except Exception as e:
            self.logger.error(f"处理PCAP文件时发生错误: {e}")
            return None
    
    def _extract_al_layer(self, pcap_path: Union[str, Path]) -> bytes:
        """
        提取AL层（应用层）数据 - 完整包数据
        
        Args:
            pcap_path: PCAP文件路径
            
        Returns:
            AL层字节数据
        """
        raw_data = b''
        target_bytes = self.config.target_pixels
        
        try:
            with PcapReader(str(pcap_path)) as pcap_reader:
                for packet in pcap_reader:
                    # 获取完整包的字节数据
                    packet_bytes = bytes(packet)
                    raw_data += packet_bytes
                    
                    # 达到目标字节数就停止
                    if len(raw_data) >= target_bytes:
                        raw_data = raw_data[:target_bytes]
                        break
                        
        except Exception as e:
            self.logger.warning(f"提取AL层数据失败: {e}")
            
        return raw_data
    
    def _extract_l7_layer(self, pcap_path: Union[str, Path]) -> bytes:
        """
        提取L7层（应用层载荷）数据
        
        Args:
            pcap_path: PCAP文件路径
            
        Returns:
            L7层字节数据
        """
        raw_data = b''
        target_bytes = self.config.target_pixels
        
        try:
            with PcapReader(str(pcap_path)) as pcap_reader:
                for packet in pcap_reader:
                    # 提取应用层载荷
                    if packet.haslayer(Raw):
                        payload = packet[Raw].load
                        raw_data += payload
                        
                        # 达到目标字节数就停止
                        if len(raw_data) >= target_bytes:
                            raw_data = raw_data[:target_bytes]
                            break
                            
        except Exception as e:
            self.logger.warning(f"提取L7层数据失败: {e}")
            
        return raw_data
    
    def _extract_l5_layer(self, pcap_path: Union[str, Path]) -> bytes:
        """
        提取L5层（传输层）数据
        
        Args:
            pcap_path: PCAP文件路径
            
        Returns:
            L5层字节数据
        """
        raw_data = b''
        target_bytes = self.config.target_pixels
        
        try:
            with PcapReader(str(pcap_path)) as pcap_reader:
                for packet in pcap_reader:
                    # 提取传输层数据
                    transport_data = b''
                    
                    if packet.haslayer(TCP):
                        transport_data = packet[TCP].build()
                    elif packet.haslayer(UDP):
                        transport_data = packet[UDP].build()
                    elif packet.haslayer(ICMP):
                        transport_data = packet[ICMP].build()
                    
                    if transport_data:
                        raw_data += transport_data
                        
                        # 达到目标字节数就停止
                        if len(raw_data) >= target_bytes:
                            raw_data = raw_data[:target_bytes]
                            break
                            
        except Exception as e:
            self.logger.warning(f"提取L5层数据失败: {e}")
            
        return raw_data
    
    def _bytes_to_matrix(self, data: bytes) -> np.ndarray:
        """
        将字节数据转换为图像矩阵
        
        Args:
            data: 字节数据
            
        Returns:
            图像矩阵 (H, W)
        """
        target_pixels = self.config.target_pixels
        
        # 如果数据不足，用零填充
        if len(data) < target_pixels:
            data += b'\x00' * (target_pixels - len(data))
        elif len(data) > target_pixels:
            data = data[:target_pixels]
        
        # 转换为numpy数组
        pixel_array = np.frombuffer(data, dtype=np.uint8)
        
        # 重塑为图像尺寸
        height, width = self.config.image_size
        matrix = pixel_array.reshape(height, width)
        
        return matrix
    
    def _combine_to_rgb(self, al_matrix: np.ndarray, 
                       l7_matrix: np.ndarray, 
                       l5_matrix: np.ndarray) -> np.ndarray:
        """
        将三层特征组合为RGB图像
        
        Args:
            al_matrix: AL层矩阵
            l7_matrix: L7层矩阵
            l5_matrix: L5层矩阵
            
        Returns:
            RGB图像数组 (H, W, 3)
        """
        # 确保所有矩阵尺寸一致
        height, width = self.config.image_size
        
        # 调整矩阵尺寸
        al_matrix = self._resize_matrix(al_matrix, (height, width))
        l7_matrix = self._resize_matrix(l7_matrix, (height, width))
        l5_matrix = self._resize_matrix(l5_matrix, (height, width))
        
        # 根据配置映射通道
        channels = {}
        channels[self.config.al_layer_channel] = al_matrix
        channels[self.config.l7_layer_channel] = l7_matrix
        channels[self.config.l5_layer_channel] = l5_matrix
        
        # 组合RGB通道
        rgb_image = np.stack([
            channels.get('R', np.zeros((height, width), dtype=np.uint8)),
            channels.get('G', np.zeros((height, width), dtype=np.uint8)),
            channels.get('B', np.zeros((height, width), dtype=np.uint8))
        ], axis=-1)
        
        return rgb_image
    
    def _resize_matrix(self, matrix: np.ndarray, 
                      target_size: Tuple[int, int]) -> np.ndarray:
        """
        调整矩阵尺寸
        
        Args:
            matrix: 输入矩阵
            target_size: 目标尺寸 (height, width)
            
        Returns:
            调整后的矩阵
        """
        current_height, current_width = matrix.shape
        target_height, target_width = target_size
        
        if (current_height, current_width) == (target_height, target_width):
            return matrix
        
        # 使用PIL进行尺寸调整
        image = Image.fromarray(matrix, mode='L')
        resized_image = image.resize((target_width, target_height), Image.LANCZOS)
        
        return np.array(resized_image)
    
    def save_image(self, image: np.ndarray, 
                   output_path: Union[str, Path]) -> bool:
        """
        保存图像到文件
        
        Args:
            image: 图像数组
            output_path: 输出路径
            
        Returns:
            是否保存成功
        """
        try:
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 确保图像数据类型正确
            if image.dtype != np.uint8:
                image = image.astype(np.uint8)
            
            # 创建PIL图像并保存
            if len(image.shape) == 3:
                pil_image = Image.fromarray(image, 'RGB')
            else:
                pil_image = Image.fromarray(image, 'L')
            
            pil_image.save(output_path)
            self.logger.info(f"图像已保存到: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存图像时发生错误: {e}")
            return False
    
    def apply_normalization(self, image: np.ndarray) -> np.ndarray:
        """
        应用图像归一化
        
        Args:
            image: 输入图像
            
        Returns:
            归一化后的图像
        """
        if not self.config.normalize:
            return image
        
        # 转换为浮点数
        image = image.astype(np.float32) / 255.0
        
        # 应用均值和标准差归一化
        mean = np.array(self.config.mean)
        std = np.array(self.config.std)
        
        if len(image.shape) == 3:
            image = (image - mean) / std
        else:
            # 对于灰度图像，使用第一个通道的参数
            image = (image - mean[0]) / std[0]
        
        return image
