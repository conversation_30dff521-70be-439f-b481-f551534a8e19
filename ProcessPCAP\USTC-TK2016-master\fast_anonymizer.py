#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
High-Efficiency PCAP Anonymizer
Optimized for fast IP and MAC address anonymization only
Uses memory-efficient streaming and optimized data structures
"""

import os
import logging
import random
import socket
import struct
import hashlib
from pathlib import Path
from collections import defaultdict
import dpkt
import tqdm
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import multiprocessing as mp


class EfficientAnonymizer:
    """
    High-performance anonymizer focused on IP and MAC address anonymization only
    Optimized for speed and memory efficiency
    """

    def __init__(self, input_base_dir="./temp_files/splitpcap",
                 output_base_dir="./temp_files/anonymous",
                 use_deterministic=True,
                 max_workers=None):
        """
        Initialize efficient anonymizer

        Args:
            input_base_dir: Input directory with PCAP files
            output_base_dir: Output directory for anonymized files
            use_deterministic: Use deterministic anonymization (same input -> same output)
            max_workers: Number of worker processes (None = auto-detect)
        """
        self.input_base_dir = Path(input_base_dir)
        self.output_base_dir = Path(output_base_dir)
        self.use_deterministic = use_deterministic
        self.max_workers = max_workers or min(mp.cpu_count(), 8)
        self.logger = self._setup_logger()

        # Pre-generate IP and MAC pools for better performance
        self._ip_pool = self._generate_ip_pool(65536)  # 64K IPs
        self._mac_pool = self._generate_mac_pool(65536)  # 64K MACs
        self._ip_counter = 0
        self._mac_counter = 0

    def _setup_logger(self):
        """Setup optimized logging"""
        logger = logging.getLogger('EfficientAnonymizer')
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger

    def _generate_ip_pool(self, size):
        """Pre-generate IP address pool for better performance"""
        ips = []
        for i in range(size):
            # Use private IP ranges: 10.0.0.0/8, **********/12, ***********/16
            if i < 16777216:  # 10.x.x.x
                ip = f"10.{(i >> 16) & 0xFF}.{(i >> 8) & 0xFF}.{i & 0xFF}"
            elif i < 17825792:  # 172.16-31.x.x
                j = i - 16777216
                ip = f"172.{16 + ((j >> 16) & 0xF)}.{(j >> 8) & 0xFF}.{j & 0xFF}"
            else:  # 192.168.x.x
                j = i - 17825792
                ip = f"192.168.{(j >> 8) & 0xFF}.{j & 0xFF}"
            ips.append(ip)
        return ips

    def _generate_mac_pool(self, size):
        """Pre-generate MAC address pool for better performance"""
        macs = []
        for i in range(size):
            # Use locally administered MAC addresses (02:xx:xx:xx:xx:xx)
            mac = f"02:{(i >> 32) & 0xFF:02x}:{(i >> 24) & 0xFF:02x}:{(i >> 16) & 0xFF:02x}:{(i >> 8) & 0xFF:02x}:{i & 0xFF:02x}"
            macs.append(mac)
        return macs

    def _get_deterministic_ip(self, original_ip):
        """Get deterministic IP mapping using hash"""
        if self.use_deterministic:
            hash_val = int(hashlib.md5(original_ip.encode()).hexdigest()[:8], 16)
            return self._ip_pool[hash_val % len(self._ip_pool)]
        else:
            self._ip_counter = (self._ip_counter + 1) % len(self._ip_pool)
            return self._ip_pool[self._ip_counter]

    def _get_deterministic_mac(self, original_mac):
        """Get deterministic MAC mapping using hash"""
        if self.use_deterministic:
            hash_val = int(hashlib.md5(original_mac.encode()).hexdigest()[:8], 16)
            return self._mac_pool[hash_val % len(self._mac_pool)]
        else:
            self._mac_counter = (self._mac_counter + 1) % len(self._mac_pool)
            return self._mac_pool[self._mac_counter]

    def _anonymize_packet_fast(self, ts, buf, ip_map, mac_map):
        """
        Fast packet anonymization - IP and MAC only
        Optimized for maximum performance
        """
        try:
            # Parse Ethernet frame
            eth = dpkt.ethernet.Ethernet(buf)
            modified = False

            # Anonymize MAC addresses (Layer 2)
            src_mac = eth.src.hex(':')
            dst_mac = eth.dst.hex(':')

            if src_mac not in mac_map:
                mac_map[src_mac] = self._get_deterministic_mac(src_mac)
            if dst_mac not in mac_map:
                mac_map[dst_mac] = self._get_deterministic_mac(dst_mac)

            # Update MAC addresses
            eth.src = bytes.fromhex(mac_map[src_mac].replace(':', ''))
            eth.dst = bytes.fromhex(mac_map[dst_mac].replace(':', ''))
            modified = True

            # Anonymize IP addresses (Layer 3) if present
            if isinstance(eth.data, dpkt.ip.IP):
                ip = eth.data
                src_ip = socket.inet_ntoa(ip.src)
                dst_ip = socket.inet_ntoa(ip.dst)

                if src_ip not in ip_map:
                    ip_map[src_ip] = self._get_deterministic_ip(src_ip)
                if dst_ip not in ip_map:
                    ip_map[dst_ip] = self._get_deterministic_ip(dst_ip)

                # Update IP addresses
                ip.src = socket.inet_aton(ip_map[src_ip])
                ip.dst = socket.inet_aton(ip_map[dst_ip])

                # Clear checksums for recalculation
                ip.sum = 0
                if hasattr(ip.data, 'sum'):
                    ip.data.sum = 0

                modified = True

            # Return modified packet
            if modified:
                return ts, bytes(eth)
            else:
                return ts, buf

        except Exception as e:
            # Return original packet if anonymization fails
            return ts, buf

    def anonymize_pcap_file(self, input_file, output_file):
        """
        Anonymize a single PCAP file efficiently
        """
        input_file = Path(input_file)
        output_file = Path(output_file)

        # Create output directory
        output_file.parent.mkdir(parents=True, exist_ok=True)

        # Local mapping dictionaries for this file
        ip_map = {}
        mac_map = {}

        try:
            with open(input_file, 'rb') as f_in:
                reader = dpkt.pcap.Reader(f_in)
                linktype = reader.datalink()

                with open(output_file, 'wb') as f_out:
                    writer = dpkt.pcap.Writer(f_out, snaplen=65535, linktype=linktype)

                    # Process packets with progress bar
                    file_size = input_file.stat().st_size
                    with tqdm.tqdm(total=file_size, unit='B', unit_scale=True,
                                   desc=f"Anonymizing {input_file.name}", leave=False) as pbar:
                        processed_bytes = 0
                        for ts, buf in reader:
                            # Anonymize packet
                            new_ts, new_buf = self._anonymize_packet_fast(ts, buf, ip_map, mac_map)
                            writer.writepkt(new_buf, new_ts)

                            # Update progress
                            processed_bytes += len(buf)
                            pbar.update(len(buf))

            return True, len(ip_map), len(mac_map)

        except Exception as e:
            self.logger.error(f"Failed to anonymize {input_file}: {e}")
            return False, 0, 0

    def process_directory(self, max_workers=None):
        """
        Process all PCAP files in directory with parallel processing
        """
        if not self.input_base_dir.exists():
            raise FileNotFoundError(f"Input directory not found: {self.input_base_dir}")

        # Find all PCAP files
        pcap_files = []
        for root, dirs, files in os.walk(self.input_base_dir):
            for file in files:
                if file.endswith('.pcap'):
                    input_path = Path(root) / file
                    rel_path = input_path.relative_to(self.input_base_dir)
                    output_path = self.output_base_dir / rel_path
                    pcap_files.append((input_path, output_path))

        if not pcap_files:
            self.logger.warning("No PCAP files found")
            return

        self.logger.info(f"Found {len(pcap_files)} PCAP files to anonymize")
        self.logger.info(f"Using {max_workers or self.max_workers} worker processes")

        # Process files in parallel
        total_ips = 0
        total_macs = 0
        successful = 0

        with ProcessPoolExecutor(max_workers=max_workers or self.max_workers) as executor:
            # Submit all tasks
            futures = {
                executor.submit(self.anonymize_pcap_file, input_file, output_file): (input_file, output_file)
                for input_file, output_file in pcap_files
            }

            # Process results with progress bar
            with tqdm.tqdm(total=len(futures), desc="Overall Progress") as pbar:
                for future in futures:
                    input_file, output_file = futures[future]
                    try:
                        success, ip_count, mac_count = future.result()
                        if success:
                            successful += 1
                            total_ips += ip_count
                            total_macs += mac_count
                        pbar.set_description(f"Completed: {successful}/{len(pcap_files)}")
                    except Exception as e:
                        self.logger.error(f"Task failed for {input_file}: {e}")
                    finally:
                        pbar.update(1)

        # Summary
        self.logger.info(f"Anonymization completed:")
        self.logger.info(f"  - Files processed: {successful}/{len(pcap_files)}")
        self.logger.info(f"  - Unique IPs anonymized: {total_ips}")
        self.logger.info(f"  - Unique MACs anonymized: {total_macs}")
        self.logger.info(f"  - Output directory: {self.output_base_dir}")


def anonymize_single_file(input_file, output_file, deterministic=True):
    """
    Convenience function to anonymize a single file
    """
    anonymizer = EfficientAnonymizer(use_deterministic=deterministic)
    return anonymizer.anonymize_pcap_file(input_file, output_file)


if __name__ == "__main__":
    import  argparse
    parser = argparse.ArgumentParser(description='PCAP Processing Pipeline')
    parser.add_argument('--input', '-i', default='./pcap_dataset',
                        help='Input directory containing PCAP files')
    parser.add_argument('--output', '-o', default='./img_dataset/ISAC218',
                        help='Output directory for RGB images')
    # Example usage
    anonymizer = EfficientAnonymizer(
        input_base_dir=parser.parse_args().input,
        output_base_dir=parser.parse_args().output,
        use_deterministic=True,  # Same input always produces same output
        max_workers=4  # Adjust based on your CPU cores
    )

    # Process all files
    anonymizer.process_directory()
