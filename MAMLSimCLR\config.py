"""
配置文件 - SimCLR + MAML 集成框架
支持YAML配置文件和命令行参数
"""

import argparse
import yaml
import os
from types import SimpleNamespace


class Config:
    """配置类，支持从YAML文件和命令行参数加载配置"""

    def __init__(self, **kwargs):
        config_path = kwargs.get('config_path')
        # 默认配置
        self.default_config = {
            # 数据相关配置
            'data': {
                'data_root': '../data/ISAC_LWH_RGB',
                'img_size': 84,
                'img_channels': 3,
                'train_ratio': 0.7,
                'val_ratio': 0.15,
                'test_ratio': 0.15
            },

            # SimCLR预训练模型配置
            'simclr': {
                'pretrained_path': None,
                'backbone': 'resnet18',
                'feature_dim': 512,
                'freeze_backbone': False
            },

            # MAML配置
            'maml': {
                'n_way': 2,
                'k_shot': 5,
                'k_query': 15,
                'task_num': 4,
                'update_lr': 0.01,
                'meta_lr': 1e-3,
                'update_step': 5,
                'update_step_test': 10
            },

            # 训练配置
            'training': {
                'epochs': 100,
                'batch_size': 32,
                'num_workers': 4,
                'device': 'cuda',
                'seed': 42,
                'early_stopping_patience': 15,
                'gradient_clip_norm': 1.0
            },

            # 优化器配置
            'optimizer': {
                'type': 'adam',
                'lr_scheduler': {
                    'type': 'cosine',
                    'T_max': 100,
                    'eta_min': 1e-6
                }
            },

            # 保存和日志配置
            'logging': {
                'save_dir': './checkpoints',
                'log_dir': './logs',
                'save_freq': 10,
                'log_freq': 100,
                'experiment_name': None
            },

            # 评估配置
            'evaluation': {
                'eval_freq': 5,
                'test_episodes': 100,
                'metrics': ['accuracy', 'precision', 'recall', 'f1_score']
            },

            # 数据增强配置
            'augmentation': {
                'horizontal_flip': True,
                'vertical_flip': False,
                'rotation': 10,
                'color_jitter': {
                    'brightness': 0.2,
                    'contrast': 0.2,
                    'saturation': 0.2,
                    'hue': 0.1
                },
                'gaussian_blur': {
                    'kernel_size': 3,
                    'sigma': [0.1, 2.0]
                },
                'normalize': {
                    'mean': [0.485, 0.456, 0.406],
                    'std': [0.229, 0.224, 0.225]
                }
            }
        }

        # 加载配置
        self.config = self.default_config.copy()

        if config_path and os.path.exists(config_path):
            self.load_from_yaml(config_path)

        # 更新配置
        self.update_config(**kwargs)

        # 转换为命名空间以便访问
        self._convert_to_namespace()

    def load_from_yaml(self, config_path):
        """从YAML文件加载配置"""
        with open(config_path, 'r', encoding='utf-8') as f:
            yaml_config = yaml.safe_load(f)

        self._deep_update(self.config, yaml_config)
        print(f"Configuration loaded from {config_path}")

    def _deep_update(self, base_dict, update_dict):
        """深度更新字典"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value

    def update_config(self, **kwargs):
        """更新配置"""
        for key, value in kwargs.items():
            if '.' in key:
                # 支持嵌套键，如 'training.epochs'
                keys = key.split('.')
                current = self.config
                for k in keys[:-1]:
                    if k not in current:
                        current[k] = {}
                    current = current[k]
                current[keys[-1]] = value
            else:
                self.config[key] = value

    def _convert_to_namespace(self):
        """将配置转换为命名空间以便访问"""
        def dict_to_namespace(d):
            if isinstance(d, dict):
                return SimpleNamespace(**{k: dict_to_namespace(v) for k, v in d.items()})
            return d

        # 扁平化配置以保持向后兼容
        flat_config = {}
        self._flatten_dict(self.config, flat_config)

        # 同时保留嵌套结构
        for key, value in flat_config.items():
            setattr(self, key, value)

        # 保留嵌套访问
        self.data = dict_to_namespace(self.config['data'])
        self.simclr = dict_to_namespace(self.config['simclr'])
        self.maml = dict_to_namespace(self.config['maml'])
        self.training = dict_to_namespace(self.config['training'])
        self.optimizer = dict_to_namespace(self.config['optimizer'])
        self.logging = dict_to_namespace(self.config['logging'])
        self.evaluation = dict_to_namespace(self.config['evaluation'])
        self.augmentation = dict_to_namespace(self.config['augmentation'])

    def _flatten_dict(self, d, parent_key='', sep='_'):
        """扁平化嵌套字典"""
        items = []
        for k, v in d.items():
            new_key = f"{parent_key}{sep}{k}" if parent_key else k
            if isinstance(v, dict):
                items.extend(self._flatten_dict(v, new_key, sep=sep).items())
            else:
                items.append((new_key, v))
        return dict(items)

    def save_config(self, save_path):
        """保存配置到YAML文件"""
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        with open(save_path, 'w', encoding='utf-8') as f:
            yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
        print(f"Configuration saved to {save_path}")

    def print_config(self):
        """打印配置"""
        print("Configuration:")
        print(yaml.dump(self.config, default_flow_style=False, allow_unicode=True))


def get_config():
    """获取配置，支持命令行参数"""
    parser = argparse.ArgumentParser(description='SimCLR + MAML Integration for Network Traffic Detection')

    # 配置文件路径
    parser.add_argument('--config', type=str, default=None,
                        help='YAML配置文件路径')

    # 主要参数（可以覆盖YAML配置）
    parser.add_argument('--data_root', type=str, default=None,
                        help='数据集根目录')
    parser.add_argument('--pretrained_path', type=str, default=None,
                        help='SimCLR预训练模型路径')
    parser.add_argument('--epochs', type=int, default=None,
                        help='训练轮数')
    parser.add_argument('--device', type=str, default=None,
                        help='训练设备')
    parser.add_argument('--experiment_name', type=str, default=None,
                        help='实验名称')

    args = parser.parse_args()

    # 创建配置对象
    config_kwargs = {k: v for k, v in vars(args).items() if v is not None and k != 'config'}
    config_kwargs['config_path'] = args.config
    config = Config(**config_kwargs)

    return config


def load_config(**kwargs):
    """
    简化的配置加载函数

    Args:
        **kwargs: 包含所有配置参数的字典
            - config_path: YAML配置文件路径（可选）
            - 其他配置参数

    Returns:
        Config: 配置对象
    """
    return Config(**kwargs)


# 网络配置
def get_network_config():
    """
    获取网络架构配置
    """
    config = [
        ('conv2d', [32, 3, 3, 3, 1, 0]),  # [out_channels, in_channels, kernel_size, stride, padding]
        ('relu', [True]),
        ('bn', [32]),
        ('max_pool2d', [2, 2, 0]),  # [kernel_size, stride, padding]

        ('conv2d', [32, 32, 3, 3, 1, 0]),
        ('relu', [True]),
        ('bn', [32]),
        ('max_pool2d', [2, 2, 0]),

        ('conv2d', [32, 32, 3, 3, 1, 0]),
        ('relu', [True]),
        ('bn', [32]),
        ('max_pool2d', [2, 2, 0]),

        ('conv2d', [32, 32, 3, 3, 1, 0]),
        ('relu', [True]),
        ('bn', [32]),
        ('max_pool2d', [2, 1, 0]),

        ('flatten', []),
        ('linear', [2, 32])  # [out_features, in_features] - 2类分类
    ]
    return config

# 数据增强配置
def get_augmentation_config():
    """
    获取数据增强配置
    """
    return {
        'horizontal_flip': True,
        'vertical_flip': False,
        'rotation': 10,
        'color_jitter': {
            'brightness': 0.2,
            'contrast': 0.2,
            'saturation': 0.2,
            'hue': 0.1
        },
        'gaussian_blur': {
            'kernel_size': 3,
            'sigma': (0.1, 2.0)
        },
        'normalize': {
            'mean': [0.485, 0.456, 0.406],
            'std': [0.229, 0.224, 0.225]
        }
    }
