# 深度学习框架
torch>=1.9.0
torchvision>=0.10.0
torchaudio>=0.9.0

# 网络数据处理
scapy>=2.4.5

# 图像处理
Pillow>=8.3.0
opencv-python>=4.5.0

# 数据处理和科学计算
numpy>=1.21.0
pandas>=1.3.0
scipy>=1.7.0

# 机器学习
scikit-learn>=1.0.0

# 进度条和可视化
tqdm>=4.62.0
matplotlib>=3.4.0
seaborn>=0.11.0

# 日志和监控
tensorboard>=2.7.0
wandb>=0.12.0

# 配置文件处理
PyYAML>=5.4.0

# 数据验证
pydantic>=1.8.0

# 并行处理
joblib>=1.1.0

# 文件处理
pathlib2>=2.3.6

# 网络工具
requests>=2.26.0

# 测试框架
pytest>=6.2.0
pytest-cov>=2.12.0

# 代码质量
black>=21.7.0
flake8>=3.9.0
isort>=5.9.0

# 类型检查
mypy>=0.910

# 文档生成
sphinx>=4.1.0
sphinx-rtd-theme>=0.5.0

# Jupyter支持
jupyter>=1.0.0
ipykernel>=6.0.0

# 其他工具
click>=8.0.0
rich>=10.7.0
