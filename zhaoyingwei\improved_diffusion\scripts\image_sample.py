"""
Generate a large batch of image samples from a model and save them as a large
numpy array. This can be used to produce samples for FID evaluation.
"""

import argparse
import os
import sys
sys.path.append(".")

import numpy as np
import torch as th
import torch.distributed as dist
from PIL import Image

from improved_diffusion import dist_util, logger
from improved_diffusion.script_util import (
    NUM_CLASSES,
    model_and_diffusion_defaults,
    create_model_and_diffusion,
    add_dict_to_argparser,
    args_to_dict,
)
import os
os.environ['CUDA_VISIBLE_DEVICES'] = '3'

def main():
    args = create_argparser().parse_args()

    dist_util.setup_dist()
    logger.configure()

    logger.log("creating model and diffusion...")
    model, diffusion = create_model_and_diffusion(
        **args_to_dict(args, model_and_diffusion_defaults().keys())
    )
    model.load_state_dict(
        dist_util.load_state_dict(args.model_path, map_location="cpu")
    )
    model.to(dist_util.dev())
    model.eval()

    logger.log("sampling...")
    all_images = []
    all_labels = []
    sample_count = 0

    # 创建保存图像的根目录
    if dist.get_rank() == 0:
        samples_dir = os.path.join(logger.get_dir(), "samples")
        os.makedirs(samples_dir, exist_ok=True)

    while len(all_images) * args.batch_size < args.num_samples:
        model_kwargs = {}
        if args.class_cond:
            classes = th.randint(
                low=0, high=NUM_CLASSES, size=(args.batch_size,), device=dist_util.dev()
            )
            model_kwargs["y"] = classes
        sample_fn = (
            diffusion.p_sample_loop if not args.use_ddim else diffusion.ddim_sample_loop
        )
        sample = sample_fn(
            model,
            (args.batch_size, 1, args.image_size, args.image_size),
            clip_denoised=args.clip_denoised,
            model_kwargs=model_kwargs,
        )
        sample = ((sample + 1) * 127.5).clamp(0, 255).to(th.uint8)
        sample = sample.permute(0, 2, 3, 1)
        sample = sample.contiguous()

        gathered_samples = [th.zeros_like(sample) for _ in range(dist.get_world_size())]
        dist.all_gather(gathered_samples, sample)  # gather not supported with NCCL
        all_images.extend([sample.cpu().numpy() for sample in gathered_samples])
        if args.class_cond:
            gathered_labels = [
                th.zeros_like(classes) for _ in range(dist.get_world_size())
            ]
            dist.all_gather(gathered_labels, classes)
            all_labels.extend([labels.cpu().numpy() for labels in gathered_labels])

        # 保存图像
        if dist.get_rank() == 0:
            samples_dir = os.path.join(logger.get_dir(), "samples")
            samples_list = [sample.cpu().numpy() for sample in gathered_samples]

            if args.class_cond:
                labels_list = [labels.cpu().numpy() for labels in gathered_labels]

                for samples_batch, labels_batch in zip(samples_list, labels_list):
                    for i in range(len(samples_batch)):
                        if sample_count >= args.num_samples:
                            break

                        # 获取类别
                        class_id = labels_batch[i]

                        # 创建类别目录
                        class_dir = os.path.join(samples_dir, str(class_id))
                        os.makedirs(class_dir, exist_ok=True)

                        # 保存灰度图像
                        image_array = samples_batch[i].squeeze()  # 移除单通道维度
                        image = Image.fromarray(image_array, mode='L')
                        image_path = os.path.join(class_dir, f"sample_{sample_count:05d}.png")
                        image.save(image_path)
                        sample_count += 1
            else:
                for samples_batch in samples_list:
                    for i in range(len(samples_batch)):
                        if sample_count >= args.num_samples:
                            break

                        # 保存灰度图像到根目录
                        image_array = samples_batch[i].squeeze()  # 移除单通道维度
                        image = Image.fromarray(image_array, mode='L')
                        image_path = os.path.join(samples_dir, f"sample_{sample_count:05d}.png")
                        image.save(image_path)
                        sample_count += 1

        logger.log(f"created {len(all_images) * args.batch_size} samples")

    # 原有的numpy保存方式保持不变（可选）
    arr = np.concatenate(all_images, axis=0)
    arr = arr[: args.num_samples]
    if args.class_cond:
        label_arr = np.concatenate(all_labels, axis=0)
        label_arr = label_arr[: args.num_samples]
    if dist.get_rank() == 0:
        shape_str = "x".join([str(x) for x in arr.shape])
        out_path = os.path.join(logger.get_dir(), f"samples_{shape_str}.npz")
        logger.log(f"saving to {out_path}")
        if args.class_cond:
            np.savez(out_path, arr, label_arr)
        else:
            np.savez(out_path, arr)
    dist.barrier()
    logger.log("sampling complete")


def create_argparser():
    defaults = dict(
        clip_denoised=True,
        num_samples=21719,
        batch_size=100,
        use_ddim=False,  # 使用DDIM加速采样
        model_path="",
    )
    defaults.update(model_and_diffusion_defaults())
    parser = argparse.ArgumentParser()
    add_dict_to_argparser(parser, defaults)
    return parser


if __name__ == "__main__":
    main()
