# 默认配置文件
# 双分支多模态对比学习网络入侵检测系统

# 基础配置
base_config:
  project_root: "."
  data_root: "./data"
  output_root: "./output"
  log_dir: "./logs"
  checkpoint_dir: "./checkpoints"
  
  # 数据集配置
  dataset_name: "network_traffic"
  train_split: 0.7
  val_split: 0.15
  test_split: 0.15
  
  # 设备配置
  device: "cuda"
  num_workers: 4
  pin_memory: true
  
  # 随机种子
  seed: 42
  
  # 日志配置
  log_level: "INFO"
  
  # 实验配置
  experiment_name: "multimodal_nid"
  tags: []

# 图像分支配置
image_config:
  # 图像数据配置
  image_size: [32, 32]
  channels: 3
  normalize: true
  mean: [0.485, 0.456, 0.406]
  std: [0.229, 0.224, 0.225]
  
  # PCAP到图像转换配置
  max_sessions: 6000
  min_file_bytes: 199
  max_file_bytes: 1048576  # 1MB
  target_pixels: 1024      # 32x32
  
  # 图像增强配置
  use_augmentation: true
  augmentation_params:
    rotation_range: 10
    width_shift_range: 0.1
    height_shift_range: 0.1
    horizontal_flip: true
    zoom_range: 0.1
    brightness_range: [0.8, 1.2]
    contrast_range: [0.8, 1.2]
  
  # 编码器配置
  encoder_type: "resnet18"  # resnet18, resnet50, efficientnet, vgg16
  pretrained: true
  feature_dim: 512
  dropout_rate: 0.1
  
  # 层特征映射配置
  al_layer_channel: "R"  # AL层映射到R通道
  l7_layer_channel: "G"  # L7层映射到G通道
  l5_layer_channel: "B"  # L5层映射到B通道

# 序列分支配置
sequence_config:
  # 序列数据配置
  max_sequence_length: 100
  feature_dim: 64
  padding_value: 0.0
  truncate_strategy: "tail"  # head, tail, random
  
  # 特征提取配置
  extract_packet_features: true
  extract_flow_features: true
  extract_statistical_features: true
  
  # 包级特征配置
  packet_features:
    - "packet_size"
    - "protocol_type"
    - "tcp_flags"
    - "inter_arrival_time"
    - "payload_size"
    - "header_length"
  
  # 流级特征配置
  flow_features:
    - "flow_duration"
    - "total_packets"
    - "total_bytes"
    - "avg_packet_size"
    - "packet_rate"
    - "byte_rate"
  
  # 统计特征配置
  statistical_features:
    - "packet_size_mean"
    - "packet_size_std"
    - "packet_size_min"
    - "packet_size_max"
    - "iat_mean"
    - "iat_std"
    - "iat_min"
    - "iat_max"
  
  # 时间窗口配置
  time_window_size: 10.0  # 秒
  overlap_ratio: 0.5
  
  # 编码器配置
  encoder_type: "transformer"  # transformer, lstm, gru, tcn
  
  # Transformer配置
  transformer_config:
    num_layers: 6
    num_heads: 8
    hidden_dim: 512
    feedforward_dim: 2048
    dropout: 0.1
    activation: "relu"
  
  # LSTM/GRU配置
  rnn_config:
    hidden_size: 256
    num_layers: 2
    bidirectional: true
    dropout: 0.1
  
  # TCN配置
  tcn_config:
    num_channels: [64, 128, 256]
    kernel_size: 3
    dropout: 0.1
    activation: "relu"
  
  # 输出配置
  output_feature_dim: 512
  use_attention: true
  dropout_rate: 0.1

# 训练配置
training_config:
  # 基础训练配置
  batch_size: 32
  num_epochs: 100
  learning_rate: 0.001
  weight_decay: 0.0001
  
  # 优化器配置
  optimizer: "adam"  # adam, sgd, adamw
  optimizer_params:
    betas: [0.9, 0.999]
    eps: 1e-8
  
  # 学习率调度配置
  scheduler: "cosine"  # cosine, step, exponential, plateau
  scheduler_params:
    T_max: 100
    eta_min: 1e-6
  
  # 对比学习配置
  temperature: 0.07
  use_hard_negatives: true
  negative_sampling_ratio: 1.0
  
  # 多模态融合配置
  fusion_strategy: "attention"  # concat, attention, gated
  fusion_dropout: 0.1
  cross_modal_weight: 1.0
  
  # 损失函数配置
  contrastive_loss_weight: 1.0
  alignment_loss_weight: 0.5
  reconstruction_loss_weight: 0.1
  
  # 正则化配置
  gradient_clip_norm: 1.0
  label_smoothing: 0.0
  mixup_alpha: 0.0
  
  # 验证和保存配置
  validation_frequency: 1
  save_frequency: 10
  early_stopping_patience: 20
  save_best_only: true
  
  # 日志配置
  log_frequency: 100
  use_tensorboard: true
  use_wandb: false
  wandb_project: "multimodal-nid"
  
  # 评估配置
  evaluation_metrics:
    - "accuracy"
    - "precision"
    - "recall"
    - "f1_score"
    - "auc_roc"
    - "contrastive_accuracy"
