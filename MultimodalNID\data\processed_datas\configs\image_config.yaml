al_layer_channel: R
augmentation_params:
  brightness_range:
  - 0.8
  - 1.2
  contrast_range:
  - 0.8
  - 1.2
  height_shift_range: 0.1
  horizontal_flip: true
  rotation_range: 10
  width_shift_range: 0.1
  zoom_range: 0.1
channels: 3
checkpoint_dir: ./checkpoints
data_root: ./data
dataset_name: network_traffic
device: cuda
dropout_rate: 0.1
encoder_type: resnet18
experiment_name: multimodal_nid
feature_dim: 512
image_size: !!python/tuple
- 32
- 32
l5_layer_channel: B
l7_layer_channel: G
log_dir: ./logs
log_format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
log_level: INFO
max_file_bytes: 1048576
max_sessions: 6000
mean: !!python/tuple
- 0.485
- 0.456
- 0.406
min_file_bytes: 199
normalize: true
num_workers: 4
output_root: ./output
pin_memory: true
pretrained: true
project_root: .
seed: 42
std: !!python/tuple
- 0.229
- 0.224
- 0.225
tags: []
target_pixels: 1024
test_split: 0.15
train_split: 0.7
use_augmentation: true
val_split: 0.15
