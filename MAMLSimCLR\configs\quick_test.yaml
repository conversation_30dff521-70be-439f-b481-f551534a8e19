# 快速测试配置 - 用于调试和快速验证

# 数据相关配置
data:
  data_root: "../data/ISAC_LWH_RGB"
  img_size: 84
  img_channels: 3
  dataset_type: "ISAC"
  classification_type: "binary"

# SimCLR预训练模型配置
simclr:
  pretrained_path: null
  backbone: "resnet18"
  feature_dim: 512
  freeze_backbone: true  # 冻结骨干网络以加快训练

# MAML配置
maml:
  n_way: 2
  k_shot: 3             # 减少shot数以加快训练
  k_query: 10           # 减少查询样本数
  task_num: 2           # 减少任务数
  update_lr: 0.01
  meta_lr: 0.001
  update_step: 3        # 减少更新步数
  update_step_test: 5

# 训练配置
training:
  epochs: 20            # 减少训练轮数
  batch_size: 16        # 减少批次大小
  num_workers: 2
  device: "cuda"
  seed: 42
  early_stopping_patience: 5
  gradient_clip_norm: 1.0

# 优化器配置
optimizer:
  type: "adam"
  lr_scheduler:
    type: "step"
    step_size: 10
    gamma: 0.5

# 保存和日志配置
logging:
  save_dir: "./checkpoints_test"
  log_dir: "./logs_test"
  save_freq: 5
  log_freq: 10
  experiment_name: "quick_test"

# 评估配置
evaluation:
  eval_freq: 2          # 更频繁的评估
  test_episodes: 20     # 减少测试episode数
  metrics: ["accuracy", "f1_score"]

# 数据增强配置（简化）
augmentation:
  horizontal_flip: true
  vertical_flip: false
  rotation: 5           # 减少旋转角度
  color_jitter:
    brightness: 0.1
    contrast: 0.1
    saturation: 0.1
    hue: 0.05
  gaussian_blur:
    kernel_size: 3
    sigma: [0.1, 1.0]
  normalize:
    mean: [0.485, 0.456, 0.406]
    std: [0.229, 0.224, 0.225]
