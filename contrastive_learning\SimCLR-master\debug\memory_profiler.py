#!/usr/bin/env python3
"""
内存使用分析脚本
分析 LlamaEncoder + SimCLR 训练的内存瓶颈
"""

import torch
import torch.nn.functional as F
import numpy as np
import psutil
import os
import sys

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.llama_encoder import LlamaEncoder, LlamaEncoderConfig

def get_memory_usage():
    """获取当前内存使用情况"""
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    return {
        'rss_mb': memory_info.rss / 1024 / 1024,  # 物理内存
        'vms_mb': memory_info.vms / 1024 / 1024,  # 虚拟内存
    }

def get_gpu_memory():
    """获取GPU内存使用情况"""
    if torch.cuda.is_available():
        return {
            'allocated_mb': torch.cuda.memory_allocated() / 1024 / 1024,
            'cached_mb': torch.cuda.memory_reserved() / 1024 / 1024,
            'max_allocated_mb': torch.cuda.max_memory_allocated() / 1024 / 1024,
        }
    return {'allocated_mb': 0, 'cached_mb': 0, 'max_allocated_mb': 0}

def analyze_model_memory(config):
    """分析模型本身的内存使用"""
    print("=== 模型内存分析 ===")
    
    # 创建模型
    model = LlamaEncoder(config)
    
    # 统计参数
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    # 参数内存 (float32 = 4 bytes)
    param_memory_mb = total_params * 4 / 1024 / 1024
    
    print(f"总参数量: {total_params:,}")
    print(f"可训练参数量: {trainable_params:,}")
    print(f"参数内存占用: {param_memory_mb:.2f} MB")
    
    # 各模块参数分析
    conv_params = sum(p.numel() for p in model.conv.parameters())
    llama_params = sum(p.numel() for p in model.llama.parameters())
    final_proj_params = sum(p.numel() for p in model.final_projection.parameters())
    
    print(f"\n模块参数分布:")
    print(f"  卷积层: {conv_params:,} ({conv_params*4/1024/1024:.2f} MB)")
    print(f"  LLaMA模型: {llama_params:,} ({llama_params*4/1024/1024:.2f} MB)")
    print(f"  最终投影: {final_proj_params:,} ({final_proj_params*4/1024/1024:.2f} MB)")
    
    return model, param_memory_mb

def analyze_forward_memory(model, batch_sizes, device='cpu'):
    """分析前向传播的内存使用"""
    print(f"\n=== 前向传播内存分析 (设备: {device}) ===")
    
    model = model.to(device)
    
    for batch_size in batch_sizes:
        print(f"\nBatch Size: {batch_size}")
        
        # 清理GPU缓存
        if device != 'cpu':
            torch.cuda.empty_cache()
            torch.cuda.reset_peak_memory_stats()
        
        # 记录初始内存
        initial_memory = get_gpu_memory() if device != 'cpu' else get_memory_usage()
        
        try:
            # 创建输入数据 (模拟对比学习的2个视图)
            input_data = torch.randn(batch_size * 2, 1024, device=device)
            
            # 记录输入后内存
            input_memory = get_gpu_memory() if device != 'cpu' else get_memory_usage()
            
            # 前向传播
            with torch.no_grad():
                features = model(input_data)
            
            # 记录前向传播后内存
            forward_memory = get_gpu_memory() if device != 'cpu' else get_memory_usage()
            
            # 计算相似度矩阵 (SimCLR)
            features_norm = F.normalize(features, dim=1)
            similarity_matrix = torch.matmul(features_norm, features_norm.T)
            
            # 记录相似度计算后内存
            similarity_memory = get_gpu_memory() if device != 'cpu' else get_memory_usage()
            
            if device != 'cpu':
                print(f"  初始GPU内存: {initial_memory['allocated_mb']:.2f} MB")
                print(f"  输入后GPU内存: {input_memory['allocated_mb']:.2f} MB")
                print(f"  前向传播后GPU内存: {forward_memory['allocated_mb']:.2f} MB")
                print(f"  相似度计算后GPU内存: {similarity_memory['allocated_mb']:.2f} MB")
                print(f"  峰值GPU内存: {torch.cuda.max_memory_allocated()/1024/1024:.2f} MB")
            else:
                print(f"  初始内存: {initial_memory['rss_mb']:.2f} MB")
                print(f"  输入后内存: {input_memory['rss_mb']:.2f} MB")
                print(f"  前向传播后内存: {forward_memory['rss_mb']:.2f} MB")
                print(f"  相似度计算后内存: {similarity_memory['rss_mb']:.2f} MB")
            
            # 计算各部分内存增长
            input_size_mb = (batch_size * 2 * 1024 * 4) / 1024 / 1024
            similarity_size_mb = ((batch_size * 2) ** 2 * 4) / 1024 / 1024
            
            print(f"  输入数据理论大小: {input_size_mb:.2f} MB")
            print(f"  相似度矩阵理论大小: {similarity_size_mb:.2f} MB")
            
        except RuntimeError as e:
            if "out of memory" in str(e):
                print(f"  ❌ OOM! 无法处理 batch_size={batch_size}")
            else:
                print(f"  ❌ 错误: {e}")
        except Exception as e:
            print(f"  ❌ 未知错误: {e}")

def analyze_training_memory(model, batch_size, device='cpu'):
    """分析训练时的内存使用（包括梯度）"""
    print(f"\n=== 训练内存分析 (Batch Size: {batch_size}, 设备: {device}) ===")
    
    model = model.to(device)
    model.train()
    
    # 创建优化器
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    
    if device != 'cpu':
        torch.cuda.empty_cache()
        torch.cuda.reset_peak_memory_stats()
    
    initial_memory = get_gpu_memory() if device != 'cpu' else get_memory_usage()
    
    try:
        # 创建输入和标签
        input_data = torch.randn(batch_size * 2, 1024, device=device)
        
        # 前向传播
        features = model(input_data)
        
        forward_memory = get_gpu_memory() if device != 'cpu' else get_memory_usage()
        
        # 计算损失 (简化的对比学习损失)
        features_norm = F.normalize(features, dim=1)
        similarity_matrix = torch.matmul(features_norm, features_norm.T)
        
        # 创建标签
        labels = torch.cat([torch.arange(batch_size) for i in range(2)], dim=0)
        labels = (labels.unsqueeze(0) == labels.unsqueeze(1)).float().to(device)
        
        # 简化损失计算
        loss = F.mse_loss(similarity_matrix, labels)
        
        loss_memory = get_gpu_memory() if device != 'cpu' else get_memory_usage()
        
        # 反向传播
        optimizer.zero_grad()
        loss.backward()
        
        backward_memory = get_gpu_memory() if device != 'cpu' else get_memory_usage()
        
        # 优化器步骤
        optimizer.step()
        
        optimizer_memory = get_gpu_memory() if device != 'cpu' else get_memory_usage()
        
        if device != 'cpu':
            print(f"  初始GPU内存: {initial_memory['allocated_mb']:.2f} MB")
            print(f"  前向传播后: {forward_memory['allocated_mb']:.2f} MB")
            print(f"  损失计算后: {loss_memory['allocated_mb']:.2f} MB")
            print(f"  反向传播后: {backward_memory['allocated_mb']:.2f} MB")
            print(f"  优化器步骤后: {optimizer_memory['allocated_mb']:.2f} MB")
            print(f"  峰值GPU内存: {torch.cuda.max_memory_allocated()/1024/1024:.2f} MB")
        else:
            print(f"  初始内存: {initial_memory['rss_mb']:.2f} MB")
            print(f"  前向传播后: {forward_memory['rss_mb']:.2f} MB")
            print(f"  损失计算后: {loss_memory['rss_mb']:.2f} MB")
            print(f"  反向传播后: {backward_memory['rss_mb']:.2f} MB")
            print(f"  优化器步骤后: {optimizer_memory['rss_mb']:.2f} MB")
        
        print(f"  ✅ 成功完成训练步骤")
        
    except RuntimeError as e:
        if "out of memory" in str(e):
            print(f"  ❌ OOM! 训练时内存不足")
        else:
            print(f"  ❌ 训练错误: {e}")
    except Exception as e:
        print(f"  ❌ 未知错误: {e}")

def main():
    print("LlamaEncoder + SimCLR 内存分析")
    print("=" * 50)
    
    # 配置
    config = LlamaEncoderConfig(
        input_dim=1024,
        conv_kernel_size=32,
        conv_output_channels=32,
        out_dim=128,
        num_hidden_layers=6,  # 原始配置
        num_attention_heads=8,
        intermediate_size=256
    )
    
    # 分析模型内存
    model, param_memory = analyze_model_memory(config)
    
    # 测试不同batch size的前向传播
    batch_sizes = [1, 2, 4, 8, 16, 32, 64, 128, 256]
    
    # CPU分析
    analyze_forward_memory(model, batch_sizes, device='cpu')
    
    # GPU分析 (如果可用)
    if torch.cuda.is_available():
        print(f"\nGPU设备: {torch.cuda.get_device_name()}")
        print(f"GPU总内存: {torch.cuda.get_device_properties(0).total_memory / 1024 / 1024:.0f} MB")
        analyze_forward_memory(model, batch_sizes, device='cuda')
        
        # 训练内存分析
        for bs in [1, 2, 4, 8, 16]:
            analyze_training_memory(model, bs, device='cuda')
    else:
        print("\n⚠️  CUDA不可用，跳过GPU分析")
    
    print("\n=== 总结 ===")
    print(f"模型参数内存: {param_memory:.2f} MB")
    print("建议:")
    print("1. 减少Transformer层数 (6→2-3)")
    print("2. 减少注意力头数 (8→4)")
    print("3. 减少FFN维度 (256→128)")
    print("4. 使用梯度累积代替大batch")
    print("5. 启用混合精度训练")

if __name__ == "__main__":
    main()
