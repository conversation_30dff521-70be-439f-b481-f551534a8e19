import os
import numpy as np
from scipy import linalg
import torch
import torchvision.transforms as TF
from torch.utils.data import Dataset, DataLoader
from PIL import Image
import torch.nn as nn
from torchvision import models
# 配置参数
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
batch_size = 32
image_size = 32  # Inception v3输入尺寸
import json

class ResNetSimCLR(nn.Module):
    def __init__(self, base_model, out_dim=128):
        super(ResNetSimCLR, self).__init__()
        self.resnet_dict = {
            "resnet18": models.resnet18(pretrained=False, num_classes=out_dim),
            "resnet50": models.resnet50(pretrained=False, num_classes=out_dim)
        }
        self.backbone = self._get_basemodel(base_model)
        # 获取特征维度（在分类层之前）
        if hasattr(self.backbone, 'fc'):
            self.feature_dim = self.backbone.fc.in_features
            # 移除分类层，用Identity替换
            self.backbone.fc = nn.Identity()
    def _get_basemodel(self, model_name):
        model = self.resnet_dict.get(model_name)
        if model is None:
            raise ValueError(f"Invalid backbone: {model_name}. Choose from {list(self.resnet_dict.keys())}")
        return model
    def forward(self, x):
        # 返回特征表示而不是分类输出
        features = self.backbone(x)
        return features


def load_pretrained_weights(model, pretrained_path):
    if pretrained_path is None:
        print("No pretrained weights found, training from scratch.")
        return model
    checkpoint = torch.load(pretrained_path, map_location='cpu')

    # 处理不同格式的checkpoint
    if 'model_state_dict' in checkpoint:
        state_dict = checkpoint['model_state_dict']
    elif 'state_dict' in checkpoint:
        state_dict = checkpoint['state_dict']
    elif 'model' in checkpoint:
        state_dict = checkpoint['model']
    else:
        state_dict = checkpoint

    # 移除可能存在的module.前缀 (如果是DataParallel保存的)
    state_dict = {k.replace('encoder.', ''): v for k, v in state_dict.items()}
    state_dict = {k.replace('module.', ''): v for k, v in state_dict.items()}
    # 不加载分类头的权重
    filtered_state_dict = {k: v for k, v in state_dict.items() if not k.startswith('classifier')}
    # 严格加载匹配的参数
    msg = model.load_state_dict(filtered_state_dict, strict=False)
    print(f'Missing keys: {msg.missing_keys}')
    print(f'Unexpected keys: {msg.unexpected_keys}')
    return model
# 准备图像转换
transform = TF.Compose([
    TF.Resize(image_size),
    TF.ToTensor(),
])

# 自定义图像数据集类
class ImageDataset(Dataset):
    def __init__(self, img_dir):
        self.img_dir = img_dir
        self.img_paths = [os.path.join(img_dir, f) for f in os.listdir(img_dir)
                          if os.path.isfile(os.path.join(img_dir, f)) and f.lower().endswith(('png', 'jpg', 'jpeg'))]
    def __len__(self):
        return len(self.img_paths)
    def __getitem__(self, idx):
        img_path = self.img_paths[idx]
        img = Image.open(img_path).convert('RGB')
        return transform(img)

# 禁用梯度计算
@torch.no_grad()
def get_activations(data_loader, model):
    """计算所有图像的特征向量"""
    activations = []
    for batch in data_loader:
        batch = batch.to(device)

        # Inception v3的辅助输出
        pred = model(batch)
        if isinstance(pred, tuple):
            pred = pred[0]  # 使用主要输出

        # 展平并存储
        activations.append(pred.cpu())

    return torch.cat(activations, dim=0)


# 计算特征统计量
def calculate_statistics(activations):
    """计算特征矩阵的均值和协方差"""
    mu = activations.mean(dim=0)
    sigma = activations.T.cov()
    return mu.numpy(), sigma.numpy()


# 计算FID
def calculate_fid(mu1, sigma1, mu2, sigma2):
    """计算两个分布间的Fréchet距离"""
    # 差异平方和
    diff = mu1 - mu2
    cov_mean = linalg.sqrtm(sigma1.dot(sigma2))

    # 检查虚部是否小到可以忽略
    if np.iscomplexobj(cov_mean):
        cov_mean = cov_mean.real

    # 计算FID分数
    fid = diff.dot(diff) + np.trace(sigma1 + sigma2 - 2 * cov_mean)
    return fid


# 在文件中添加以下代码到适当位置，建议放在 calculate_fid 函数之后，main 函数之前

def calculate_theoretical_fid(real_dataset, model, num_samples=None, max_samples=500):
    """
    计算理论FID，即真实数据集自身的FID分数
    通过将真实数据集分成两部分并计算它们之间的FID来实现

    Args:
        real_dataset: 真实图像数据集
        model: 特征提取模型
        num_samples: 每部分的样本数量，默认为数据集大小的10%
        max_samples: 最大样本数量限制

    Returns:
        theoretical_fid: 理论FID分数
    """
    import random

    total_size = len(real_dataset)

    # 如果没有指定样本数，则使用数据集大小的10%，但不超过最大限制
    if num_samples is None:
        num_samples = min(int(total_size * 0.1), max_samples)
    else:
        num_samples = min(num_samples, max_samples)

    # 确保不会超过数据集大小的一半
    num_samples = min(num_samples, total_size // 2)

    if num_samples <= 0:
        raise ValueError("数据集太小，无法计算理论FID")

    # 随机选择索引并分成两部分
    indices = list(range(total_size))
    random.shuffle(indices)

    part1_indices = indices[:num_samples]
    part2_indices = indices[num_samples:2 * num_samples]

    # 创建子数据集
    part1_dataset = torch.utils.data.Subset(real_dataset, part1_indices)
    part2_dataset = torch.utils.data.Subset(real_dataset, part2_indices)

    # 创建数据加载器
    part1_loader = DataLoader(part1_dataset, batch_size=batch_size, shuffle=False, num_workers=4)
    part2_loader = DataLoader(part2_dataset, batch_size=batch_size, shuffle=False, num_workers=4)

    # 提取特征
    print(f'提取第一部分图像特征 (样本数: {len(part1_dataset)})...')
    part1_activations = get_activations(part1_loader, model)
    mu1, sigma1 = calculate_statistics(part1_activations)

    print(f'提取第二部分图像特征 (样本数: {len(part2_dataset)})...')
    part2_activations = get_activations(part2_loader, model)
    mu2, sigma2 = calculate_statistics(part2_activations)

    # 计算FID
    theoretical_fid = calculate_fid(mu1, sigma1, mu2, sigma2)

    return theoretical_fid

def main():
    # 加载类别映射字典
    class_mapping_path = './NID_class_mapping.json'
    if os.path.exists(class_mapping_path):
        with open(class_mapping_path, 'r') as f:
            class_mapping = json.load(f)
        print(f"加载类别映射: {class_mapping}")
    else:
        raise FileNotFoundError(f"找不到类别映射文件: {class_mapping_path}")

    # 获取所有类别名称
    class_names = list(class_mapping.keys())
    print(f"处理以下类别: {class_names}")

    base_real_dir = './Filter'
    base_gen_dir = './source3'

    # 加载预训练模型
    model = ResNetSimCLR("resnet18")
    model = load_pretrained_weights(model, pretrained_path='./FID_pretrained.pth.tar')
    model.eval()
    model.to(device)

    # 存储各类别的FID分数
    fid_scores = {}
    theoretical_fid_scores = {}

    # 循环检测所有类别
    for class_name in class_names:
        print(f'\n处理类别 {class_name}...')

        # 使用类别名称作为目录名
        real_dir = os.path.join(base_real_dir, class_name)
        gen_dir = os.path.join(base_gen_dir, class_name)

        # 检查目录是否存在
        if not os.path.exists(real_dir) or not os.path.exists(gen_dir):
            print(f"类别 {class_name} 的目录不存在，跳过...")
            print(f"  真实数据目录: {real_dir} - {'存在' if os.path.exists(real_dir) else '不存在'}")
            print(f"  生成数据目录: {gen_dir} - {'存在' if os.path.exists(gen_dir) else '不存在'}")
            continue

        print('加载数据集...')
        real_dataset = ImageDataset(real_dir)
        gen_dataset = ImageDataset(gen_dir)

        # 确保数据集不为空
        if len(real_dataset) == 0 or len(gen_dataset) == 0:
            print(f"类别 {class_name} 的数据集为空，跳过...")
            print(f"  真实图像数量: {len(real_dataset)}")
            print(f"  生成图像数量: {len(gen_dataset)}")
            continue

        print(f"真实图像数量: {len(real_dataset)}")
        print(f"生成图像数量: {len(gen_dataset)}")

        # 创建数据加载器
        real_loader = DataLoader(real_dataset, batch_size=batch_size, shuffle=False, num_workers=4)
        gen_loader = DataLoader(gen_dataset, batch_size=batch_size, shuffle=False, num_workers=4)

        print('提取真实图像特征...')
        real_activations = get_activations(real_loader, model)
        mu_real, sigma_real = calculate_statistics(real_activations)

        print('提取生成图像特征...')
        gen_activations = get_activations(gen_loader, model)
        mu_gen, sigma_gen = calculate_statistics(gen_activations)

        print('计算FID...')
        fid_value = calculate_fid(mu_real, sigma_real, mu_gen, sigma_gen)

        fid_scores[class_name] = fid_value

        # 计算该类别的理论FID
        print('计算理论FID...')
        try:
            # 计算抽样数量：20%但不超过500张
            sample_size = min(int(len(real_dataset) * 0.2), 500)
            if sample_size > 0:
                theoretical_fid = calculate_theoretical_fid(
                    real_dataset, model, num_samples=sample_size, max_samples=500
                )
                theoretical_fid_scores[class_name] = theoretical_fid
                print(f"类别 {class_name} 的理论FID分数: {theoretical_fid:.2f}")
            else:
                print(f"类别 {class_name} 的数据集太小，无法计算理论FID")
        except Exception as e:
            print(f"计算类别 {class_name} 的理论FID时出错: {e}")

        print('\n' + '=' * 50)
        print(f"类别 {class_name} 的FID分数: {fid_value:.2f}")
        print('=' * 50)

    # 输出所有类别的统计结果
    print('\n\n所有类别的FID统计:')
    print('-' * 50)
    for class_name, fid_score in fid_scores.items():
        theoretical_score = theoretical_fid_scores.get(class_name, "N/A")
        print(f"类别 {class_name:12s}: FID={fid_score:6.2f}, 理论FID={theoretical_score}")

    # 计算平均FID
    if fid_scores:
        avg_fid = sum(fid_scores.values()) / len(fid_scores)
        print(f"\n平均FID分数: {avg_fid:.2f}")

        # 分类统计
        print("\nFID分类统计:")
        excellent = sum(1 for score in fid_scores.values() if score < 10)
        good = sum(1 for score in fid_scores.values() if 10 <= score < 30)
        medium = sum(1 for score in fid_scores.values() if 30 <= score < 50)
        poor = sum(1 for score in fid_scores.values() if score >= 50)

        print(f"- 优秀 (<10): {excellent} 类")
        print(f"- 良好 (10-30): {good} 类")
        print(f"- 中等 (30-50): {medium} 类")
        print(f"- 较差 (>50): {poor} 类")

    # 计算平均理论FID
    if theoretical_fid_scores:
        avg_theoretical_fid = sum(theoretical_fid_scores.values()) / len(theoretical_fid_scores)
        print(f"\n平均理论FID分数: {avg_theoretical_fid:.2f}")


if __name__ == '__main__':
    main()