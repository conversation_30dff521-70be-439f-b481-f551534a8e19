# -*- coding: utf-8 -*-
"""
序列编码器模型
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Tuple
import math

from ..config.sequence_config import SequenceConfig


class SequenceEncoder(nn.Module):
    """序列编码器，将时序数据编码为特征向量"""
    
    def __init__(self, config: SequenceConfig):
        """
        初始化序列编码器
        
        Args:
            config: 序列配置对象
        """
        super(SequenceEncoder, self).__init__()
        self.config = config
        
        # 输入特征维度
        self.input_dim = config.get_total_feature_dim()
        
        # 创建编码器
        if config.encoder_type == 'transformer':
            self.encoder = self._create_transformer_encoder()
        elif config.encoder_type == 'lstm':
            self.encoder = self._create_lstm_encoder()
        elif config.encoder_type == 'gru':
            self.encoder = self._create_gru_encoder()
        elif config.encoder_type == 'tcn':
            self.encoder = self._create_tcn_encoder()
        else:
            raise ValueError(f"不支持的编码器类型: {config.encoder_type}")
        
        # 投影头
        self.projection_head = self._create_projection_head()
        
        # Dropout层
        self.dropout = nn.Dropout(config.dropout_rate)
    
    def _create_transformer_encoder(self) -> nn.Module:
        """创建Transformer编码器"""
        config = self.config.transformer_config
        
        # 输入投影层
        input_projection = nn.Linear(self.input_dim, config['hidden_dim'])
        
        # 位置编码
        position_encoding = PositionalEncoding(
            config['hidden_dim'], 
            max_len=self.config.max_sequence_length
        )
        
        # Transformer编码器层
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=config['hidden_dim'],
            nhead=config['num_heads'],
            dim_feedforward=config['feedforward_dim'],
            dropout=config['dropout'],
            activation=config['activation'],
            batch_first=True
        )
        
        transformer_encoder = nn.TransformerEncoder(
            encoder_layer,
            num_layers=config['num_layers']
        )
        
        return nn.ModuleDict({
            'input_projection': input_projection,
            'position_encoding': position_encoding,
            'transformer': transformer_encoder
        })
    
    def _create_lstm_encoder(self) -> nn.Module:
        """创建LSTM编码器"""
        config = self.config.rnn_config
        
        lstm = nn.LSTM(
            input_size=self.input_dim,
            hidden_size=config['hidden_size'],
            num_layers=config['num_layers'],
            batch_first=True,
            dropout=config['dropout'] if config['num_layers'] > 1 else 0,
            bidirectional=config['bidirectional']
        )
        
        return lstm
    
    def _create_gru_encoder(self) -> nn.Module:
        """创建GRU编码器"""
        config = self.config.rnn_config
        
        gru = nn.GRU(
            input_size=self.input_dim,
            hidden_size=config['hidden_size'],
            num_layers=config['num_layers'],
            batch_first=True,
            dropout=config['dropout'] if config['num_layers'] > 1 else 0,
            bidirectional=config['bidirectional']
        )
        
        return gru
    
    def _create_tcn_encoder(self) -> nn.Module:
        """创建TCN编码器"""
        config = self.config.tcn_config
        
        return TemporalConvNet(
            num_inputs=self.input_dim,
            num_channels=config['num_channels'],
            kernel_size=config['kernel_size'],
            dropout=config['dropout']
        )
    
    def _create_projection_head(self) -> nn.Module:
        """创建投影头"""
        # 根据编码器类型确定输入维度
        if self.config.encoder_type == 'transformer':
            input_dim = self.config.transformer_config['hidden_dim']
        elif self.config.encoder_type in ['lstm', 'gru']:
            hidden_size = self.config.rnn_config['hidden_size']
            multiplier = 2 if self.config.rnn_config['bidirectional'] else 1
            input_dim = hidden_size * multiplier
        elif self.config.encoder_type == 'tcn':
            input_dim = self.config.tcn_config['num_channels'][-1]
        else:
            input_dim = 512  # 默认值
        
        return nn.Sequential(
            nn.Linear(input_dim, input_dim // 2),
            nn.ReLU(inplace=True),
            nn.Dropout(self.config.dropout_rate),
            nn.Linear(input_dim // 2, self.config.output_feature_dim),
            nn.ReLU(inplace=True)
        )
    
    def forward(self, x: torch.Tensor, 
                mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            x: 输入序列张量 (batch_size, seq_len, feature_dim)
            mask: 序列掩码 (batch_size, seq_len)
            
        Returns:
            (encoder_features, projected_features): 编码器特征和投影特征
        """
        batch_size, seq_len, _ = x.shape
        
        # 根据编码器类型进行前向传播
        if self.config.encoder_type == 'transformer':
            encoder_features = self._forward_transformer(x, mask)
        elif self.config.encoder_type == 'lstm':
            encoder_features = self._forward_lstm(x, mask)
        elif self.config.encoder_type == 'gru':
            encoder_features = self._forward_gru(x, mask)
        elif self.config.encoder_type == 'tcn':
            encoder_features = self._forward_tcn(x)
        else:
            raise ValueError(f"不支持的编码器类型: {self.config.encoder_type}")
        
        # 应用dropout
        encoder_features = self.dropout(encoder_features)
        
        # 投影到目标维度
        projected_features = self.projection_head(encoder_features)
        
        # L2归一化
        projected_features = F.normalize(projected_features, p=2, dim=1)
        
        return encoder_features, projected_features
    
    def _forward_transformer(self, x: torch.Tensor, 
                           mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """Transformer前向传播"""
        # 输入投影
        x = self.encoder['input_projection'](x)
        
        # 位置编码
        x = self.encoder['position_encoding'](x)
        
        # 创建注意力掩码
        if mask is not None:
            # 将padding位置设为True（被掩码）
            attn_mask = ~mask.bool()
        else:
            attn_mask = None
        
        # Transformer编码
        encoded = self.encoder['transformer'](x, src_key_padding_mask=attn_mask)
        
        # 全局平均池化或使用CLS token
        if self.config.use_attention:
            # 使用注意力池化
            pooled = self._attention_pooling(encoded, mask)
        else:
            # 简单平均池化
            if mask is not None:
                # 考虑掩码的平均池化
                mask_expanded = mask.unsqueeze(-1).expand_as(encoded)
                encoded_masked = encoded * mask_expanded
                pooled = encoded_masked.sum(dim=1) / mask.sum(dim=1, keepdim=True)
            else:
                pooled = encoded.mean(dim=1)
        
        return pooled
    
    def _forward_lstm(self, x: torch.Tensor, 
                     mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """LSTM前向传播"""
        # 如果有掩码，使用pack_padded_sequence
        if mask is not None:
            lengths = mask.sum(dim=1).cpu()
            x = nn.utils.rnn.pack_padded_sequence(
                x, lengths, batch_first=True, enforce_sorted=False
            )
        
        # LSTM编码
        output, (hidden, _) = self.encoder(x)
        
        # 如果使用了pack_padded_sequence，需要unpack
        if mask is not None:
            output, _ = nn.utils.rnn.pad_packed_sequence(output, batch_first=True)
        
        # 使用最后一个时间步的隐藏状态
        if self.config.rnn_config['bidirectional']:
            # 双向LSTM：连接前向和后向的最后隐藏状态
            hidden = torch.cat([hidden[-2], hidden[-1]], dim=1)
        else:
            hidden = hidden[-1]
        
        return hidden
    
    def _forward_gru(self, x: torch.Tensor, 
                    mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """GRU前向传播"""
        # 类似LSTM的处理
        if mask is not None:
            lengths = mask.sum(dim=1).cpu()
            x = nn.utils.rnn.pack_padded_sequence(
                x, lengths, batch_first=True, enforce_sorted=False
            )
        
        output, hidden = self.encoder(x)
        
        if mask is not None:
            output, _ = nn.utils.rnn.pad_packed_sequence(output, batch_first=True)
        
        if self.config.rnn_config['bidirectional']:
            hidden = torch.cat([hidden[-2], hidden[-1]], dim=1)
        else:
            hidden = hidden[-1]
        
        return hidden
    
    def _forward_tcn(self, x: torch.Tensor) -> torch.Tensor:
        """TCN前向传播"""
        # TCN期望输入为 (batch_size, channels, seq_len)
        x = x.transpose(1, 2)
        
        # TCN编码
        encoded = self.encoder(x)
        
        # 全局平均池化
        pooled = encoded.mean(dim=2)
        
        return pooled
    
    def _attention_pooling(self, encoded: torch.Tensor, 
                          mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """注意力池化"""
        # 计算注意力权重
        attention_weights = torch.tanh(encoded).mean(dim=-1)
        
        if mask is not None:
            # 将padding位置的权重设为很小的值
            attention_weights = attention_weights.masked_fill(~mask.bool(), -1e9)
        
        # Softmax归一化
        attention_weights = F.softmax(attention_weights, dim=1)
        
        # 加权平均
        pooled = (encoded * attention_weights.unsqueeze(-1)).sum(dim=1)
        
        return pooled
    
    def get_features(self, x: torch.Tensor, 
                    mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        仅获取投影后的特征（用于推理）
        
        Args:
            x: 输入序列张量
            mask: 序列掩码
            
        Returns:
            投影特征
        """
        _, projected_features = self.forward(x, mask)
        return projected_features


class PositionalEncoding(nn.Module):
    """位置编码"""
    
    def __init__(self, d_model: int, max_len: int = 5000):
        """
        初始化位置编码
        
        Args:
            d_model: 模型维度
            max_len: 最大序列长度
        """
        super(PositionalEncoding, self).__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        return x + self.pe[:x.size(1), :].transpose(0, 1)


class TemporalBlock(nn.Module):
    """TCN的时间卷积块"""
    
    def __init__(self, n_inputs: int, n_outputs: int, kernel_size: int, 
                 stride: int, dilation: int, padding: int, dropout: float = 0.2):
        """
        初始化时间卷积块
        
        Args:
            n_inputs: 输入通道数
            n_outputs: 输出通道数
            kernel_size: 卷积核大小
            stride: 步长
            dilation: 膨胀率
            padding: 填充
            dropout: Dropout率
        """
        super(TemporalBlock, self).__init__()
        
        self.conv1 = nn.Conv1d(n_inputs, n_outputs, kernel_size,
                              stride=stride, padding=padding, dilation=dilation)
        self.chomp1 = Chomp1d(padding)
        self.relu1 = nn.ReLU()
        self.dropout1 = nn.Dropout(dropout)
        
        self.conv2 = nn.Conv1d(n_outputs, n_outputs, kernel_size,
                              stride=stride, padding=padding, dilation=dilation)
        self.chomp2 = Chomp1d(padding)
        self.relu2 = nn.ReLU()
        self.dropout2 = nn.Dropout(dropout)
        
        self.net = nn.Sequential(self.conv1, self.chomp1, self.relu1, self.dropout1,
                                self.conv2, self.chomp2, self.relu2, self.dropout2)
        
        self.downsample = nn.Conv1d(n_inputs, n_outputs, 1) if n_inputs != n_outputs else None
        self.relu = nn.ReLU()
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        out = self.net(x)
        res = x if self.downsample is None else self.downsample(x)
        return self.relu(out + res)


class Chomp1d(nn.Module):
    """移除卷积后多余的填充"""
    
    def __init__(self, chomp_size: int):
        super(Chomp1d, self).__init__()
        self.chomp_size = chomp_size
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return x[:, :, :-self.chomp_size].contiguous()


class TemporalConvNet(nn.Module):
    """时间卷积网络(TCN)"""
    
    def __init__(self, num_inputs: int, num_channels: list, 
                 kernel_size: int = 2, dropout: float = 0.2):
        """
        初始化TCN
        
        Args:
            num_inputs: 输入特征数
            num_channels: 每层的通道数列表
            kernel_size: 卷积核大小
            dropout: Dropout率
        """
        super(TemporalConvNet, self).__init__()
        
        layers = []
        num_levels = len(num_channels)
        
        for i in range(num_levels):
            dilation_size = 2 ** i
            in_channels = num_inputs if i == 0 else num_channels[i-1]
            out_channels = num_channels[i]
            
            layers += [TemporalBlock(in_channels, out_channels, kernel_size,
                                   stride=1, dilation=dilation_size,
                                   padding=(kernel_size-1) * dilation_size,
                                   dropout=dropout)]
        
        self.network = nn.Sequential(*layers)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        return self.network(x)
