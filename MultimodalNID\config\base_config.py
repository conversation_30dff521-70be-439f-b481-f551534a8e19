# -*- coding: utf-8 -*-
"""
基础配置类
"""

import os
from pathlib import Path
from dataclasses import dataclass
from typing import Optional, List, Dict, Any


@dataclass
class BaseConfig:
    """基础配置类，包含所有模块共享的配置参数"""
    
    # 项目路径配置
    project_root: str = "."
    data_root: str = "./data"
    output_root: str = "./output"
    log_dir: str = "./logs"
    checkpoint_dir: str = "./checkpoints"
    
    # 数据集配置
    dataset_name: str = "network_traffic"
    train_split: float = 0.7
    val_split: float = 0.15
    test_split: float = 0.15
    
    # 设备配置
    device: str = "cuda"
    num_workers: int = 4
    pin_memory: bool = True
    
    # 随机种子
    seed: int = 42
    
    # 日志配置
    log_level: str = "INFO"
    log_format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # 实验配置
    experiment_name: str = "multimodal_nid"
    tags: List[str] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.tags is None:
            self.tags = []
            
        # 确保路径存在
        self._create_directories()
    
    def _create_directories(self):
        """创建必要的目录"""
        dirs = [
            self.output_root,
            self.log_dir,
            self.checkpoint_dir
        ]
        
        for dir_path in dirs:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            field.name: getattr(self, field.name)
            for field in self.__dataclass_fields__.values()
        }
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'BaseConfig':
        """从字典创建配置对象"""
        return cls(**config_dict)
    
    def update(self, **kwargs):
        """更新配置参数"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
            else:
                raise ValueError(f"Unknown configuration parameter: {key}")
    
    def get_full_path(self, relative_path: str) -> str:
        """获取相对于项目根目录的完整路径"""
        return os.path.join(self.project_root, relative_path)
    
    def validate(self) -> bool:
        """验证配置参数的有效性"""
        # 验证分割比例
        total_split = self.train_split + self.val_split + self.test_split
        if abs(total_split - 1.0) > 1e-6:
            raise ValueError(f"数据集分割比例总和必须为1.0，当前为{total_split}")
        
        # 验证设备配置
        if self.device not in ["cpu", "cuda", "auto"]:
            raise ValueError(f"不支持的设备类型: {self.device}")
        
        # 验证工作进程数
        if self.num_workers < 0:
            raise ValueError("工作进程数必须为非负整数")
        
        return True
