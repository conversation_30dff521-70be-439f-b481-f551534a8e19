# -*- coding: utf-8 -*-
import torch
import torch.nn as nn
import torch.optim as optim
import torchvision.transforms as transforms
from torch.utils.data import DataLoader
from models.resnet_simclr import ResNetSimCLR
from data.fewshot_dataset import FewShotDataset
import os
from torch.utils.tensorboard import SummaryWriter
from tqdm import tqdm

class MetaBaseline(nn.Module):
    """A simple wrapper that forwards inputs through the frozen encoder.

    Meta-Baseline only changes the way we compute the classification logits
    during episodic training; the encoder itself can be any network.
    """

    def __init__(self, encoder):
        super().__init__()
        self.encoder = encoder

    def forward(self, x):
        return self.encoder(x)

def euclidean_dist(a, b):
    n = a.size(0)
    m = b.size(0)
    a = a.unsqueeze(1).expand(n, m, -1)
    b = b.unsqueeze(0).expand(n, m, -1)
    return torch.pow(a - b, 2).sum(2)

def meta_baseline_loss(support, query, n_way, k_shot, q_query, model, temperature=10.0):
    """Meta-Baseline loss: cosine similarity between query embeddings and class prototypes.

    Args:
        support (Tensor): Support images, shape [n_way * k_shot, C, H, W]
        query (Tensor): Query images, shape [n_way * q_query, C, H, W]
        n_way (int): Number of classes per episode.
        k_shot (int): Number of support samples per class.
        q_query (int): Number of query samples per class.
        model (nn.Module): Encoder network.
        temperature (float): Scaling factor for logits. Default 10.0 as in the paper.
    Returns:
        loss (Tensor), acc (Tensor)
    """

    support_emb = model(support)  # [n_way * k_shot, dim]
    query_emb = model(query)      # [n_way * q_query, dim]

    # Compute class prototypes
    prototypes = support_emb.reshape(n_way, k_shot, -1).mean(1)  # [n_way, dim]

    # Normalize prototypes and embeddings
    prototypes = nn.functional.normalize(prototypes, dim=-1)
    query_emb = nn.functional.normalize(query_emb, dim=-1)

    # Cosine similarity as logits
    logits = torch.matmul(query_emb, prototypes.t()) * temperature  # [n_way * q_query, n_way]

    # Ground-truth labels
    labels = torch.arange(n_way).unsqueeze(1).expand(n_way, q_query).reshape(-1).to(query.device)

    loss = nn.CrossEntropyLoss()(logits, labels)
    preds = torch.argmax(logits, dim=1)
    acc = (preds == labels).float().mean()

    return loss, acc

def main():
    n_way = 5
    val_n_way = 3
    k_shot = 1
    q_query = 15
    meta_batch_size = 4
    epochs = 100

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    encoder = ResNetSimCLR(base_model='resnet18', out_dim=128)
    encoder = nn.DataParallel(encoder)
    checkpoint = torch.load('./runs/Jun10_21-31-44_amax/checkpoint_9500.pth.tar', map_location=device)
    encoder.load_state_dict(checkpoint['state_dict'])
    
    model = MetaBaseline(encoder).to(device).train()

    optimizer = optim.Adam(model.parameters(), lr=1e-3)

    # TensorBoard writer (logs will be stored in ./runs_few_shot)
    writer = SummaryWriter(log_dir="runs_few_shot")

    global_step = 0

    transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                             std=[0.229, 0.224, 0.225])
    ])
    
    # You may need to adjust the following dataset paths
    dataset_base = "../../data/CTU_RGB_AL_L7_L4_few_shot"  # <- change to your dataset root
    train_root = os.path.join(dataset_base, "train")
    val_root = os.path.join(dataset_base, "val")

    trainset = FewShotDataset(
        root=train_root,
        split='train',
        n_way=n_way,
        k_shot=k_shot,
        q_query=q_query,
        episodes_per_epoch=100,
        transform=transform
    )

    # Validation episodes (optional, only if path exists)
    if os.path.exists(val_root):
        valset = FewShotDataset(
            root=val_root,
            split='val',
            n_way=val_n_way,
            k_shot=k_shot,
            q_query=q_query,
            episodes_per_epoch=600,
            transform=transform
        )
        valloader = DataLoader(
            valset,
            batch_size=meta_batch_size,
            shuffle=False,
            num_workers=4,
            pin_memory=True,
            collate_fn=lambda x: x
        )
    else:
        valloader = None  # No validation set found
    
    trainloader = DataLoader(
        trainset, 
        batch_size=meta_batch_size, 
        shuffle=True,
        num_workers=4,
        pin_memory=True,
        collate_fn=lambda x: x
    )

    def evaluate(model, dataloader, epoch_idx):
        """Evaluate model over the given dataloader (episodic) with tqdm bar."""
        if dataloader is None:
            return None, None

        model.eval()
        total_loss = 0.0
        total_acc = 0.0
        total_episodes = 0

        val_iterator = tqdm(dataloader, desc=f"Val {epoch_idx}", leave=True)

        with torch.no_grad():
            for batch in val_iterator:
                batch_loss = 0.0
                batch_acc = 0.0
                for episode in batch:
                    (support_x, _), (query_x, _) = episode
                    support_x = support_x.to(device)
                    query_x = query_x.to(device)

                    loss, acc = meta_baseline_loss(support_x, query_x, val_n_way, k_shot, q_query, model)
                    batch_loss += loss.item()
                    batch_acc += acc.item()
                    total_loss += loss.item()
                    total_acc += acc.item()
                    total_episodes += 1

                # average within batch
                if len(batch) > 0:
                    val_iterator.set_postfix({"loss": f"{batch_loss/len(batch):.4f}", "acc": f"{batch_acc/len(batch):.4f}"})

        avg_loss = total_loss / total_episodes
        avg_acc = total_acc / total_episodes
        model.train()
        return avg_loss, avg_acc

    for epoch in range(epochs):
        # tqdm progress bar for batches in an epoch
        batch_iterator = tqdm(trainloader, desc=f"Epoch {epoch}", leave=True)
        for i, batch in enumerate(batch_iterator):
            total_loss = 0.0
            total_acc = 0.0
            
            for episode in batch:
                (support_x, support_y), (query_x, query_y) = episode
                
                support_x = support_x.to(device)
                query_x = query_x.to(device)
                
                loss, acc = meta_baseline_loss(support_x, query_x, n_way, k_shot, q_query, model)
                total_loss += loss
                total_acc += acc
            
            avg_loss = total_loss / len(batch)
            avg_acc = total_acc / len(batch)
            
            optimizer.zero_grad()
            avg_loss.backward()
            optimizer.step()

            # Update tqdm postfix
            batch_iterator.set_postfix({"loss": f"{avg_loss.item():.4f}", "acc": f"{avg_acc.item():.4f}"})

            # TensorBoard logging
            writer.add_scalar('train/loss', avg_loss.item(), global_step)
            writer.add_scalar('train/acc', avg_acc.item(), global_step)
            global_step += 1

        # ---- Validation at the end of each epoch ----
        # print train summary
        print(f"[Train]  Epoch {epoch}: LastBatchLoss={avg_loss.item():.4f}, Acc={avg_acc.item():.4f}")

        val_loss, val_acc = evaluate(model, valloader, epoch)
        if val_loss is not None:
            print(f"[Validation] Epoch {epoch}: Loss={val_loss:.4f}, Acc={val_acc:.4f}")
            writer.add_scalar('val/loss', val_loss, epoch)
            writer.add_scalar('val/acc', val_acc, epoch)

    writer.close()

if __name__ == '__main__':
    main()