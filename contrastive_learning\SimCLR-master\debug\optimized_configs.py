#!/usr/bin/env python3
"""
优化配置建议
提供不同内存预算下的模型配置选项
"""

import os
import sys

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.llama_encoder import LlamaEncoderConfig

class OptimizedConfigs:
    """优化的配置类，提供不同内存预算的配置选项"""
    
    @staticmethod
    def get_tiny_config():
        """超小配置 - 适用于极低内存环境 (~2GB GPU)"""
        config = LlamaEncoderConfig(
            input_dim=512,  # 减少输入长度
            conv_kernel_size=32,
            conv_output_channels=16,  # 减少通道数
            out_dim=64,  # 减少输出维度
            num_hidden_layers=2,  # 大幅减少层数
            num_attention_heads=4,  # 减少注意力头
            intermediate_size=64,  # 大幅减少FFN维度
            max_position_embeddings=512,
        )
        config.input_dim = 512  # 手动设置
        return config
    
    @staticmethod
    def get_small_config():
        """小配置 - 适用于低内存环境 (~4GB GPU)"""
        config = LlamaEncoderConfig(
            input_dim=1024,
            conv_kernel_size=32,
            conv_output_channels=24,  # 适中减少
            out_dim=96,
            num_hidden_layers=3,  # 减少层数
            num_attention_heads=6,  # 减少注意力头
            intermediate_size=128,  # 减少FFN维度
            max_position_embeddings=1024,
        )
        config.input_dim = 1024  # 手动设置
        return config
    
    @staticmethod
    def get_medium_config():
        """中等配置 - 适用于中等内存环境 (~6-8GB GPU)"""
        config = LlamaEncoderConfig(
            input_dim=1024,
            conv_kernel_size=32,
            conv_output_channels=32,  # 保持原始
            out_dim=128,
            num_hidden_layers=4,  # 适度减少
            num_attention_heads=8,  # 保持原始
            intermediate_size=192,  # 适度减少
            max_position_embeddings=1024,
        )
        config.input_dim = 1024  # 手动设置
        return config
    
    @staticmethod
    def get_original_config():
        """原始配置 - 需要大内存环境 (~10GB+ GPU)"""
        config = LlamaEncoderConfig(
            input_dim=1024,
            conv_kernel_size=32,
            conv_output_channels=32,
            out_dim=128,
            num_hidden_layers=6,  # 原始
            num_attention_heads=8,
            intermediate_size=256,  # 原始
            max_position_embeddings=1024,
        )
        config.input_dim = 1024  # 手动设置
        return config
    
    @staticmethod
    def get_config_by_memory(gpu_memory_gb):
        """根据GPU内存大小自动选择配置"""
        if gpu_memory_gb <= 2:
            return OptimizedConfigs.get_tiny_config()
        elif gpu_memory_gb <= 4:
            return OptimizedConfigs.get_small_config()
        elif gpu_memory_gb <= 8:
            return OptimizedConfigs.get_medium_config()
        else:
            return OptimizedConfigs.get_original_config()
    
    @staticmethod
    def print_config_comparison():
        """打印不同配置的对比"""
        configs = {
            'Tiny': OptimizedConfigs.get_tiny_config(),
            'Small': OptimizedConfigs.get_small_config(),
            'Medium': OptimizedConfigs.get_medium_config(),
            'Original': OptimizedConfigs.get_original_config(),
        }
        
        print("配置对比:")
        print("-" * 80)
        print(f"{'配置':<10} {'输入长度':<8} {'通道数':<6} {'层数':<4} {'注意力头':<8} {'FFN维度':<8} {'输出维度':<8}")
        print("-" * 80)
        
        for name, config in configs.items():
            print(f"{name:<10} {config.input_dim:<8} {config.conv_output_channels:<6} "
                  f"{config.num_hidden_layers:<4} {config.num_attention_heads:<8} "
                  f"{config.intermediate_size:<8} {config.out_dim:<8}")

class TrainingOptimizations:
    """训练优化建议"""
    
    @staticmethod
    def get_memory_efficient_args():
        """内存高效的训练参数"""
        return {
            'batch_size': 4,  # 小batch size
            'gradient_accumulation_steps': 16,  # 梯度累积模拟大batch
            'fp16_precision': True,  # 混合精度
            'dataloader_num_workers': 2,  # 减少数据加载进程
            'pin_memory': False,  # 在内存紧张时关闭
        }
    
    @staticmethod
    def get_balanced_args():
        """平衡的训练参数"""
        return {
            'batch_size': 8,
            'gradient_accumulation_steps': 8,
            'fp16_precision': True,
            'dataloader_num_workers': 4,
            'pin_memory': True,
        }
    
    @staticmethod
    def get_performance_args():
        """性能优先的训练参数"""
        return {
            'batch_size': 16,
            'gradient_accumulation_steps': 4,
            'fp16_precision': True,
            'dataloader_num_workers': 8,
            'pin_memory': True,
        }

def estimate_memory_usage(config, batch_size):
    """估算内存使用量"""
    # 模型参数估算
    # 简化计算：主要考虑Transformer层的参数
    hidden_size = config.conv_output_channels
    intermediate_size = config.intermediate_size
    num_layers = config.num_hidden_layers
    num_heads = config.num_attention_heads
    
    # 每层参数估算
    attention_params = hidden_size * hidden_size * 4  # Q, K, V, O
    ffn_params = hidden_size * intermediate_size * 2  # up and down projection
    layer_params = attention_params + ffn_params
    
    total_params = layer_params * num_layers
    
    # 其他参数
    conv_params = config.conv_output_channels * config.conv_kernel_size
    final_proj_params = hidden_size * config.out_dim
    total_params += conv_params + final_proj_params
    
    # 内存估算 (float32 = 4 bytes)
    param_memory_mb = total_params * 4 / 1024 / 1024
    
    # 激活内存估算 (粗略)
    sequence_length = config.input_dim // config.conv_kernel_size
    activation_per_sample = hidden_size * sequence_length * num_layers * 2  # 前向+反向
    activation_memory_mb = activation_per_sample * batch_size * 2 * 4 / 1024 / 1024  # 2 views
    
    # 优化器内存 (Adam: 2x parameters)
    optimizer_memory_mb = param_memory_mb * 2
    
    # 相似度矩阵
    similarity_matrix_size = (batch_size * 2) ** 2 * 4 / 1024 / 1024
    
    total_memory_mb = param_memory_mb + activation_memory_mb + optimizer_memory_mb + similarity_matrix_size
    
    return {
        'parameters_mb': param_memory_mb,
        'activations_mb': activation_memory_mb,
        'optimizer_mb': optimizer_memory_mb,
        'similarity_matrix_mb': similarity_matrix_size,
        'total_mb': total_memory_mb,
        'total_params': total_params
    }

def main():
    """主函数：展示配置对比和内存估算"""
    print("LlamaEncoder 优化配置建议")
    print("=" * 50)
    
    # 配置对比
    OptimizedConfigs.print_config_comparison()
    
    print("\n内存使用估算 (batch_size=8):")
    print("-" * 60)
    print(f"{'配置':<10} {'参数(M)':<8} {'总内存(MB)':<12} {'推荐GPU':<15}")
    print("-" * 60)
    
    configs = {
        'Tiny': OptimizedConfigs.get_tiny_config(),
        'Small': OptimizedConfigs.get_small_config(),
        'Medium': OptimizedConfigs.get_medium_config(),
        'Original': OptimizedConfigs.get_original_config(),
    }
    
    gpu_recommendations = {
        'Tiny': '2GB+',
        'Small': '4GB+',
        'Medium': '6-8GB',
        'Original': '10GB+'
    }
    
    for name, config in configs.items():
        memory_info = estimate_memory_usage(config, batch_size=8)
        params_m = memory_info['total_params'] / 1_000_000
        total_mb = memory_info['total_mb']
        gpu_rec = gpu_recommendations[name]
        
        print(f"{name:<10} {params_m:<8.2f} {total_mb:<12.0f} {gpu_rec:<15}")
    
    print("\n训练参数建议:")
    print("-" * 40)
    
    optimizations = {
        'Memory Efficient': TrainingOptimizations.get_memory_efficient_args(),
        'Balanced': TrainingOptimizations.get_balanced_args(),
        'Performance': TrainingOptimizations.get_performance_args(),
    }
    
    for name, args in optimizations.items():
        print(f"\n{name}:")
        for key, value in args.items():
            print(f"  {key}: {value}")

if __name__ == "__main__":
    main()
